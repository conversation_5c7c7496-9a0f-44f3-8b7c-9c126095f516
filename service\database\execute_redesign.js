const mysql = require('mysql2/promise');
const fs = require('fs');

async function executeRedesign() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    const sqlContent = fs.readFileSync('./service/database/points_goods_redesign.sql', 'utf8');
    
    // 分割SQL语句，处理多行语句
    const statements = [];
    const lines = sqlContent.split('\n');
    let currentStatement = '';
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('--') && !trimmedLine.startsWith('USE')) {
        currentStatement += line + '\n';
        if (trimmedLine.endsWith(';')) {
          statements.push(currentStatement.trim());
          currentStatement = '';
        }
      }
    }
    
    console.log(`📝 开始执行 ${statements.length} 条SQL语句...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`执行第 ${i + 1} 条语句: ${statement.substring(0, 50)}...`);
          await connection.execute(statement);
          console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error.message);
          // 继续执行其他语句
        }
      }
    }
    
    console.log('🎉 积分商品表重新设计完成！');
    
    // 验证表是否创建成功
    console.log('\n=== 验证表创建结果 ===');
    const [tables] = await connection.execute("SHOW TABLES LIKE 'hiolabs_points_%'");
    
    console.log('📋 积分相关表列表:');
    tables.forEach(table => {
      console.log('  -', Object.values(table)[0]);
    });

    // 检查新的积分商品表结构
    try {
      const [columns] = await connection.execute('DESCRIBE hiolabs_points_goods');
      console.log('\n=== 新的积分商品表结构 ===');
      columns.forEach(col => {
        console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
      });
    } catch (error) {
      console.log('❌ 积分商品表检查失败:', error.message);
    }

    await connection.end();
    console.log('📝 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 执行失败:', error);
  }
}

executeRedesign();
