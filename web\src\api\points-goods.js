import axios from 'axios'
import api from '@/config/api'

// 设置基础URL
const baseURL = api.rootUrl

// 创建axios实例
const request = axios.create({
  baseURL: baseURL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 直接返回完整的响应数据，让调用方处理
    return response.data
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 积分商品管理API
export const pointsGoodsApi = {
  // 获取积分商品列表
  getList(params) {
    return request({
      url: '/admin/points-goods/list',
      method: 'get',
      params
    })
  },

  // 获取积分商品详情
  getDetail(id) {
    return request({
      url: `/admin/points-goods/detail`,
      method: 'get',
      params: { id }
    })
  },

  // 获取可选商品列表
  getAvailableGoods(params) {
    return request({
      url: '/admin/points-goods/available-goods',
      method: 'get',
      params
    })
  },

  // 添加积分商品
  add(data) {
    return request({
      url: '/admin/points-goods/add',
      method: 'post',
      data
    })
  },

  // 更新积分商品
  update(id, data) {
    return request({
      url: `/admin/points-goods/update`,
      method: 'post',
      data: { id, ...data }
    })
  },

  // 删除积分商品
  delete(id) {
    return request({
      url: '/admin/points-goods/delete',
      method: 'post',
      data: { id }
    })
  },

  // 批量更新状态
  batchStatus(ids, status) {
    return request({
      url: '/admin/points-goods/batch-status',
      method: 'post',
      data: { ids, status }
    })
  },

  // 获取统计数据
  getStatistics() {
    return request({
      url: '/admin/points-goods/statistics',
      method: 'get'
    })
  },

  // 获取积分商品分类
  getCategories() {
    return request({
      url: '/admin/points-goods/categories',
      method: 'get'
    })
  },

  // 获取积分兑换订单列表
  getOrders(params) {
    return request({
      url: '/admin/points-goods/orders',
      method: 'get',
      params
    })
  },

  // 获取商品的规格列表
  getGoodsProducts(goodsId) {
    return request({
      url: '/admin/points-goods/goods-products',
      method: 'get',
      params: { goods_id: goodsId }
    })
  },

  // 更新单个商品状态
  updateStatus(id, status) {
    return request({
      url: '/admin/points-goods/update-status',
      method: 'post',
      data: { id, status }
    })
  }
}

export default pointsGoodsApi
