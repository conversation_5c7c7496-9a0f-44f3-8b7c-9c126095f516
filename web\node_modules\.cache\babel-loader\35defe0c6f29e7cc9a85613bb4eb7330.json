{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1754251484081}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkjBA;EACAA;EACAC;IACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC;QAAAL;QAAAM;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAf;QACAgB;MACA;MAEA;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;MACA;QAAA;MAAA;MACA;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAtC;MACA;MAEA;IACA;IAEA;IACAuC;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;MAEA;QACA;QACA;UACAC;UACAC;QACA;QACA;MACA;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;MACA;;MAEA;MACAC;MAEA;MACAC;;MAEA;QACA;UACA;UACAC;UAEA;UACAC;;UAEA;UACA;YACA;UACA;UAEAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;;QAEA;QACAP;MACA;MACA;IACA;IAEA;IACAQ;MACA;MACA;QAAA;MAAA;IACA;EACA;EAEAC;IACAtB;IACAA;IACA;EACA;EAEAuB;IACA;MACAC;IACA;IACA;MACAA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACAC,aACA,wBACA,2BACA,wBACA,sBACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAC;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA7B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAD;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA7B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAF;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA7B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACAhC;cAAA;cAAA,OAEA;YAAA;cAAA6B;cACA7B;cAEA;gBACA;gBACAA;gBAEA;kBACA;gBACA;cACA;gBACAA;gBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;cACA;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAiC;MAAA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;MAEA;QACA;MACA;QACA;QACA;QACA;QAEA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACApD;MACA;MACA;MACA;MACA;MACA;MACAA;IACA;IAEAqD;MACA;MACA;MACA;QACAC;QACA7C;QACAC;QACA6C;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;QACAhD;QACAC;QACA6C;QACAC;MACA;IACA;IAEAE;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACA;cAAA;YAAA;cAIA;cACAC;cACAC,kBAEA;cACAC;gBAAA;cAAA;cAAA,MAEAA;gBAAA;gBAAA;cAAA;cACA;cAAA;YAAA;cAAA,MAKAA;gBAAA;gBAAA;cAAA;cACAC;cAAA,IACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAMA;cACA;cACA;cAEAC;cACAC;cACAC,oBAEA;cACAC;cAAA;gBAAA;gBAAA;kBAAA;oBAAA;sBAEAC;sBACAC,gDAEA;sBACAC;wBAAA;0BAAA;0BAAA;4BAAA;8BAAA;gCACAC,uCAEA;gCACAC;gCACAC;gCAAA;kCAAA;kCAAA;oCAAA;sCAAA;wCAAA;wCAIAC;0CACAC;0CACAC;0CACAC;0CACAC;0CACAC;0CACAC;0CACAzG;4CAAA;8CACA6D;8CACAC;8CACA4C;8CACA3C;8CACAC;8CACAE;8CACAD;4CACA;0CAAA;wCACA;wCAAA;wCAAA,OAEA;sCAAA;wCAAAZ;wCAAA,MAEAA;0CAAA;0CAAA;wCAAA;wCAAA;0CAAA,GACA;4CAAAsD;4CAAAC;0CAAA;wCAAA;sCAAA;wCAEAV;wCACA;wCAAA,MACAA;0CAAA;0CAAA;wCAAA;wCAAA;wCAAA,OACA;0CAAA;wCAAA;sCAAA;wCAAA;sCAAA;wCAAA;0CAAA,GAGA;4CAAAS;4CAAAC;4CAAAC;0CAAA;wCAAA;sCAAA;wCAAA;wCAAA;sCAAA;wCAAA;wCAAA;wCAGAX;wCACA;wCAAA,MACAY;0CAAA;0CAAA;wCAAA;wCAAA;wCAAA,OACA;0CAAA;wCAAA;sCAAA;wCAAA;sCAAA;sCAAA;wCAAA;oCAAA;kCAAA;gCAAA;gCArCAA;8BAAA;gCAAA;kCAAA;kCAAA;gCAAA;gCAAA;8BAAA;gCAAA;gCAAA;kCAAA;kCAAA;gCAAA;gCAAA;8BAAA;gCAAA;kCAAA;kCAAA;gCAAA;gCAAA;8BAAA;gCAAAA;gCAAA;gCAAA;8BAAA;gCAAA,kCA2CA;kCAAAH;kCAAAC;kCAAAC;gCAAA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA,CACA;wBAAA;0BAAA;wBAAA;sBAAA,MAEA;sBAAA;sBAAA,OACA1D;oBAAA;sBAAA4D;sBAEA;sBACAA;wBACA;wBACA;0BACAtB;wBACA;0BACAC;0BACA;4BAAA;4BACAC;0BACA;wBACA;sBACA;;sBAEA;sBAAA,MACAE;wBAAA;wBAAA;sBAAA;sBAAA;sBAAA,OACA;wBAAA;sBAAA;oBAAA;oBAAA;sBAAA;kBAAA;gBAAA;cAAA;cA5EAmB;YAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAAA;cAAA;cAAA;YAAA;cAgFA;cACA;gBACAC;gBACA;kBACAA;kBACA;oBACAzF;oBACAyF;kBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAC;gBACA;kBACAA;gBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGA1F;cACA;YAAA;cAAA;cAEA;cACA;cACA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA2F;MACA;MACA;QACArH;QACAC;QACAC;MACA;IACA;IAEAoH;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACAC;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAKA;cAAA;cAAA,OACA;gBACAC;cACA;YAAA;cAFAlE;cAIA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA7B;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAIAgG;MACA;MACA;MACA;MACA;MACA;IACA;EAAA,uEAEAC;IACA;IACA;EACA,sEAEAC;IACA;MACA;MACA;MACA;IACA;IACA;EACA,wEAGAA;IACA;MACA;MACA;MACA;IACA;IACA;EACA,oEAGAC;IACA;IACA;IACA;IACA;IAEA;IAEA;IACA;IACA;IAEA;MACA;IACA;MACA;IACA;MACA;IACA;EACA,gFAGA;IACA;MACA;IACA;MACA;IACA;EACA,8EAEA;IAAA;IACA;;IAEA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;QACA;MACA;IACA;EACA,4EAEA;IACA;MACA3E;MACA;IACA;IACA;MACAA;MACA;IACA;IACA;EACA,oEAEA;IAAA;IAAA;MAAA;QAAA;UAAA;YACA;YAAA;YAAA;YAAA,OAEA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEAxB;UAAA;YAAA;YAEA;YAAA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,4EAGA;IACA;MACA;IACA;MACA;QAAA;MAAA;IACA;EACA,4EAEA;IACA;EAAA,CACA,yEAEA;IACA;EACA,8EAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA,MACA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKA,2EACA,kEACA,UACA;cACAoG;cACAC;cACAC;YACA,EACA;UAAA;YARAC;YAAA,KAUAA;cAAA;cAAA;YAAA;YACAC;YACAC;YAAA,uCAEA;YAAA;YAAA;UAAA;YAAA;cAAA;cAAA;YAAA;YAAAC;YAAA;YAAA;YAAA,OAEA;cACAX;YACA;UAAA;YAFAlE;YAIA;cACA2E;YACA;cACAC;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEAA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA;YAIA;YACA;YACA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAGA;cACAzG;cACA;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,gFAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA,MACA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKA,4EACA,6QACA,UACA;cACAoG;cACAC;cACAC;YACA,EACA;UAAA;YARAC;YAAA,KAUAA;cAAA;cAAA;YAAA;YACA;YAAA;YAAA,OAEA;cACAI;YACA;UAAA;YAFA9E;YAIA;cACA;cACA;cACA;YACA;cACA;YACA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAGA;cACA7B;cACA;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,0EAGA4G;IACA;IACA;IACA;EACA,8EAEA;IACA;IACA;IACA;EACA,wEAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA,MACA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKA;cACAb;cACAc;YACA;UAAA;YAHAhF;YAKA;cACA;cACA;cACA;YACA;cACA;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEA7B;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,4EAGA4G;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;EACA,gFAEA;IACA;IACA;IACA;IACA;EACA,0EAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA,MACA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA,MAIA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKA;cACAb;cACAe;cACAC;YACA;UAAA;YAJAlF;YAMA;cACA;cACA;cACA;YACA;cACA;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEA7B;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,sEAGA4G;IACA;IACA;;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;EACA,0EAEA;IACA;IACA;IACA;IACA;IACA;EACA,oEAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA,MACA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA,MAIA;cAAA;cAAA;YAAA;YACA;YAAA;UAAA;YAAA;YAAA;YAAA,OAKA;cACAb;cACAiB;cACAF;cACAC;YACA;UAAA;YALAlF;YAOA;cACA;cACA;cACA;YACA;cACA;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEA7B;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,kEAGA4G;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;YAEAK;cACA;cACA;cACA;YACA;YAEAC;YAEA;cACAA;YACA;YAAA;YAAA,OAEA,iBACAA,gBACA,UACA;cACAd;cACAC;cACAC;YACA,EACA;UAAA;YARAC;YAAA,KAUAA;cAAA;cAAA;YAAA;YACA;YAAA;YAAA,OAEA;cACAR;YACA;UAAA;YAFAlE;YAIA;cACA;cACA;YACA;cACA;YACA;UAAA;YAAA;YAAA;UAAA;YAAA;YAAA;YAGA;cACA7B;cACA;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA,8DAGA4G;IACA;IACA;EACA,kFAGAO;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;EACA;AAEA", "names": ["name", "data", "statistics", "currentRounds", "current", "upcoming", "roundsList", "count", "goodsList", "loadingGoods", "showAddModal", "showDetailModal", "selectedRound", "isCreating", "newRound", "start_date", "end_date", "goods_list", "refreshTimer", "creationProgress", "total", "selectedRounds", "autoRefresh", "refreshInterval", "refreshCountdown", "countdownTimer", "loading", "showExtendModalFlag", "extendRound", "extendMinutes", "showRestartModalFlag", "restartRound", "newStartTime", "newEndTime", "showCopyModalFlag", "copyRound", "newRoundName", "copyStartTime", "copyEndTime", "computed", "canCreateRound", "console", "hasStartDate", "hasEndDate", "hasGoods", "goodsValid", "date<PERSON><PERSON>d", "isAllSelected", "isIndeterminate", "generatedRoundName", "month", "day", "hourlySlotPreview", "endDate", "currentDate", "slotStart", "slotEnd", "slots", "start", "end", "startTime", "endTime", "validSlotsCount", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "loadData", "Promise", "loadStatistics", "response", "loadCurrentRounds", "loadRoundsList", "loadGoodsList", "startAutoRefresh", "isGoodsSelected", "getSelectedGoods", "toggleGoods", "goods_id", "goods_name", "original_price", "flash_price", "discount_rate", "stock", "limit_quantity", "removeGoods", "calculateDiscountRate", "updateFlashPriceByDiscount", "selectedGoods", "formatLocalDateTime", "formatLocalDate", "getCurrentDateTime", "getCurrentDate", "openCreateModal", "formatDateTime", "year", "hour", "minute", "formatSlotTime", "isSlotInPast", "isSlotActive", "createRound", "hourlySlots", "now", "validSlots", "confirmed", "createdCount", "failedCount", "failedReasons", "batchSize", "batchEnd", "batch", "batchPromises", "globalIndex", "maxRetries", "lastError", "roundData", "round_name", "start_time", "end_time", "is_hourly_flash", "slot_index", "total_slots", "goods_image", "success", "index", "error", "retry", "batchResults", "batchStart", "message", "errorMessage", "closeModal", "viewRoundDetails", "closeRound", "confirm", "round_id", "formatTime", "dateTime", "status", "timeStr", "confirmButtonText", "cancelButtonText", "type", "confirmResult", "successCount", "failCount", "roundId", "round_ids", "round", "extend_minutes", "new_start_time", "new_end_time", "new_round_name", "statusText", "confirmMessage", "date"], "sourceRoot": "src/components/Marketing", "sources": ["FlashSaleMultiPage.vue"], "sourcesContent": ["<template>\n  <div class=\"flash-sale-multi-page\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>限时秒杀管理（多商品轮次）</h2>\n      <button @click=\"openCreateModal\" class=\"btn btn-primary\">\n        创建新轮次\n      </button>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <h3>总轮次</h3>\n        <p class=\"stat-number\">{{ statistics.totalRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>进行中</h3>\n        <p class=\"stat-number active\">{{ statistics.activeRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>即将开始</h3>\n        <p class=\"stat-number upcoming\">{{ statistics.upcomingRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>总销售额</h3>\n        <p class=\"stat-number\">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>\n      </div>\n    </div>\n\n    <!-- 当前轮次 -->\n    <div class=\"current-rounds\" v-if=\"currentRounds.current && currentRounds.current.length > 0\">\n      <h3>当前进行中的轮次</h3>\n      <div class=\"round-list\">\n        <div v-for=\"round in currentRounds.current\" :key=\"round.id\" class=\"round-item active\">\n          <div class=\"round-info\">\n            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>\n            <p>商品数量: {{ round.goods_count }}</p>\n            <p>剩余时间: {{ formatTime(round.countdown) }}</p>\n          </div>\n          <div class=\"goods-preview\">\n            <div v-for=\"goods in round.goods_list.slice(0, 3)\" :key=\"goods.id\" class=\"goods-item\">\n              <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n              <span>{{ goods.goods_name }}</span>\n              <span class=\"price\">¥{{ goods.flash_price }}</span>\n            </div>\n            <span v-if=\"round.goods_list.length > 3\" class=\"more-goods\">\n              +{{ round.goods_list.length - 3 }}个商品\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次列表 -->\n    <div class=\"rounds-table\">\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\n        <h3>轮次列表</h3>\n        <div class=\"table-controls\">\n          <div class=\"auto-refresh-control\">\n            <label class=\"form-check-label\">\n              <input\n                type=\"checkbox\"\n                v-model=\"autoRefresh\"\n                @change=\"toggleAutoRefresh\"\n                class=\"form-check-input\"\n              >\n              自动刷新 ({{ refreshInterval / 1000 }}秒)\n            </label>\n            <span v-if=\"autoRefresh\" class=\"refresh-countdown\">\n              下次刷新: {{ refreshCountdown }}秒\n            </span>\n          </div>\n          <button @click=\"refreshData\" class=\"btn btn-secondary btn-sm\">\n            <i class=\"fa fa-refresh\" :class=\"{ 'fa-spin': loading }\"></i>\n            手动刷新\n          </button>\n        </div>\n      </div>\n\n      <!-- 批量操作区域 -->\n      <div v-if=\"selectedRounds.length > 0\" class=\"batch-operations mb-3\">\n        <div class=\"alert alert-info\">\n          已选择 {{ selectedRounds.length }} 个轮次\n          <div class=\"batch-actions\">\n            <button @click=\"batchCloseRounds\" class=\"btn btn-warning btn-sm\">批量关闭</button>\n            <button @click=\"batchDeleteRounds\" class=\"btn btn-danger btn-sm\">批量删除</button>\n            <button @click=\"clearSelection\" class=\"btn btn-secondary btn-sm\">取消选择</button>\n          </div>\n        </div>\n      </div>\n\n      <table class=\"table\">\n        <thead>\n          <tr>\n            <th>\n              <input\n                type=\"checkbox\"\n                @change=\"toggleSelectAll\"\n                :checked=\"isAllSelected\"\n                :indeterminate.prop=\"isIndeterminate\"\n              >\n            </th>\n            <th>轮次编号</th>\n            <th>轮次名称</th>\n            <th>商品数量</th>\n            <th>总库存</th>\n            <th>已售出</th>\n            <th>开始时间</th>\n            <th>结束时间</th>\n            <th>状态</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n            <td>\n              <input\n                type=\"checkbox\"\n                :value=\"round.id\"\n                v-model=\"selectedRounds\"\n                @change=\"updateSelection\"\n              >\n            </td>\n            <td>#{{ round.round_number }}</td>\n            <td>{{ round.round_name }}</td>\n            <td>{{ round.goods_count }}</td>\n            <td>{{ round.total_stock }}</td>\n            <td>{{ round.total_sold }}</td>\n            <td>{{ formatDateTime(round.start_time) }}</td>\n            <td>{{ formatDateTime(round.end_time) }}</td>\n            <td>\n              <div class=\"status-info\">\n                <span :class=\"getStatusClass(round.status)\">{{ getStatusText(round.status) }}</span>\n                <div class=\"time-info\">\n                  <small v-if=\"round.status === 'upcoming'\">\n                    开始时间: {{ formatTime(round.start_time) }}\n                    <span v-if=\"getCountdown(round.start_time)\" class=\"countdown\">\n                      ({{ getCountdown(round.start_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'active'\">\n                    结束时间: {{ formatTime(round.end_time) }}\n                    <span v-if=\"getCountdown(round.end_time)\" class=\"countdown text-danger\">\n                      ({{ getCountdown(round.end_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'ended'\">\n                    已于 {{ formatTime(round.end_time) }} 结束\n                  </small>\n                </div>\n              </div>\n            </td>\n            <td>\n              <div class=\"round-actions\">\n                <button @click=\"viewRoundDetails(round)\" class=\"btn btn-info btn-sm\">查看详情</button>\n\n                <!-- 即将开始的轮次 -->\n                <template v-if=\"round.status === 'upcoming'\">\n                  <button @click=\"editRound(round)\" class=\"btn btn-success btn-sm\">编辑</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">取消轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 进行中的轮次 -->\n                <template v-if=\"round.status === 'active'\">\n                  <button @click=\"showExtendModal(round)\" class=\"btn btn-primary btn-sm\">延期</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">立即结束</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 已结束的轮次 -->\n                <template v-if=\"round.status === 'ended'\">\n                  <button @click=\"showRestartModal(round)\" class=\"btn btn-success btn-sm\">重新启动</button>\n                  <button @click=\"showCopyModal(round)\" class=\"btn btn-secondary btn-sm\">复制轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 延期轮次模态框 -->\n    <div v-if=\"showExtendModalFlag\" class=\"modal-overlay\" @click=\"closeExtendModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>延期轮次</h4>\n          <button @click=\"closeExtendModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ extendRound && extendRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>延期时间 (分钟):</label>\n            <input\n              type=\"number\"\n              v-model=\"extendMinutes\"\n              class=\"form-control\"\n              min=\"1\"\n              max=\"1440\"\n              placeholder=\"请输入延期分钟数\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmExtend\" class=\"btn btn-primary\" :disabled=\"!extendMinutes\">确认延期</button>\n          <button @click=\"closeExtendModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重新启动轮次模态框 -->\n    <div v-if=\"showRestartModalFlag\" class=\"modal-overlay\" @click=\"closeRestartModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>重新启动轮次</h4>\n          <button @click=\"closeRestartModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ restartRound && restartRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>新结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmRestart\" class=\"btn btn-primary\">确认重启</button>\n          <button @click=\"closeRestartModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 复制轮次模态框 -->\n    <div v-if=\"showCopyModalFlag\" class=\"modal-overlay\" @click=\"closeCopyModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>复制轮次</h4>\n          <button @click=\"closeCopyModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>源轮次: {{ copyRound && copyRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新轮次名称:</label>\n            <input\n              type=\"text\"\n              v-model=\"newRoundName\"\n              class=\"form-control\"\n              :placeholder=\"copyRound && copyRound.round_name ? copyRound.round_name + ' (复制)' : ''\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmCopy\" class=\"btn btn-primary\">确认复制</button>\n          <button @click=\"closeCopyModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>创建新轮次</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <!-- 整点秒杀时间设置 -->\n          <div class=\"hourly-flash-settings\">\n            <div class=\"setting-header\">\n              <h4>整点秒杀设置</h4>\n              <p class=\"setting-description\">每小时整点开始，持续55分钟，24小时不间断</p>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label>活动开始日期 *</label>\n                <input\n                  v-model=\"newRound.start_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  required\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>活动结束日期 *</label>\n                <input\n                  v-model=\"newRound.end_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  :min=\"newRound.start_date\"\n                  required\n                />\n              </div>\n            </div>\n\n            <!-- 自动生成的轮次名称预览 -->\n            <div v-if=\"generatedRoundName\" class=\"round-name-preview\">\n              <h5>轮次名称：</h5>\n              <div class=\"name-display\">{{ generatedRoundName }}</div>\n            </div>\n\n            <!-- 时段预览 -->\n            <div v-if=\"hourlySlotPreview.length > 0\" class=\"slot-preview\">\n              <h5>将生成以下秒杀时段：</h5>\n              <div class=\"slot-list-container\">\n                <div class=\"slot-list\">\n                  <div v-for=\"(slot, index) in hourlySlotPreview\" :key=\"index\" class=\"slot-item\">\n                    <span class=\"slot-number\">第{{ index + 1 }}场</span>\n                    <span class=\"slot-time\">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>\n                    <span class=\"slot-duration\">55分钟</span>\n                    <span v-if=\"isSlotInPast(slot.start)\" class=\"slot-status past\">已过期</span>\n                    <span v-else-if=\"isSlotActive(slot.start, slot.end)\" class=\"slot-status active\">进行中</span>\n                    <span v-else class=\"slot-status upcoming\">待开始</span>\n                  </div>\n                  <div v-if=\"hourlySlotPreview.length > 10\" class=\"more-slots\">\n                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段\n                  </div>\n                </div>\n              </div>\n              <div class=\"slot-summary\">\n                <span>共 {{ hourlySlotPreview.length }} 个时段</span>\n                <span class=\"valid-slots\">（有效时段：{{ validSlotsCount }} 个）</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 商品选择 -->\n          <div class=\"form-group\">\n            <label>选择商品 * (点击商品卡片进行选择)</label>\n\n            <!-- 加载状态 -->\n            <div v-if=\"loadingGoods\" class=\"loading-state\">\n              <i class=\"loading-icon\">⏳</i>\n              <span>正在加载商品列表...</span>\n            </div>\n\n            <!-- 商品列表 -->\n            <div v-else-if=\"goodsList && goodsList.length > 0\" class=\"goods-selection-grid\">\n              <div\n                v-for=\"goods in goodsList\"\n                :key=\"goods.id\"\n                class=\"goods-card\"\n                :class=\"{\n                  'selected': isGoodsSelected(goods.id),\n                  'disabled': !goods.can_select\n                }\"\n                @click=\"toggleGoods(goods)\"\n              >\n                <!-- 商品基本信息 -->\n                <div class=\"goods-card-header\">\n                  <div class=\"goods-image-container\">\n                    <img :src=\"goods.list_pic_url\" :alt=\"goods.name\" class=\"goods-card-image\" />\n                    <div v-if=\"isGoodsSelected(goods.id)\" class=\"selected-badge\">\n                      <i class=\"checkmark\">✓</i>\n                    </div>\n                    <div v-if=\"!goods.can_select\" class=\"disabled-overlay\">\n                      <span>已参与其他秒杀</span>\n                    </div>\n                  </div>\n                  <div class=\"goods-card-info\">\n                    <h4 class=\"goods-name\">{{ goods.name }}</h4>\n                    <p class=\"original-price\">原价: ¥{{ goods.retail_price }}</p>\n                    <div v-if=\"!goods.can_select\" class=\"warning-text\">\n                      <i class=\"warning-icon\">⚠</i>\n                      <span>已参与其他秒杀活动</span>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 秒杀设置面板 -->\n                <div v-if=\"isGoodsSelected(goods.id)\" class=\"goods-settings-panel\">\n                  <div class=\"settings-title\">秒杀设置</div>\n                  <div class=\"settings-grid\">\n                    <div class=\"setting-item full-width\">\n                      <label>折扣设置</label>\n                      <div class=\"discount-setting\">\n                        <div class=\"discount-input-group\">\n                          <input\n                            v-model.number=\"getSelectedGoods(goods.id).discount_rate\"\n                            type=\"number\"\n                            step=\"1\"\n                            min=\"10\"\n                            max=\"90\"\n                            class=\"discount-input\"\n                            @input=\"updateFlashPriceByDiscount(goods.id)\"\n                            @click.stop\n                          />\n                          <span class=\"discount-unit\">% OFF</span>\n                        </div>\n                        <div class=\"price-preview\">\n                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →\n                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}\n                        </div>\n                        <div class=\"price-range-hint\" v-if=\"goods.price_range\">\n                          商品价格区间: {{ goods.price_range }}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>秒杀库存</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).stock\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"9999\"\n                        class=\"stock-input\"\n                        @click.stop\n                      />\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>限购数量</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).limit_quantity\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"99\"\n                        class=\"limit-input\"\n                        @click.stop\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 空状态显示 -->\n            <div v-else class=\"empty-state\">\n              <div class=\"empty-icon\">📦</div>\n              <p class=\"empty-text\">暂无可选商品</p>\n              <p class=\"empty-hint\">请确保有上架的商品，且未参与其他秒杀活动</p>\n              <button @click=\"loadGoodsList\" class=\"btn btn-secondary btn-sm\">重新加载</button>\n            </div>\n\n            <!-- 选择提示 -->\n            <div class=\"selection-hint\">\n              <p v-if=\"newRound.goods_list.length === 0\" class=\"hint-text\">\n                <i class=\"info-icon\">ℹ</i>\n                请点击商品卡片选择参与秒杀的商品，可以选择多个商品\n              </p>\n              <p v-else class=\"selected-count\">\n                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品\n              </p>\n            </div>\n          </div>\n\n          <!-- 已选商品汇总 -->\n          <div v-if=\"newRound.goods_list.length > 0\" class=\"selected-summary\">\n            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>\n            <div class=\"summary-list\">\n              <div v-for=\"goods in newRound.goods_list\" :key=\"goods.goods_id\" class=\"summary-item\">\n                <span>{{ goods.goods_name }}</span>\n                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>\n                <span>库存: {{ goods.stock }}</span>\n                <button @click=\"removeGoods(goods.goods_id)\" class=\"remove-btn\">移除</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"modal-footer\">\n          <button @click=\"closeModal\" class=\"btn btn-secondary\">取消</button>\n          <button\n            @click=\"createRound\"\n            class=\"btn btn-primary\"\n            :disabled=\"!canCreateRound || isCreating\"\n          >\n            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次详情模态框 -->\n    <div v-if=\"showDetailModal\" class=\"modal-overlay\" @click=\"showDetailModal = false\">\n      <div class=\"modal-content large\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>\n          <button @click=\"showDetailModal = false\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"round-details\">\n            <div class=\"detail-section\">\n              <h4>基本信息</h4>\n              <p>轮次编号: #{{ selectedRound.round_number }}</p>\n              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>\n              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>\n              <p>状态: {{ getStatusText(selectedRound.status) }}</p>\n            </div>\n            \n            <div class=\"detail-section\">\n              <h4>商品列表</h4>\n              <table class=\"table\">\n                <thead>\n                  <tr>\n                    <th>商品</th>\n                    <th>原价</th>\n                    <th>秒杀价</th>\n                    <th>折扣</th>\n                    <th>库存</th>\n                    <th>已售</th>\n                    <th>限购</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"goods in selectedRound.goods_list\" :key=\"goods.id\">\n                    <td>\n                      <div class=\"goods-cell\">\n                        <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n                        <span>{{ goods.goods_name }}</span>\n                      </div>\n                    </td>\n                    <td>¥{{ goods.original_price }}</td>\n                    <td>¥{{ goods.flash_price }}</td>\n                    <td>{{ goods.discount_rate }}%</td>\n                    <td>{{ goods.stock }}</td>\n                    <td>{{ goods.sold_count }}</td>\n                    <td>{{ goods.limit_quantity }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleMultiPage',\n  data() {\n    return {\n      statistics: {},\n      currentRounds: { current: [], upcoming: [] },\n      roundsList: { data: [], count: 0 },\n      goodsList: [],\n      loadingGoods: false,\n      showAddModal: false,\n      showDetailModal: false,\n      selectedRound: {},\n      isCreating: false,\n      newRound: {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      },\n      refreshTimer: null,\n      creationProgress: {\n        current: 0,\n        total: 0\n      },\n\n      // 批量操作相关\n      selectedRounds: [],\n\n      // 自动刷新相关\n      autoRefresh: false,\n      refreshInterval: 30000, // 30秒\n      refreshCountdown: 0,\n      countdownTimer: null,\n      loading: false,\n\n      // 延期模态框\n      showExtendModalFlag: false,\n      extendRound: null,\n      extendMinutes: null,\n\n      // 重启模态框\n      showRestartModalFlag: false,\n      restartRound: null,\n      newStartTime: '',\n      newEndTime: '',\n\n      // 复制模态框\n      showCopyModalFlag: false,\n      copyRound: null,\n      newRoundName: '',\n      copyStartTime: '',\n      copyEndTime: ''\n    };\n  },\n  \n  computed: {\n    canCreateRound() {\n      const hasStartDate = this.newRound.start_date;\n      const hasEndDate = this.newRound.end_date;\n      const hasGoods = this.newRound.goods_list.length > 0;\n      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);\n      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);\n\n      console.log('canCreateRound检查:', {\n        hasStartDate,\n        hasEndDate,\n        hasGoods,\n        goodsValid,\n        dateValid,\n        goodsList: this.newRound.goods_list\n      });\n\n      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;\n    },\n\n    // 批量选择相关计算属性\n    isAllSelected() {\n      return this.roundsList.data.length > 0 && this.selectedRounds.length === this.roundsList.data.length;\n    },\n\n    isIndeterminate() {\n      return this.selectedRounds.length > 0 && this.selectedRounds.length < this.roundsList.data.length;\n    },\n\n    // 自动生成轮次名称\n    generatedRoundName() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return '';\n      }\n\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      if (startDate.getTime() === endDate.getTime()) {\n        // 单日活动\n        const dateStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${dateStr}整点秒杀`;\n      } else {\n        // 多日活动\n        const startStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        const endStr = endDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${startStr}至${endStr}整点秒杀`;\n      }\n    },\n\n    // 整点秒杀时段预览（24小时全天候）\n    hourlySlotPreview() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return [];\n      }\n\n      const slots = [];\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      // 设置结束日期为当天的23:59:59\n      endDate.setHours(23, 59, 59, 999);\n\n      let currentDate = new Date(startDate);\n      currentDate.setHours(0, 0, 0, 0); // 从00:00开始\n\n      while (currentDate <= endDate) {\n        for (let hour = 0; hour < 24; hour++) {\n          const slotStart = new Date(currentDate);\n          slotStart.setHours(hour, 0, 0, 0);\n\n          const slotEnd = new Date(currentDate);\n          slotEnd.setHours(hour, 55, 0, 0);\n\n          // 检查轮次开始时间是否超出结束日期\n          if (slotStart > endDate) {\n            break;\n          }\n\n          slots.push({\n            start: this.formatLocalDateTime(slotStart),\n            end: this.formatLocalDateTime(slotEnd),\n            startTime: slotStart,\n            endTime: slotEnd\n          });\n        }\n\n        // 移动到下一天\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n      return slots;\n    },\n\n    // 有效时段数量（未过期的时段）\n    validSlotsCount() {\n      const now = new Date();\n      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;\n    }\n  },\n\n  mounted() {\n    console.log('FlashSaleMultiPage组件已挂载');\n    console.log('初始showAddModal值:', this.showAddModal);\n    this.loadData();\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n    if (this.countdownTimer) {\n      clearInterval(this.countdownTimer);\n    }\n  },\n\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList()\n      ]);\n    },\n\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalemulti/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalemulti/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    async loadRoundsList() {\n      try {\n        const response = await this.axios.get('flashsalemulti/list');\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    async loadGoodsList() {\n      try {\n        this.loadingGoods = true;\n        console.log('开始加载商品列表...');\n\n        const response = await this.axios.get('flashsalemulti/goods');\n        console.log('商品列表API响应:', response.data);\n\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data || [];\n          console.log('商品列表加载成功，数量:', this.goodsList.length);\n\n          if (this.goodsList.length === 0) {\n            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');\n          }\n        } else {\n          console.error('API返回错误:', response.data.errmsg);\n          this.$message.error(response.data.errmsg || '加载商品列表失败');\n          this.goodsList = [];\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n        this.$message.error('网络错误，请检查服务器连接');\n        this.goodsList = [];\n      } finally {\n        this.loadingGoods = false;\n      }\n    },\n\n    startAutoRefresh() {\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 30000); // 30秒刷新一次\n    },\n\n    isGoodsSelected(goodsId) {\n      return this.newRound.goods_list.some(g => g.goods_id === goodsId);\n    },\n\n    getSelectedGoods(goodsId) {\n      return this.newRound.goods_list.find(g => g.goods_id === goodsId);\n    },\n\n    toggleGoods(goods) {\n      if (!goods.can_select) return;\n\n      if (this.isGoodsSelected(goods.id)) {\n        this.removeGoods(goods.id);\n      } else {\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const defaultDiscount = 20; // 默认20%折扣\n        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;\n\n        this.newRound.goods_list.push({\n          goods_id: goods.id,\n          goods_name: goods.name,\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: defaultDiscount,\n          stock: 100,\n          limit_quantity: 1\n        });\n      }\n    },\n\n    removeGoods(goodsId) {\n      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);\n      if (index > -1) {\n        this.newRound.goods_list.splice(index, 1);\n      }\n    },\n\n    calculateDiscountRate(originalPrice, flashPrice) {\n      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;\n      const rate = Math.round((1 - flashPrice / originalPrice) * 100);\n      return isNaN(rate) ? 0 : rate;\n    },\n\n    updateFlashPriceByDiscount(goodsId) {\n      const selectedGoods = this.getSelectedGoods(goodsId);\n      if (selectedGoods && selectedGoods.original_price > 0) {\n        const discountRate = selectedGoods.discount_rate || 0;\n        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;\n        selectedGoods.flash_price = flashPrice;\n      }\n    },\n\n    // 格式化本地日期时间为字符串（避免时区问题）\n    formatLocalDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n\n    // 格式化本地日期为字符串（避免时区问题）\n    formatLocalDate(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    getCurrentDateTime() {\n      const now = new Date();\n      return this.formatLocalDateTime(now).slice(0, 16).replace(' ', 'T');\n    },\n\n    getCurrentDate() {\n      const now = new Date();\n      return this.formatLocalDate(now);\n    },\n\n    openCreateModal() {\n      console.log('点击创建新轮次按钮');\n      // 设置默认日期为今天\n      const today = this.getCurrentDate();\n      this.newRound.start_date = today;\n      this.newRound.end_date = today;\n      this.showAddModal = true;\n      console.log('showAddModal设置为:', this.showAddModal);\n    },\n\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatSlotTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    isSlotInPast(startTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      return slotStart < now;\n    },\n\n    isSlotActive(startTime, endTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      const slotEnd = new Date(endTime);\n      return now >= slotStart && now <= slotEnd;\n    },\n\n    async createRound() {\n      if (!this.canCreateRound) {\n        this.$message.error('请完善轮次信息');\n        return;\n      }\n\n      // 生成整点秒杀时段数据\n      const hourlySlots = this.hourlySlotPreview;\n      const now = new Date();\n\n      // 过滤掉已完全结束的时段，保留当前正在进行的和未来的时段\n      const validSlots = hourlySlots.filter(slot => new Date(slot.end) > now);\n\n      if (validSlots.length === 0) {\n        this.$message.error('所选时间段内没有有效的秒杀时段');\n        return;\n      }\n\n      // 如果轮次数量过多，询问用户确认\n      if (validSlots.length > 50) {\n        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);\n        if (!confirmed) {\n          return;\n        }\n      }\n\n      try {\n        this.isCreating = true;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = validSlots.length;\n\n        let createdCount = 0;\n        let failedCount = 0;\n        const failedReasons = [];\n\n        // 批量处理，每次处理10个轮次\n        const batchSize = 10;\n        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {\n          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);\n          const batch = validSlots.slice(batchStart, batchEnd);\n\n          // 并行创建当前批次的轮次\n          const batchPromises = batch.map(async (slot, batchIndex) => {\n            const globalIndex = batchStart + batchIndex;\n\n            // 重试逻辑\n            const maxRetries = 3;\n            let lastError = null;\n\n            for (let retry = 0; retry < maxRetries; retry++) {\n              try {\n                const roundData = {\n                  round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,\n                  start_time: slot.start,\n                  end_time: slot.end,\n                  is_hourly_flash: true,\n                  slot_index: globalIndex + 1,\n                  total_slots: validSlots.length,\n                  goods_list: this.newRound.goods_list.map(goods => ({\n                    goods_id: goods.goods_id,\n                    goods_name: goods.goods_name,\n                    goods_image: goods.goods_image,\n                    original_price: goods.original_price,\n                    flash_price: goods.flash_price,\n                    stock: goods.stock,\n                    discount_rate: goods.discount_rate\n                  }))\n                };\n\n                const response = await this.axios.post('flashsalemulti/create', roundData);\n\n                if (response.data.errno === 0) {\n                  return { success: true, index: globalIndex + 1 };\n                } else {\n                  lastError = response.data.errmsg;\n                  // 如果是重复键错误，等待一段时间后重试\n                  if (lastError.includes('Duplicate entry') && retry < maxRetries - 1) {\n                    await new Promise(resolve => setTimeout(resolve, 500 * (retry + 1)));\n                    continue;\n                  }\n                  return { success: false, index: globalIndex + 1, error: lastError };\n                }\n              } catch (error) {\n                lastError = error.message;\n                // 如果是网络错误或服务器错误，等待后重试\n                if (retry < maxRetries - 1) {\n                  await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));\n                  continue;\n                }\n              }\n            }\n\n            return { success: false, index: globalIndex + 1, error: `创建失败 (已重试${maxRetries}次): ${lastError}` };\n          });\n\n          // 等待当前批次完成\n          const batchResults = await Promise.all(batchPromises);\n\n          // 统计结果\n          batchResults.forEach(result => {\n            this.creationProgress.current++;\n            if (result.success) {\n              createdCount++;\n            } else {\n              failedCount++;\n              if (failedReasons.length < 5) { // 只记录前5个错误\n                failedReasons.push(`第${result.index}场: ${result.error}`);\n              }\n            }\n          });\n\n          // 短暂延迟，避免服务器压力过大\n          if (batchEnd < validSlots.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n          }\n        }\n\n        // 显示结果\n        if (createdCount > 0) {\n          let message = `成功创建${createdCount}个整点秒杀轮次`;\n          if (failedCount > 0) {\n            message += `，${failedCount}个失败`;\n            if (failedReasons.length > 0) {\n              console.warn('创建失败的轮次:', failedReasons);\n              message += `\\n主要错误: ${failedReasons[0]}`;\n            }\n          }\n          this.$message.success(message);\n          this.closeModal();\n          this.loadData();\n        } else {\n          let errorMessage = '所有轮次创建失败';\n          if (failedReasons.length > 0) {\n            errorMessage += `\\n错误信息: ${failedReasons[0]}`;\n          }\n          this.$message.error(errorMessage);\n        }\n\n      } catch (error) {\n        console.error('创建整点秒杀轮次失败:', error);\n        this.$message.error('创建过程中发生错误: ' + error.message);\n      } finally {\n        this.isCreating = false;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = 0;\n      }\n    },\n\n    closeModal() {\n      this.showAddModal = false;\n      this.newRound = {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      };\n    },\n\n    viewRoundDetails(round) {\n      this.selectedRound = round;\n      this.showDetailModal = true;\n    },\n\n    async closeRound(round) {\n      if (!confirm(`确定要关闭轮次\"${round.round_name}\"吗？关闭后轮次将立即结束。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在关闭轮次...');\n        const response = await this.axios.post('flashsalemulti/close', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已关闭');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '关闭失败');\n        }\n      } catch (error) {\n        console.error('关闭轮次失败:', error);\n        this.$message.error('关闭失败');\n      }\n    },\n\n\n\n    formatTime(seconds) {\n      if (!seconds || seconds <= 0) return '00:00:00';\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor((seconds % 3600) / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return new Date(dateTime).toLocaleString('zh-CN');\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束'\n      };\n      return statusMap[status] || status;\n    },\n\n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'upcoming': 'badge badge-warning',\n        'active': 'badge badge-success',\n        'ended': 'badge badge-secondary'\n      };\n      return classMap[status] || 'badge badge-light';\n    },\n\n    // 获取倒计时\n    getCountdown(timeStr) {\n      if (!timeStr) return '';\n      const targetTime = new Date(timeStr);\n      const now = new Date();\n      const diff = targetTime - now;\n\n      if (diff <= 0) return '';\n\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n\n      if (hours > 0) {\n        return `${hours}小时${minutes}分钟`;\n      } else if (minutes > 0) {\n        return `${minutes}分钟${seconds}秒`;\n      } else {\n        return `${seconds}秒`;\n      }\n    },\n\n    // 自动刷新相关方法\n    toggleAutoRefresh() {\n      if (this.autoRefresh) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先停止之前的定时器\n\n      this.refreshTimer = setInterval(() => {\n        this.refreshData();\n      }, this.refreshInterval);\n\n      // 启动倒计时\n      this.refreshCountdown = this.refreshInterval / 1000;\n      this.countdownTimer = setInterval(() => {\n        this.refreshCountdown--;\n        if (this.refreshCountdown <= 0) {\n          this.refreshCountdown = this.refreshInterval / 1000;\n        }\n      }, 1000);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n      this.refreshCountdown = 0;\n    },\n\n    async refreshData() {\n      this.loading = true;\n      try {\n        await this.loadData();\n      } catch (error) {\n        console.error('刷新数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 批量操作相关方法\n    toggleSelectAll() {\n      if (this.isAllSelected) {\n        this.selectedRounds = [];\n      } else {\n        this.selectedRounds = this.roundsList.data.map(round => round.id);\n      }\n    },\n\n    updateSelection() {\n      // 这个方法会在复选框状态改变时自动调用\n    },\n\n    clearSelection() {\n      this.selectedRounds = [];\n    },\n\n    async batchCloseRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要关闭的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要关闭选中的 ${this.selectedRounds.length} 个轮次吗？`,\n          '批量关闭确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          let successCount = 0;\n          let failCount = 0;\n\n          for (const roundId of this.selectedRounds) {\n            try {\n              const response = await this.$http.post('/admin/flashsalemulti/close', {\n                round_id: roundId\n              });\n\n              if (response.data.errno === 0) {\n                successCount++;\n              } else {\n                failCount++;\n              }\n            } catch (error) {\n              failCount++;\n            }\n          }\n\n          this.$message.success(`批量关闭完成：成功 ${successCount} 个，失败 ${failCount} 个`);\n          this.clearSelection();\n          this.loadData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量关闭失败:', error);\n          this.$message.error('批量关闭失败');\n        }\n      }\n    },\n\n    async batchDeleteRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要删除的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要删除选中的 ${this.selectedRounds.length} 个轮次吗？此操作不可恢复！\\n\\n注意：进行中或即将开始的轮次将先被关闭，然后删除。`,\n          '批量删除确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在批量删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/batchDelete', {\n            round_ids: this.selectedRounds\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success(response.data.errmsg || '批量删除完成');\n            this.clearSelection();\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '批量删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error);\n          this.$message.error('批量删除失败');\n        }\n      }\n    },\n\n    // 延期轮次相关方法\n    showExtendModal(round) {\n      this.extendRound = round;\n      this.extendMinutes = null;\n      this.showExtendModalFlag = true;\n    },\n\n    closeExtendModal() {\n      this.showExtendModalFlag = false;\n      this.extendRound = null;\n      this.extendMinutes = null;\n    },\n\n    async confirmExtend() {\n      if (!this.extendMinutes || this.extendMinutes <= 0) {\n        this.$message.warning('请输入有效的延期时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/extend', {\n          round_id: this.extendRound.id,\n          extend_minutes: this.extendMinutes\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success(`轮次已延期${this.extendMinutes}分钟`);\n          this.closeExtendModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '延期失败');\n        }\n      } catch (error) {\n        console.error('延期轮次失败:', error);\n        this.$message.error('延期失败');\n      }\n    },\n\n    // 重启轮次相关方法\n    showRestartModal(round) {\n      this.restartRound = round;\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.newStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.newEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showRestartModalFlag = true;\n    },\n\n    closeRestartModal() {\n      this.showRestartModalFlag = false;\n      this.restartRound = null;\n      this.newStartTime = '';\n      this.newEndTime = '';\n    },\n\n    async confirmRestart() {\n      if (!this.newStartTime || !this.newEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.newStartTime) >= new Date(this.newEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/restart', {\n          round_id: this.restartRound.id,\n          new_start_time: this.newStartTime,\n          new_end_time: this.newEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已重新启动');\n          this.closeRestartModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '重启失败');\n        }\n      } catch (error) {\n        console.error('重启轮次失败:', error);\n        this.$message.error('重启失败');\n      }\n    },\n\n    // 复制轮次相关方法\n    showCopyModal(round) {\n      this.copyRound = round;\n      this.newRoundName = round.round_name + ' (复制)';\n\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.copyStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.copyEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showCopyModalFlag = true;\n    },\n\n    closeCopyModal() {\n      this.showCopyModalFlag = false;\n      this.copyRound = null;\n      this.newRoundName = '';\n      this.copyStartTime = '';\n      this.copyEndTime = '';\n    },\n\n    async confirmCopy() {\n      if (!this.copyStartTime || !this.copyEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.copyStartTime) >= new Date(this.copyEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/copy', {\n          round_id: this.copyRound.id,\n          new_round_name: this.newRoundName,\n          new_start_time: this.copyStartTime,\n          new_end_time: this.copyEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次复制成功');\n          this.closeCopyModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '复制失败');\n        }\n      } catch (error) {\n        console.error('复制轮次失败:', error);\n        this.$message.error('复制失败');\n      }\n    },\n\n    // 删除轮次\n    async deleteRound(round) {\n      try {\n        const statusText = {\n          'upcoming': '即将开始',\n          'active': '进行中',\n          'ended': '已结束'\n        };\n\n        let confirmMessage = `确定要删除轮次\"${round.round_name}\"吗？此操作不可恢复！`;\n\n        if (round.status === 'active' || round.status === 'upcoming') {\n          confirmMessage += `\\n\\n注意：该轮次当前状态为\"${statusText[round.status]}\"，删除前将先自动关闭轮次。`;\n        }\n\n        const confirmResult = await this.$confirm(\n          confirmMessage,\n          '删除轮次确认',\n          {\n            confirmButtonText: '确定删除',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/delete', {\n            round_id: round.id\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success('轮次删除成功');\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除轮次失败:', error);\n          this.$message.error('删除失败');\n        }\n      }\n    },\n\n    // 编辑轮次\n    editRound(round) {\n      // 这里可以跳转到编辑页面或打开编辑模态框\n      this.$message.info('编辑功能待实现');\n    },\n\n    // 格式化日期时间为本地格式（用于datetime-local输入框）\n    formatDateTimeLocal(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n\n      return `${year}-${month}-${day}T${hours}:${minutes}`;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.flash-sale-multi-page {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n/* 表格控制区域 */\n.table-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.auto-refresh-control {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-size: 14px;\n}\n\n.refresh-countdown {\n  color: #666;\n  font-size: 12px;\n}\n\n/* 批量操作区域 */\n.batch-operations {\n  margin-bottom: 15px;\n}\n\n.batch-actions {\n  margin-top: 10px;\n  display: flex;\n  gap: 10px;\n}\n\n/* 状态信息显示 */\n.status-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.time-info {\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.countdown {\n  font-weight: bold;\n  color: #f56c6c;\n}\n\n/* 操作按钮组 */\n.round-actions {\n  display: flex;\n  gap: 5px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin-bottom: 2px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.round-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin: 0;\n}\n\n.btn-info {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: white;\n}\n\n.btn-info:hover {\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #212529;\n}\n\n.btn-warning:hover {\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n/* 整点秒杀设置样式 */\n.hourly-flash-settings {\n  margin-bottom: 20px;\n}\n\n.setting-header {\n  margin-bottom: 15px;\n}\n\n.setting-header h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.setting-description {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.time-range-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  color: #666;\n  font-weight: bold;\n}\n\n.slot-preview {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 5px;\n  border: 1px solid #e9ecef;\n}\n\n.slot-preview h5 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.slot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.slot-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 8px 12px;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.slot-number {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 60px;\n}\n\n.slot-time {\n  flex: 1;\n  color: #333;\n}\n\n.slot-duration {\n  color: #28a745;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.slot-summary {\n  margin-top: 10px;\n  padding-top: 10px;\n  border-top: 1px solid #dee2e6;\n  text-align: center;\n  color: #666;\n  font-weight: bold;\n}\n\n.valid-slots {\n  color: #28a745;\n  margin-left: 10px;\n}\n\n.round-name-preview {\n  margin-bottom: 20px;\n}\n\n.round-name-preview h5 {\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: bold;\n}\n\n.name-display {\n  padding: 10px 15px;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n.slot-list-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.slot-status {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n}\n\n.slot-status.past {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.slot-status.active {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.slot-status.upcoming {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.more-slots {\n  padding: 10px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.stat-card h3 {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.stat-number.active {\n  color: #28a745;\n}\n\n.stat-number.upcoming {\n  color: #ffc107;\n}\n\n/* 当前轮次 */\n.current-rounds {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.round-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.round-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n}\n\n.round-item.active {\n  border-color: #28a745;\n  background-color: #f8fff9;\n}\n\n.round-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.round-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.goods-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 80px;\n}\n\n.goods-item img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n  margin-bottom: 4px;\n}\n\n.goods-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n.goods-item .price {\n  color: #e74c3c;\n  font-weight: bold;\n}\n\n.more-goods {\n  color: #007bff;\n  font-size: 12px;\n}\n\n/* 表格 */\n.rounds-table {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.status-upcoming {\n  color: #ffc107;\n  font-weight: bold;\n}\n\n.status-active {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.status-ended {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n/* 模态框 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content.large {\n  max-width: 1000px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n}\n\n.modal-body {\n  padding: 20px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 表单 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: flex;\n  gap: 15px;\n}\n\n.form-row .form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control.small {\n  width: 80px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 商品选择网格 */\n.goods-selection-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n}\n\n/* 商品卡片 */\n.goods-card {\n  background: white;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.goods-card:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.goods-card.selected {\n  border-color: #28a745;\n  background-color: #f8fff9;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);\n}\n\n.goods-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background-color: #f5f5f5;\n  border-color: #dee2e6;\n}\n\n.goods-card.disabled:hover {\n  transform: none;\n  box-shadow: none;\n  border-color: #dee2e6;\n}\n\n/* 商品卡片头部 */\n.goods-card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.goods-image-container {\n  position: relative;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.goods-card-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.selected-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background-color: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.2);\n}\n\n.checkmark {\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.disabled-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 商品信息 */\n.goods-card-info {\n  flex: 1;\n}\n\n.goods-name {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.original-price {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.warning-text {\n  display: flex;\n  align-items: center;\n  color: #dc3545;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.warning-icon {\n  margin-right: 4px;\n  font-size: 14px;\n}\n\n/* 商品设置面板 */\n.goods-settings-panel {\n  border-top: 1px solid #e9ecef;\n  padding-top: 15px;\n  margin-top: 15px;\n}\n\n.settings-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.settings-title::before {\n  content: \"⚙\";\n  margin-right: 6px;\n  color: #007bff;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n}\n\n.setting-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-item.full-width {\n  grid-column: 1 / -1;\n}\n\n.setting-item label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n/* 价格输入组 */\n.price-input-group {\n  display: flex;\n  align-items: center;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n  background: white;\n}\n\n.currency {\n  background-color: #f8f9fa;\n  padding: 6px 8px;\n  border-right: 1px solid #ddd;\n  font-size: 14px;\n  color: #666;\n}\n\n.price-input {\n  border: none;\n  padding: 6px 8px;\n  font-size: 14px;\n  flex: 1;\n  outline: none;\n}\n\n.price-input:focus {\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.discount-display {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #e74c3c;\n  font-weight: bold;\n  text-align: center;\n  background-color: #fff5f5;\n  padding: 2px 6px;\n  border-radius: 12px;\n  border: 1px solid #fecaca;\n}\n\n/* 库存和限购输入 */\n.stock-input,\n.limit-input {\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 6px 8px;\n  font-size: 14px;\n  outline: none;\n  background: white;\n}\n\n.stock-input:focus,\n.limit-input:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 折扣设置样式 */\n.discount-setting {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.discount-input-group {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.discount-input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.discount-unit {\n  margin-left: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.price-preview {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.price-range-hint {\n  font-size: 12px;\n  color: #28a745;\n  font-style: italic;\n}\n\n/* 选择提示 */\n.selection-hint {\n  margin-top: 15px;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #007bff;\n}\n\n.hint-text {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-right: 8px;\n  color: #007bff;\n  font-size: 16px;\n}\n\n.selected-count {\n  margin: 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.selected-count strong {\n  color: #28a745;\n  font-size: 16px;\n}\n\n/* 已选商品汇总 */\n.selected-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.selected-summary h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n}\n\n.summary-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.remove-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n/* 轮次详情 */\n.round-details {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.detail-section h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.detail-section p {\n  margin: 5px 0;\n  color: #666;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  color: #6c757d;\n  font-size: 16px;\n}\n\n.loading-state .loading-icon {\n  margin-right: 10px;\n  font-size: 20px;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n.empty-state .empty-text {\n  font-size: 18px;\n  font-weight: 500;\n  color: #495057;\n  margin: 0 0 8px 0;\n}\n\n.empty-state .empty-hint {\n  font-size: 14px;\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.empty-state .btn-sm {\n  padding: 6px 16px;\n  font-size: 14px;\n}\n\n.goods-cell {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-cell img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.goods-cell span {\n  font-size: 14px;\n}\n\n/* 状态徽章样式 */\n.badge {\n  display: inline-block;\n  padding: 4px 8px;\n  font-size: 12px;\n  font-weight: bold;\n  border-radius: 4px;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.badge-warning {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.badge-success {\n  background-color: #28a745;\n  color: white;\n}\n\n.badge-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.badge-light {\n  background-color: #f8f9fa;\n  color: #6c757d;\n  border: 1px solid #dee2e6;\n}\n\n/* 模态框样式增强 */\n.modal-content.small {\n  max-width: 500px;\n}\n\n.modal-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 18px;\n}\n\n/* 表单控件增强 */\n.form-control[type=\"number\"] {\n  text-align: center;\n}\n\n.form-control[type=\"datetime-local\"] {\n  font-family: monospace;\n}\n\n/* 按钮样式增强 */\n.btn:hover {\n  opacity: 0.9;\n  transform: translateY(-1px);\n  transition: all 0.2s ease;\n}\n\n.btn-success {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: white;\n}\n\n.btn-success:hover {\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n</style>\n</style>\n"]}]}