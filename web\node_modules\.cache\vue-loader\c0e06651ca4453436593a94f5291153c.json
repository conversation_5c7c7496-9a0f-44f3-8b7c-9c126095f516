{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue?vue&type=template&id=52cb9d70&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue", "mtime": 1754302177628}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}