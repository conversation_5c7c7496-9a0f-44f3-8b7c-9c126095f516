-- 积分商品相关表结构
-- 请在MySQL中执行此脚本

USE hiolabsDB;

-- 创建积分商品表
DROP TABLE IF EXISTS `hiolabs_points_goods`;
CREATE TABLE `hiolabs_points_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '关联的商品ID',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `goods_image` varchar(500) DEFAULT '' COMMENT '商品图片',
  `points_price` int(11) NOT NULL COMMENT '积分价格',
  `original_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '原价',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sold_count` int(11) NOT NULL DEFAULT '0' COMMENT '已售数量',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重，数值越大越靠前',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0=下架，1=上架',
  `is_hot` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否热门：0=否，1=是',
  `description` text COMMENT '商品描述',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否，1=是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_sort` (`sort`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品表';

-- 创建积分兑换订单表
DROP TABLE IF EXISTS `hiolabs_points_orders`;
CREATE TABLE `hiolabs_points_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_goods_id` int(11) NOT NULL COMMENT '积分商品ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `goods_image` varchar(500) DEFAULT '' COMMENT '商品图片',
  `points_price` int(11) NOT NULL COMMENT '积分价格',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '兑换数量',
  `total_points` int(11) NOT NULL COMMENT '总积分',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态：0=待发货，1=已发货，2=已完成，3=已取消',
  `address_info` text COMMENT '收货地址信息JSON',
  `express_company` varchar(100) DEFAULT '' COMMENT '快递公司',
  `express_no` varchar(100) DEFAULT '' COMMENT '快递单号',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否，1=是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `shipped_at` timestamp NULL DEFAULT NULL COMMENT '发货时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sn` (`order_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_points_goods_id` (`points_goods_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分兑换订单表';

-- 插入一些测试数据
INSERT INTO `hiolabs_points_goods` (`goods_id`, `goods_name`, `goods_image`, `points_price`, `original_price`, `stock`, `sold_count`, `sort`, `status`, `is_hot`, `description`) VALUES
(1, '精美保温水杯', 'https://picsum.photos/300/300?random=1', 500, 89.00, 100, 25, 100, 1, 1, '高品质保温水杯，保温效果佳，适合日常使用'),
(2, '无线蓝牙耳机', 'https://picsum.photos/300/300?random=2', 1200, 199.00, 50, 18, 90, 1, 1, '高音质无线蓝牙耳机，降噪效果好，续航时间长'),
(3, '多功能手机支架', 'https://picsum.photos/300/300?random=3', 300, 39.00, 200, 56, 80, 1, 0, '可调节角度的手机支架，适用于各种场景'),
(4, '快充无线充电器', 'https://picsum.photos/300/300?random=4', 800, 129.00, 80, 32, 70, 1, 1, '支持快充的无线充电器，兼容多种设备'),
(5, '大容量充电宝', 'https://picsum.photos/300/300?random=5', 600, 99.00, 120, 41, 60, 1, 0, '20000mAh大容量充电宝，多接口设计'),
(6, '智能手环', 'https://picsum.photos/300/300?random=6', 900, 159.00, 60, 23, 50, 1, 1, '多功能智能手环，健康监测，运动记录');

-- 创建积分商品配置表
DROP TABLE IF EXISTS `hiolabs_points_config`;
CREATE TABLE `hiolabs_points_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT '' COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商城配置表';

-- 插入默认配置
INSERT INTO `hiolabs_points_config` (`config_key`, `config_value`, `config_desc`) VALUES
('points_mall_enabled', '1', '积分商城是否启用：0=关闭，1=开启'),
('points_mall_banner', '[]', '积分商城轮播图配置JSON'),
('points_mall_notice', '欢迎来到积分商城，用积分兑换心仪商品！', '积分商城公告'),
('max_exchange_per_day', '10', '每日最大兑换次数'),
('min_points_required', '100', '最低积分要求');

-- 创建积分商品分类表（可选，用于后续扩展）
DROP TABLE IF EXISTS `hiolabs_points_categories`;
CREATE TABLE `hiolabs_points_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT '' COMMENT '分类图标',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否，1=是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品分类表';

-- 插入默认分类
INSERT INTO `hiolabs_points_categories` (`name`, `icon`, `sort`, `status`) VALUES
('数码配件', '/images/category/digital.png', 100, 1),
('生活用品', '/images/category/life.png', 90, 1),
('美妆护肤', '/images/category/beauty.png', 80, 1),
('食品饮料', '/images/category/food.png', 70, 1),
('服装配饰', '/images/category/clothing.png', 60, 1);

-- 为积分商品表添加分类字段（可选）
ALTER TABLE `hiolabs_points_goods` ADD COLUMN `category_id` int(11) DEFAULT '0' COMMENT '分类ID' AFTER `goods_id`;
ALTER TABLE `hiolabs_points_goods` ADD KEY `idx_category_id` (`category_id`);

-- 更新测试数据的分类
UPDATE `hiolabs_points_goods` SET `category_id` = 1 WHERE `id` IN (2, 4, 5, 6); -- 数码配件
UPDATE `hiolabs_points_goods` SET `category_id` = 2 WHERE `id` IN (1, 3); -- 生活用品

COMMIT;

-- 显示创建结果
SELECT 'Points goods tables created successfully!' as message;
SELECT COUNT(*) as points_goods_count FROM hiolabs_points_goods;
SELECT COUNT(*) as points_categories_count FROM hiolabs_points_categories;
SELECT COUNT(*) as points_config_count FROM hiolabs_points_config;
