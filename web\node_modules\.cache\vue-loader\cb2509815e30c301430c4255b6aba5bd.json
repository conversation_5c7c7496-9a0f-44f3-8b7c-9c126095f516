{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1754251484081}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmxhc2hTYWxlTXVsdGlQYWdlJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3RhdGlzdGljczoge30sCiAgICAgIGN1cnJlbnRSb3VuZHM6IHsgY3VycmVudDogW10sIHVwY29taW5nOiBbXSB9LAogICAgICByb3VuZHNMaXN0OiB7IGRhdGE6IFtdLCBjb3VudDogMCB9LAogICAgICBnb29kc0xpc3Q6IFtdLAogICAgICBsb2FkaW5nR29vZHM6IGZhbHNlLAogICAgICBzaG93QWRkTW9kYWw6IGZhbHNlLAogICAgICBzaG93RGV0YWlsTW9kYWw6IGZhbHNlLAogICAgICBzZWxlY3RlZFJvdW5kOiB7fSwKICAgICAgaXNDcmVhdGluZzogZmFsc2UsCiAgICAgIG5ld1JvdW5kOiB7CiAgICAgICAgc3RhcnRfZGF0ZTogJycsCiAgICAgICAgZW5kX2RhdGU6ICcnLAogICAgICAgIGdvb2RzX2xpc3Q6IFtdCiAgICAgIH0sCiAgICAgIHJlZnJlc2hUaW1lcjogbnVsbCwKICAgICAgY3JlYXRpb25Qcm9ncmVzczogewogICAgICAgIGN1cnJlbnQ6IDAsCiAgICAgICAgdG90YWw6IDAKICAgICAgfSwKCiAgICAgIC8vIOaJuemHj+aTjeS9nOebuOWFswogICAgICBzZWxlY3RlZFJvdW5kczogW10sCgogICAgICAvLyDoh6rliqjliLfmlrDnm7jlhbMKICAgICAgYXV0b1JlZnJlc2g6IGZhbHNlLAogICAgICByZWZyZXNoSW50ZXJ2YWw6IDMwMDAwLCAvLyAzMOenkgogICAgICByZWZyZXNoQ291bnRkb3duOiAwLAogICAgICBjb3VudGRvd25UaW1lcjogbnVsbCwKICAgICAgbG9hZGluZzogZmFsc2UsCgogICAgICAvLyDlu7bmnJ/mqKHmgIHmoYYKICAgICAgc2hvd0V4dGVuZE1vZGFsRmxhZzogZmFsc2UsCiAgICAgIGV4dGVuZFJvdW5kOiBudWxsLAogICAgICBleHRlbmRNaW51dGVzOiBudWxsLAoKICAgICAgLy8g6YeN5ZCv5qih5oCB5qGGCiAgICAgIHNob3dSZXN0YXJ0TW9kYWxGbGFnOiBmYWxzZSwKICAgICAgcmVzdGFydFJvdW5kOiBudWxsLAogICAgICBuZXdTdGFydFRpbWU6ICcnLAogICAgICBuZXdFbmRUaW1lOiAnJywKCiAgICAgIC8vIOWkjeWItuaooeaAgeahhgogICAgICBzaG93Q29weU1vZGFsRmxhZzogZmFsc2UsCiAgICAgIGNvcHlSb3VuZDogbnVsbCwKICAgICAgbmV3Um91bmROYW1lOiAnJywKICAgICAgY29weVN0YXJ0VGltZTogJycsCiAgICAgIGNvcHlFbmRUaW1lOiAnJwogICAgfTsKICB9LAogIAogIGNvbXB1dGVkOiB7CiAgICBjYW5DcmVhdGVSb3VuZCgpIHsKICAgICAgY29uc3QgaGFzU3RhcnREYXRlID0gdGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlOwogICAgICBjb25zdCBoYXNFbmREYXRlID0gdGhpcy5uZXdSb3VuZC5lbmRfZGF0ZTsKICAgICAgY29uc3QgaGFzR29vZHMgPSB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QubGVuZ3RoID4gMDsKICAgICAgY29uc3QgZ29vZHNWYWxpZCA9IHRoaXMubmV3Um91bmQuZ29vZHNfbGlzdC5ldmVyeShnID0+IGcuZmxhc2hfcHJpY2UgPiAwICYmIGcuc3RvY2sgPiAwKTsKICAgICAgY29uc3QgZGF0ZVZhbGlkID0gaGFzU3RhcnREYXRlICYmIGhhc0VuZERhdGUgJiYgbmV3IERhdGUodGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlKSA8PSBuZXcgRGF0ZSh0aGlzLm5ld1JvdW5kLmVuZF9kYXRlKTsKCiAgICAgIGNvbnNvbGUubG9nKCdjYW5DcmVhdGVSb3VuZOajgOafpTonLCB7CiAgICAgICAgaGFzU3RhcnREYXRlLAogICAgICAgIGhhc0VuZERhdGUsCiAgICAgICAgaGFzR29vZHMsCiAgICAgICAgZ29vZHNWYWxpZCwKICAgICAgICBkYXRlVmFsaWQsCiAgICAgICAgZ29vZHNMaXN0OiB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QKICAgICAgfSk7CgogICAgICByZXR1cm4gaGFzU3RhcnREYXRlICYmIGhhc0VuZERhdGUgJiYgaGFzR29vZHMgJiYgZ29vZHNWYWxpZCAmJiBkYXRlVmFsaWQ7CiAgICB9LAoKICAgIC8vIOaJuemHj+mAieaLqeebuOWFs+iuoeeul+WxnuaApwogICAgaXNBbGxTZWxlY3RlZCgpIHsKICAgICAgcmV0dXJuIHRoaXMucm91bmRzTGlzdC5kYXRhLmxlbmd0aCA+IDAgJiYgdGhpcy5zZWxlY3RlZFJvdW5kcy5sZW5ndGggPT09IHRoaXMucm91bmRzTGlzdC5kYXRhLmxlbmd0aDsKICAgIH0sCgogICAgaXNJbmRldGVybWluYXRlKCkgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZFJvdW5kcy5sZW5ndGggPiAwICYmIHRoaXMuc2VsZWN0ZWRSb3VuZHMubGVuZ3RoIDwgdGhpcy5yb3VuZHNMaXN0LmRhdGEubGVuZ3RoOwogICAgfSwKCiAgICAvLyDoh6rliqjnlJ/miJDova7mrKHlkI3np7AKICAgIGdlbmVyYXRlZFJvdW5kTmFtZSgpIHsKICAgICAgaWYgKCF0aGlzLm5ld1JvdW5kLnN0YXJ0X2RhdGUgfHwgIXRoaXMubmV3Um91bmQuZW5kX2RhdGUpIHsKICAgICAgICByZXR1cm4gJyc7CiAgICAgIH0KCiAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMubmV3Um91bmQuc3RhcnRfZGF0ZSk7CiAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh0aGlzLm5ld1JvdW5kLmVuZF9kYXRlKTsKCiAgICAgIGlmIChzdGFydERhdGUuZ2V0VGltZSgpID09PSBlbmREYXRlLmdldFRpbWUoKSkgewogICAgICAgIC8vIOWNleaXpea0u+WKqAogICAgICAgIGNvbnN0IGRhdGVTdHIgPSBzdGFydERhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICAgIG1vbnRoOiAnbG9uZycsCiAgICAgICAgICBkYXk6ICdudW1lcmljJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBgJHtkYXRlU3RyfeaVtOeCueenkuadgGA7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aSa5pel5rS75YqoCiAgICAgICAgY29uc3Qgc3RhcnRTdHIgPSBzdGFydERhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICAgIG1vbnRoOiAnbG9uZycsCiAgICAgICAgICBkYXk6ICdudW1lcmljJwogICAgICAgIH0pOwogICAgICAgIGNvbnN0IGVuZFN0ciA9IGVuZERhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICAgIG1vbnRoOiAnbG9uZycsCiAgICAgICAgICBkYXk6ICdudW1lcmljJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBgJHtzdGFydFN0cn3oh7Mke2VuZFN0cn3mlbTngrnnp5LmnYBgOwogICAgICB9CiAgICB9LAoKICAgIC8vIOaVtOeCueenkuadgOaXtuautemihOiniO+8iDI05bCP5pe25YWo5aSp5YCZ77yJCiAgICBob3VybHlTbG90UHJldmlldygpIHsKICAgICAgaWYgKCF0aGlzLm5ld1JvdW5kLnN0YXJ0X2RhdGUgfHwgIXRoaXMubmV3Um91bmQuZW5kX2RhdGUpIHsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KCiAgICAgIGNvbnN0IHNsb3RzID0gW107CiAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRoaXMubmV3Um91bmQuc3RhcnRfZGF0ZSk7CiAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZSh0aGlzLm5ld1JvdW5kLmVuZF9kYXRlKTsKCiAgICAgIC8vIOiuvue9rue7k+adn+aXpeacn+S4uuW9k+WkqeeahDIzOjU5OjU5CiAgICAgIGVuZERhdGUuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KTsKCiAgICAgIGxldCBjdXJyZW50RGF0ZSA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7CiAgICAgIGN1cnJlbnREYXRlLnNldEhvdXJzKDAsIDAsIDAsIDApOyAvLyDku44wMDowMOW8gOWniwoKICAgICAgd2hpbGUgKGN1cnJlbnREYXRlIDw9IGVuZERhdGUpIHsKICAgICAgICBmb3IgKGxldCBob3VyID0gMDsgaG91ciA8IDI0OyBob3VyKyspIHsKICAgICAgICAgIGNvbnN0IHNsb3RTdGFydCA9IG5ldyBEYXRlKGN1cnJlbnREYXRlKTsKICAgICAgICAgIHNsb3RTdGFydC5zZXRIb3Vycyhob3VyLCAwLCAwLCAwKTsKCiAgICAgICAgICBjb25zdCBzbG90RW5kID0gbmV3IERhdGUoY3VycmVudERhdGUpOwogICAgICAgICAgc2xvdEVuZC5zZXRIb3Vycyhob3VyLCA1NSwgMCwgMCk7CgogICAgICAgICAgLy8g5qOA5p+l6L2u5qyh5byA5aeL5pe26Ze05piv5ZCm6LaF5Ye657uT5p2f5pel5pyfCiAgICAgICAgICBpZiAoc2xvdFN0YXJ0ID4gZW5kRGF0ZSkgewogICAgICAgICAgICBicmVhazsKICAgICAgICAgIH0KCiAgICAgICAgICBzbG90cy5wdXNoKHsKICAgICAgICAgICAgc3RhcnQ6IHRoaXMuZm9ybWF0TG9jYWxEYXRlVGltZShzbG90U3RhcnQpLAogICAgICAgICAgICBlbmQ6IHRoaXMuZm9ybWF0TG9jYWxEYXRlVGltZShzbG90RW5kKSwKICAgICAgICAgICAgc3RhcnRUaW1lOiBzbG90U3RhcnQsCiAgICAgICAgICAgIGVuZFRpbWU6IHNsb3RFbmQKICAgICAgICAgIH0pOwogICAgICAgIH0KCiAgICAgICAgLy8g56e75Yqo5Yiw5LiL5LiA5aSpCiAgICAgICAgY3VycmVudERhdGUuc2V0RGF0ZShjdXJyZW50RGF0ZS5nZXREYXRlKCkgKyAxKTsKICAgICAgfQogICAgICByZXR1cm4gc2xvdHM7CiAgICB9LAoKICAgIC8vIOacieaViOaXtuauteaVsOmHj++8iOacqui/h+acn+eahOaXtuaute+8iQogICAgdmFsaWRTbG90c0NvdW50KCkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICByZXR1cm4gdGhpcy5ob3VybHlTbG90UHJldmlldy5maWx0ZXIoc2xvdCA9PiBuZXcgRGF0ZShzbG90LnN0YXJ0KSA+IG5vdykubGVuZ3RoOwogICAgfQogIH0sCgogIG1vdW50ZWQoKSB7CiAgICBjb25zb2xlLmxvZygnRmxhc2hTYWxlTXVsdGlQYWdl57uE5Lu25bey5oyC6L29Jyk7CiAgICBjb25zb2xlLmxvZygn5Yid5aeLc2hvd0FkZE1vZGFs5YC8OicsIHRoaXMuc2hvd0FkZE1vZGFsKTsKICAgIHRoaXMubG9hZERhdGEoKTsKICB9LAoKICBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKHRoaXMucmVmcmVzaFRpbWVyKSB7CiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpOwogICAgfQogICAgaWYgKHRoaXMuY291bnRkb3duVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvdW50ZG93blRpbWVyKTsKICAgIH0KICB9LAoKICBtZXRob2RzOiB7CiAgICBhc3luYyBsb2FkRGF0YSgpIHsKICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoWwogICAgICAgIHRoaXMubG9hZFN0YXRpc3RpY3MoKSwKICAgICAgICB0aGlzLmxvYWRDdXJyZW50Um91bmRzKCksCiAgICAgICAgdGhpcy5sb2FkUm91bmRzTGlzdCgpLAogICAgICAgIHRoaXMubG9hZEdvb2RzTGlzdCgpCiAgICAgIF0pOwogICAgfSwKCiAgICBhc3luYyBsb2FkU3RhdGlzdGljcygpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MuZ2V0KCdmbGFzaHNhbGVtdWx0aS9zdGF0aXN0aWNzJyk7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgIHRoaXMuc3RhdGlzdGljcyA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L2957uf6K6h5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkQ3VycmVudFJvdW5kcygpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MuZ2V0KCdmbGFzaHNhbGVtdWx0aS9jdXJyZW50Jyk7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgIHRoaXMuY3VycmVudFJvdW5kcyA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295b2T5YmN6L2u5qyh5aSx6LSlOicsIGVycm9yKTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkUm91bmRzTGlzdCgpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MuZ2V0KCdmbGFzaHNhbGVtdWx0aS9saXN0Jyk7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgIHRoaXMucm91bmRzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296L2u5qyh5YiX6KGo5aSx6LSlOicsIGVycm9yKTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkR29vZHNMaXN0KCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMubG9hZGluZ0dvb2RzID0gdHJ1ZTsKICAgICAgICBjb25zb2xlLmxvZygn5byA5aeL5Yqg6L295ZWG5ZOB5YiX6KGoLi4uJyk7CgogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5heGlvcy5nZXQoJ2ZsYXNoc2FsZW11bHRpL2dvb2RzJyk7CiAgICAgICAgY29uc29sZS5sb2coJ+WVhuWTgeWIl+ihqEFQSeWTjeW6lDonLCByZXNwb25zZS5kYXRhKTsKCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgIHRoaXMuZ29vZHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdOwogICAgICAgICAgY29uc29sZS5sb2coJ+WVhuWTgeWIl+ihqOWKoOi9veaIkOWKn++8jOaVsOmHjzonLCB0aGlzLmdvb2RzTGlzdC5sZW5ndGgpOwoKICAgICAgICAgIGlmICh0aGlzLmdvb2RzTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmmoLml6Dlj6/pgInllYblk4HvvIzor7fnoa7kv53mnInkuIrmnrbnmoTllYblk4HkuJTmnKrlj4LkuI7lhbbku5bnp5LmnYDmtLvliqgnKTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcignQVBJ6L+U5Zue6ZSZ6K+vOicsIHJlc3BvbnNlLmRhdGEuZXJybXNnKTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+WKoOi9veWVhuWTgeWIl+ihqOWksei0pScpOwogICAgICAgICAgdGhpcy5nb29kc0xpc3QgPSBbXTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295ZWG5ZOB5YiX6KGo5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zplJnor6/vvIzor7fmo4Dmn6XmnI3liqHlmajov57mjqUnKTsKICAgICAgICB0aGlzLmdvb2RzTGlzdCA9IFtdOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZ0dvb2RzID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgc3RhcnRBdXRvUmVmcmVzaCgpIHsKICAgICAgdGhpcy5yZWZyZXNoVGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkQ3VycmVudFJvdW5kcygpOwogICAgICAgIHRoaXMubG9hZFN0YXRpc3RpY3MoKTsKICAgICAgfSwgMzAwMDApOyAvLyAzMOenkuWIt+aWsOS4gOasoQogICAgfSwKCiAgICBpc0dvb2RzU2VsZWN0ZWQoZ29vZHNJZCkgewogICAgICByZXR1cm4gdGhpcy5uZXdSb3VuZC5nb29kc19saXN0LnNvbWUoZyA9PiBnLmdvb2RzX2lkID09PSBnb29kc0lkKTsKICAgIH0sCgogICAgZ2V0U2VsZWN0ZWRHb29kcyhnb29kc0lkKSB7CiAgICAgIHJldHVybiB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QuZmluZChnID0+IGcuZ29vZHNfaWQgPT09IGdvb2RzSWQpOwogICAgfSwKCiAgICB0b2dnbGVHb29kcyhnb29kcykgewogICAgICBpZiAoIWdvb2RzLmNhbl9zZWxlY3QpIHJldHVybjsKCiAgICAgIGlmICh0aGlzLmlzR29vZHNTZWxlY3RlZChnb29kcy5pZCkpIHsKICAgICAgICB0aGlzLnJlbW92ZUdvb2RzKGdvb2RzLmlkKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjb25zdCBvcmlnaW5hbFByaWNlID0gcGFyc2VGbG9hdChnb29kcy5yZXRhaWxfcHJpY2UpIHx8IDA7CiAgICAgICAgY29uc3QgZGVmYXVsdERpc2NvdW50ID0gMjA7IC8vIOm7mOiupDIwJeaKmOaJowogICAgICAgIGNvbnN0IGZsYXNoUHJpY2UgPSBNYXRoLnJvdW5kKG9yaWdpbmFsUHJpY2UgKiAoMTAwIC0gZGVmYXVsdERpc2NvdW50KSAvIDEwMCAqIDEwMCkgLyAxMDA7CgogICAgICAgIHRoaXMubmV3Um91bmQuZ29vZHNfbGlzdC5wdXNoKHsKICAgICAgICAgIGdvb2RzX2lkOiBnb29kcy5pZCwKICAgICAgICAgIGdvb2RzX25hbWU6IGdvb2RzLm5hbWUsCiAgICAgICAgICBvcmlnaW5hbF9wcmljZTogb3JpZ2luYWxQcmljZSwKICAgICAgICAgIGZsYXNoX3ByaWNlOiBmbGFzaFByaWNlLAogICAgICAgICAgZGlzY291bnRfcmF0ZTogZGVmYXVsdERpc2NvdW50LAogICAgICAgICAgc3RvY2s6IDEwMCwKICAgICAgICAgIGxpbWl0X3F1YW50aXR5OiAxCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCgogICAgcmVtb3ZlR29vZHMoZ29vZHNJZCkgewogICAgICBjb25zdCBpbmRleCA9IHRoaXMubmV3Um91bmQuZ29vZHNfbGlzdC5maW5kSW5kZXgoZyA9PiBnLmdvb2RzX2lkID09PSBnb29kc0lkKTsKICAgICAgaWYgKGluZGV4ID4gLTEpIHsKICAgICAgICB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3Quc3BsaWNlKGluZGV4LCAxKTsKICAgICAgfQogICAgfSwKCiAgICBjYWxjdWxhdGVEaXNjb3VudFJhdGUob3JpZ2luYWxQcmljZSwgZmxhc2hQcmljZSkgewogICAgICBpZiAoIW9yaWdpbmFsUHJpY2UgfHwgb3JpZ2luYWxQcmljZSA8PSAwIHx8ICFmbGFzaFByaWNlIHx8IGZsYXNoUHJpY2UgPD0gMCkgcmV0dXJuIDA7CiAgICAgIGNvbnN0IHJhdGUgPSBNYXRoLnJvdW5kKCgxIC0gZmxhc2hQcmljZSAvIG9yaWdpbmFsUHJpY2UpICogMTAwKTsKICAgICAgcmV0dXJuIGlzTmFOKHJhdGUpID8gMCA6IHJhdGU7CiAgICB9LAoKICAgIHVwZGF0ZUZsYXNoUHJpY2VCeURpc2NvdW50KGdvb2RzSWQpIHsKICAgICAgY29uc3Qgc2VsZWN0ZWRHb29kcyA9IHRoaXMuZ2V0U2VsZWN0ZWRHb29kcyhnb29kc0lkKTsKICAgICAgaWYgKHNlbGVjdGVkR29vZHMgJiYgc2VsZWN0ZWRHb29kcy5vcmlnaW5hbF9wcmljZSA+IDApIHsKICAgICAgICBjb25zdCBkaXNjb3VudFJhdGUgPSBzZWxlY3RlZEdvb2RzLmRpc2NvdW50X3JhdGUgfHwgMDsKICAgICAgICBjb25zdCBmbGFzaFByaWNlID0gTWF0aC5yb3VuZChzZWxlY3RlZEdvb2RzLm9yaWdpbmFsX3ByaWNlICogKDEwMCAtIGRpc2NvdW50UmF0ZSkgLyAxMDAgKiAxMDApIC8gMTAwOwogICAgICAgIHNlbGVjdGVkR29vZHMuZmxhc2hfcHJpY2UgPSBmbGFzaFByaWNlOwogICAgICB9CiAgICB9LAoKICAgIC8vIOagvOW8j+WMluacrOWcsOaXpeacn+aXtumXtOS4uuWtl+espuS4su+8iOmBv+WFjeaXtuWMuumXrumimO+8iQogICAgZm9ybWF0TG9jYWxEYXRlVGltZShkYXRlKSB7CiAgICAgIGNvbnN0IGQgPSBuZXcgRGF0ZShkYXRlKTsKICAgICAgY29uc3QgeWVhciA9IGQuZ2V0RnVsbFllYXIoKTsKICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZC5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgY29uc3QgZGF5ID0gU3RyaW5nKGQuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBTdHJpbmcoZC5nZXRNaW51dGVzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IHNlY29uZHMgPSBTdHJpbmcoZC5nZXRTZWNvbmRzKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX0gJHtob3Vyc306JHttaW51dGVzfToke3NlY29uZHN9YDsKICAgIH0sCgogICAgLy8g5qC85byP5YyW5pys5Zyw5pel5pyf5Li65a2X56ym5Liy77yI6YG/5YWN5pe25Yy66Zeu6aKY77yJCiAgICBmb3JtYXRMb2NhbERhdGUoZGF0ZSkgewogICAgICBjb25zdCBkID0gbmV3IERhdGUoZGF0ZSk7CiAgICAgIGNvbnN0IHllYXIgPSBkLmdldEZ1bGxZZWFyKCk7CiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGQuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhkLmdldERhdGUoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7CiAgICB9LAoKICAgIGdldEN1cnJlbnREYXRlVGltZSgpIHsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKICAgICAgcmV0dXJuIHRoaXMuZm9ybWF0TG9jYWxEYXRlVGltZShub3cpLnNsaWNlKDAsIDE2KS5yZXBsYWNlKCcgJywgJ1QnKTsKICAgIH0sCgogICAgZ2V0Q3VycmVudERhdGUoKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIHJldHVybiB0aGlzLmZvcm1hdExvY2FsRGF0ZShub3cpOwogICAgfSwKCiAgICBvcGVuQ3JlYXRlTW9kYWwoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfngrnlh7vliJvlu7rmlrDova7mrKHmjInpkq4nKTsKICAgICAgLy8g6K6+572u6buY6K6k5pel5pyf5Li65LuK5aSpCiAgICAgIGNvbnN0IHRvZGF5ID0gdGhpcy5nZXRDdXJyZW50RGF0ZSgpOwogICAgICB0aGlzLm5ld1JvdW5kLnN0YXJ0X2RhdGUgPSB0b2RheTsKICAgICAgdGhpcy5uZXdSb3VuZC5lbmRfZGF0ZSA9IHRvZGF5OwogICAgICB0aGlzLnNob3dBZGRNb2RhbCA9IHRydWU7CiAgICAgIGNvbnNvbGUubG9nKCdzaG93QWRkTW9kYWzorr7nva7kuLo6JywgdGhpcy5zaG93QWRkTW9kYWwpOwogICAgfSwKCiAgICBmb3JtYXREYXRlVGltZShkYXRlVGltZVN0cikgewogICAgICBpZiAoIWRhdGVUaW1lU3RyKSByZXR1cm4gJyc7CiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlVGltZVN0cik7CiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICB5ZWFyOiAnbnVtZXJpYycsCiAgICAgICAgbW9udGg6ICcyLWRpZ2l0JywKICAgICAgICBkYXk6ICcyLWRpZ2l0JywKICAgICAgICBob3VyOiAnMi1kaWdpdCcsCiAgICAgICAgbWludXRlOiAnMi1kaWdpdCcKICAgICAgfSk7CiAgICB9LAoKICAgIGZvcm1hdFNsb3RUaW1lKGRhdGVUaW1lU3RyKSB7CiAgICAgIGlmICghZGF0ZVRpbWVTdHIpIHJldHVybiAnJzsKICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVUaW1lU3RyKTsKICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgewogICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsCiAgICAgICAgaG91cjogJzItZGlnaXQnLAogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnCiAgICAgIH0pOwogICAgfSwKCiAgICBpc1Nsb3RJblBhc3Qoc3RhcnRUaW1lKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IHNsb3RTdGFydCA9IG5ldyBEYXRlKHN0YXJ0VGltZSk7CiAgICAgIHJldHVybiBzbG90U3RhcnQgPCBub3c7CiAgICB9LAoKICAgIGlzU2xvdEFjdGl2ZShzdGFydFRpbWUsIGVuZFRpbWUpIHsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKICAgICAgY29uc3Qgc2xvdFN0YXJ0ID0gbmV3IERhdGUoc3RhcnRUaW1lKTsKICAgICAgY29uc3Qgc2xvdEVuZCA9IG5ldyBEYXRlKGVuZFRpbWUpOwogICAgICByZXR1cm4gbm93ID49IHNsb3RTdGFydCAmJiBub3cgPD0gc2xvdEVuZDsKICAgIH0sCgogICAgYXN5bmMgY3JlYXRlUm91bmQoKSB7CiAgICAgIGlmICghdGhpcy5jYW5DcmVhdGVSb3VuZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+WujOWWhOi9ruasoeS/oeaBrycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g55Sf5oiQ5pW054K556eS5p2A5pe25q615pWw5o2uCiAgICAgIGNvbnN0IGhvdXJseVNsb3RzID0gdGhpcy5ob3VybHlTbG90UHJldmlldzsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKCiAgICAgIC8vIOi/h+a7pOaOieW3suWujOWFqOe7k+adn+eahOaXtuaute+8jOS/neeVmeW9k+WJjeato+WcqOi/m+ihjOeahOWSjOacquadpeeahOaXtuautQogICAgICBjb25zdCB2YWxpZFNsb3RzID0gaG91cmx5U2xvdHMuZmlsdGVyKHNsb3QgPT4gbmV3IERhdGUoc2xvdC5lbmQpID4gbm93KTsKCiAgICAgIGlmICh2YWxpZFNsb3RzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aJgOmAieaXtumXtOauteWGheayoeacieacieaViOeahOenkuadgOaXtuautScpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c6L2u5qyh5pWw6YeP6L+H5aSa77yM6K+i6Zeu55So5oi356Gu6K6kCiAgICAgIGlmICh2YWxpZFNsb3RzLmxlbmd0aCA+IDUwKSB7CiAgICAgICAgY29uc3QgY29uZmlybWVkID0gY29uZmlybShg5bCG6KaB5Yib5bu6JHt2YWxpZFNsb3RzLmxlbmd0aH3kuKrova7mrKHvvIzov5nlj6/og73pnIDopoHovoPplb/ml7bpl7TjgILmmK/lkKbnu6fnu63vvJ9gKTsKICAgICAgICBpZiAoIWNvbmZpcm1lZCkgewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmlzQ3JlYXRpbmcgPSB0cnVlOwogICAgICAgIHRoaXMuY3JlYXRpb25Qcm9ncmVzcy5jdXJyZW50ID0gMDsKICAgICAgICB0aGlzLmNyZWF0aW9uUHJvZ3Jlc3MudG90YWwgPSB2YWxpZFNsb3RzLmxlbmd0aDsKCiAgICAgICAgbGV0IGNyZWF0ZWRDb3VudCA9IDA7CiAgICAgICAgbGV0IGZhaWxlZENvdW50ID0gMDsKICAgICAgICBjb25zdCBmYWlsZWRSZWFzb25zID0gW107CgogICAgICAgIC8vIOaJuemHj+WkhOeQhu+8jOavj+asoeWkhOeQhjEw5Liq6L2u5qyhCiAgICAgICAgY29uc3QgYmF0Y2hTaXplID0gMTA7CiAgICAgICAgZm9yIChsZXQgYmF0Y2hTdGFydCA9IDA7IGJhdGNoU3RhcnQgPCB2YWxpZFNsb3RzLmxlbmd0aDsgYmF0Y2hTdGFydCArPSBiYXRjaFNpemUpIHsKICAgICAgICAgIGNvbnN0IGJhdGNoRW5kID0gTWF0aC5taW4oYmF0Y2hTdGFydCArIGJhdGNoU2l6ZSwgdmFsaWRTbG90cy5sZW5ndGgpOwogICAgICAgICAgY29uc3QgYmF0Y2ggPSB2YWxpZFNsb3RzLnNsaWNlKGJhdGNoU3RhcnQsIGJhdGNoRW5kKTsKCiAgICAgICAgICAvLyDlubbooYzliJvlu7rlvZPliY3mibnmrKHnmoTova7mrKEKICAgICAgICAgIGNvbnN0IGJhdGNoUHJvbWlzZXMgPSBiYXRjaC5tYXAoYXN5bmMgKHNsb3QsIGJhdGNoSW5kZXgpID0+IHsKICAgICAgICAgICAgY29uc3QgZ2xvYmFsSW5kZXggPSBiYXRjaFN0YXJ0ICsgYmF0Y2hJbmRleDsKCiAgICAgICAgICAgIC8vIOmHjeivlemAu+i+kQogICAgICAgICAgICBjb25zdCBtYXhSZXRyaWVzID0gMzsKICAgICAgICAgICAgbGV0IGxhc3RFcnJvciA9IG51bGw7CgogICAgICAgICAgICBmb3IgKGxldCByZXRyeSA9IDA7IHJldHJ5IDwgbWF4UmV0cmllczsgcmV0cnkrKykgewogICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCByb3VuZERhdGEgPSB7CiAgICAgICAgICAgICAgICAgIHJvdW5kX25hbWU6IGAke3RoaXMuZ2VuZXJhdGVkUm91bmROYW1lfSAtIOesrCR7Z2xvYmFsSW5kZXggKyAxfeWcumAsCiAgICAgICAgICAgICAgICAgIHN0YXJ0X3RpbWU6IHNsb3Quc3RhcnQsCiAgICAgICAgICAgICAgICAgIGVuZF90aW1lOiBzbG90LmVuZCwKICAgICAgICAgICAgICAgICAgaXNfaG91cmx5X2ZsYXNoOiB0cnVlLAogICAgICAgICAgICAgICAgICBzbG90X2luZGV4OiBnbG9iYWxJbmRleCArIDEsCiAgICAgICAgICAgICAgICAgIHRvdGFsX3Nsb3RzOiB2YWxpZFNsb3RzLmxlbmd0aCwKICAgICAgICAgICAgICAgICAgZ29vZHNfbGlzdDogdGhpcy5uZXdSb3VuZC5nb29kc19saXN0Lm1hcChnb29kcyA9PiAoewogICAgICAgICAgICAgICAgICAgIGdvb2RzX2lkOiBnb29kcy5nb29kc19pZCwKICAgICAgICAgICAgICAgICAgICBnb29kc19uYW1lOiBnb29kcy5nb29kc19uYW1lLAogICAgICAgICAgICAgICAgICAgIGdvb2RzX2ltYWdlOiBnb29kcy5nb29kc19pbWFnZSwKICAgICAgICAgICAgICAgICAgICBvcmlnaW5hbF9wcmljZTogZ29vZHMub3JpZ2luYWxfcHJpY2UsCiAgICAgICAgICAgICAgICAgICAgZmxhc2hfcHJpY2U6IGdvb2RzLmZsYXNoX3ByaWNlLAogICAgICAgICAgICAgICAgICAgIHN0b2NrOiBnb29kcy5zdG9jaywKICAgICAgICAgICAgICAgICAgICBkaXNjb3VudF9yYXRlOiBnb29kcy5kaXNjb3VudF9yYXRlCiAgICAgICAgICAgICAgICAgIH0pKQogICAgICAgICAgICAgICAgfTsKCiAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MucG9zdCgnZmxhc2hzYWxlbXVsdGkvY3JlYXRlJywgcm91bmREYXRhKTsKCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBpbmRleDogZ2xvYmFsSW5kZXggKyAxIH07CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBsYXN0RXJyb3IgPSByZXNwb25zZS5kYXRhLmVycm1zZzsKICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c5piv6YeN5aSN6ZSu6ZSZ6K+v77yM562J5b6F5LiA5q615pe26Ze05ZCO6YeN6K+VCiAgICAgICAgICAgICAgICAgIGlmIChsYXN0RXJyb3IuaW5jbHVkZXMoJ0R1cGxpY2F0ZSBlbnRyeScpICYmIHJldHJ5IDwgbWF4UmV0cmllcyAtIDEpIHsKICAgICAgICAgICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwICogKHJldHJ5ICsgMSkpKTsKICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgaW5kZXg6IGdsb2JhbEluZGV4ICsgMSwgZXJyb3I6IGxhc3RFcnJvciB9OwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBsYXN0RXJyb3IgPSBlcnJvci5tZXNzYWdlOwogICAgICAgICAgICAgICAgLy8g5aaC5p6c5piv572R57uc6ZSZ6K+v5oiW5pyN5Yqh5Zmo6ZSZ6K+v77yM562J5b6F5ZCO6YeN6K+VCiAgICAgICAgICAgICAgICBpZiAocmV0cnkgPCBtYXhSZXRyaWVzIC0gMSkgewogICAgICAgICAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCAqIChyZXRyeSArIDEpKSk7CiAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQoKICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGluZGV4OiBnbG9iYWxJbmRleCArIDEsIGVycm9yOiBg5Yib5bu65aSx6LSlICjlt7Lph43or5Uke21heFJldHJpZXN95qyhKTogJHtsYXN0RXJyb3J9YCB9OwogICAgICAgICAgfSk7CgogICAgICAgICAgLy8g562J5b6F5b2T5YmN5om55qyh5a6M5oiQCiAgICAgICAgICBjb25zdCBiYXRjaFJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbChiYXRjaFByb21pc2VzKTsKCiAgICAgICAgICAvLyDnu5/orqHnu5PmnpwKICAgICAgICAgIGJhdGNoUmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB7CiAgICAgICAgICAgIHRoaXMuY3JlYXRpb25Qcm9ncmVzcy5jdXJyZW50Kys7CiAgICAgICAgICAgIGlmIChyZXN1bHQuc3VjY2VzcykgewogICAgICAgICAgICAgIGNyZWF0ZWRDb3VudCsrOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGZhaWxlZENvdW50Kys7CiAgICAgICAgICAgICAgaWYgKGZhaWxlZFJlYXNvbnMubGVuZ3RoIDwgNSkgeyAvLyDlj6rorrDlvZXliY015Liq6ZSZ6K+vCiAgICAgICAgICAgICAgICBmYWlsZWRSZWFzb25zLnB1c2goYOesrCR7cmVzdWx0LmluZGV4feWcujogJHtyZXN1bHQuZXJyb3J9YCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyDnn63mmoLlu7bov5/vvIzpgb/lhY3mnI3liqHlmajljovlipvov4flpKcKICAgICAgICAgIGlmIChiYXRjaEVuZCA8IHZhbGlkU2xvdHMubGVuZ3RoKSB7CiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOaYvuekuue7k+aenAogICAgICAgIGlmIChjcmVhdGVkQ291bnQgPiAwKSB7CiAgICAgICAgICBsZXQgbWVzc2FnZSA9IGDmiJDlip/liJvlu7oke2NyZWF0ZWRDb3VudH3kuKrmlbTngrnnp5LmnYDova7mrKFgOwogICAgICAgICAgaWYgKGZhaWxlZENvdW50ID4gMCkgewogICAgICAgICAgICBtZXNzYWdlICs9IGDvvIwke2ZhaWxlZENvdW50feS4quWksei0pWA7CiAgICAgICAgICAgIGlmIChmYWlsZWRSZWFzb25zLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+WIm+W7uuWksei0peeahOi9ruasoTonLCBmYWlsZWRSZWFzb25zKTsKICAgICAgICAgICAgICBtZXNzYWdlICs9IGBcbuS4u+imgemUmeivrzogJHtmYWlsZWRSZWFzb25zWzBdfWA7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhtZXNzYWdlKTsKICAgICAgICAgIHRoaXMuY2xvc2VNb2RhbCgpOwogICAgICAgICAgdGhpcy5sb2FkRGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ+aJgOaciei9ruasoeWIm+W7uuWksei0pSc7CiAgICAgICAgICBpZiAoZmFpbGVkUmVhc29ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGVycm9yTWVzc2FnZSArPSBgXG7plJnor6/kv6Hmga86ICR7ZmFpbGVkUmVhc29uc1swXX1gOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvck1lc3NhZ2UpOwogICAgICAgIH0KCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yib5bu65pW054K556eS5p2A6L2u5qyh5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliJvlu7rov4fnqIvkuK3lj5HnlJ/plJnor686ICcgKyBlcnJvci5tZXNzYWdlKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzQ3JlYXRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLmNyZWF0aW9uUHJvZ3Jlc3MuY3VycmVudCA9IDA7CiAgICAgICAgdGhpcy5jcmVhdGlvblByb2dyZXNzLnRvdGFsID0gMDsKICAgICAgfQogICAgfSwKCiAgICBjbG9zZU1vZGFsKCkgewogICAgICB0aGlzLnNob3dBZGRNb2RhbCA9IGZhbHNlOwogICAgICB0aGlzLm5ld1JvdW5kID0gewogICAgICAgIHN0YXJ0X2RhdGU6ICcnLAogICAgICAgIGVuZF9kYXRlOiAnJywKICAgICAgICBnb29kc19saXN0OiBbXQogICAgICB9OwogICAgfSwKCiAgICB2aWV3Um91bmREZXRhaWxzKHJvdW5kKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3VuZCA9IHJvdW5kOwogICAgICB0aGlzLnNob3dEZXRhaWxNb2RhbCA9IHRydWU7CiAgICB9LAoKICAgIGFzeW5jIGNsb3NlUm91bmQocm91bmQpIHsKICAgICAgaWYgKCFjb25maXJtKGDnoa7lrpropoHlhbPpl63ova7mrKEiJHtyb3VuZC5yb3VuZF9uYW1lfSLlkJfvvJ/lhbPpl63lkI7ova7mrKHlsIbnq4vljbPnu5PmnZ/jgIJgKSkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOWFs+mXrei9ruasoS4uLicpOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5heGlvcy5wb3N0KCdmbGFzaHNhbGVtdWx0aS9jbG9zZScsIHsKICAgICAgICAgIHJvdW5kX2lkOiByb3VuZC5pZAogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfova7mrKHlt7LlhbPpl60nKTsKICAgICAgICAgIHRoaXMubG9hZERhdGEoKTsgLy8g6YeN5paw5Yqg6L295pWw5o2uCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+WFs+mXreWksei0pScpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCflhbPpl63ova7mrKHlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WFs+mXreWksei0pScpOwogICAgICB9CiAgICB9LAoKCgogICAgZm9ybWF0VGltZShzZWNvbmRzKSB7CiAgICAgIGlmICghc2Vjb25kcyB8fCBzZWNvbmRzIDw9IDApIHJldHVybiAnMDA6MDA6MDAnOwogICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDM2MDApOwogICAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlIDM2MDApIC8gNjApOwogICAgICBjb25zdCBzZWNzID0gc2Vjb25kcyAlIDYwOwogICAgICByZXR1cm4gYCR7aG91cnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke21pbnV0ZXMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7CiAgICB9LAoKICAgIGZvcm1hdERhdGVUaW1lKGRhdGVUaW1lKSB7CiAgICAgIGlmICghZGF0ZVRpbWUpIHJldHVybiAnJzsKICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lKS50b0xvY2FsZVN0cmluZygnemgtQ04nKTsKICAgIH0sCgogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICd1cGNvbWluZyc6ICfljbPlsIblvIDlp4snLAogICAgICAgICdhY3RpdmUnOiAn6L+b6KGM5LitJywKICAgICAgICAnZW5kZWQnOiAn5bey57uT5p2fJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzOwogICAgfSwKCiAgICAvLyDojrflj5bnirbmgIHmoLflvI/nsbsKICAgIGdldFN0YXR1c0NsYXNzKHN0YXR1cykgewogICAgICBjb25zdCBjbGFzc01hcCA9IHsKICAgICAgICAndXBjb21pbmcnOiAnYmFkZ2UgYmFkZ2Utd2FybmluZycsCiAgICAgICAgJ2FjdGl2ZSc6ICdiYWRnZSBiYWRnZS1zdWNjZXNzJywKICAgICAgICAnZW5kZWQnOiAnYmFkZ2UgYmFkZ2Utc2Vjb25kYXJ5JwogICAgICB9OwogICAgICByZXR1cm4gY2xhc3NNYXBbc3RhdHVzXSB8fCAnYmFkZ2UgYmFkZ2UtbGlnaHQnOwogICAgfSwKCiAgICAvLyDojrflj5blgJLorqHml7YKICAgIGdldENvdW50ZG93bih0aW1lU3RyKSB7CiAgICAgIGlmICghdGltZVN0cikgcmV0dXJuICcnOwogICAgICBjb25zdCB0YXJnZXRUaW1lID0gbmV3IERhdGUodGltZVN0cik7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IGRpZmYgPSB0YXJnZXRUaW1lIC0gbm93OwoKICAgICAgaWYgKGRpZmYgPD0gMCkgcmV0dXJuICcnOwoKICAgICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKGRpZmYgLyAoMTAwMCAqIDYwICogNjApKTsKICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoKGRpZmYgJSAoMTAwMCAqIDYwICogNjApKSAvICgxMDAwICogNjApKTsKICAgICAgY29uc3Qgc2Vjb25kcyA9IE1hdGguZmxvb3IoKGRpZmYgJSAoMTAwMCAqIDYwKSkgLyAxMDAwKTsKCiAgICAgIGlmIChob3VycyA+IDApIHsKICAgICAgICByZXR1cm4gYCR7aG91cnN95bCP5pe2JHttaW51dGVzfeWIhumSn2A7CiAgICAgIH0gZWxzZSBpZiAobWludXRlcyA+IDApIHsKICAgICAgICByZXR1cm4gYCR7bWludXRlc33liIbpkp8ke3NlY29uZHN956eSYDsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gYCR7c2Vjb25kc33np5JgOwogICAgICB9CiAgICB9LAoKICAgIC8vIOiHquWKqOWIt+aWsOebuOWFs+aWueazlQogICAgdG9nZ2xlQXV0b1JlZnJlc2goKSB7CiAgICAgIGlmICh0aGlzLmF1dG9SZWZyZXNoKSB7CiAgICAgICAgdGhpcy5zdGFydEF1dG9SZWZyZXNoKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zdG9wQXV0b1JlZnJlc2goKTsKICAgICAgfQogICAgfSwKCiAgICBzdGFydEF1dG9SZWZyZXNoKCkgewogICAgICB0aGlzLnN0b3BBdXRvUmVmcmVzaCgpOyAvLyDlhYjlgZzmraLkuYvliY3nmoTlrprml7blmagKCiAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMucmVmcmVzaERhdGEoKTsKICAgICAgfSwgdGhpcy5yZWZyZXNoSW50ZXJ2YWwpOwoKICAgICAgLy8g5ZCv5Yqo5YCS6K6h5pe2CiAgICAgIHRoaXMucmVmcmVzaENvdW50ZG93biA9IHRoaXMucmVmcmVzaEludGVydmFsIC8gMTAwMDsKICAgICAgdGhpcy5jb3VudGRvd25UaW1lciA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICB0aGlzLnJlZnJlc2hDb3VudGRvd24tLTsKICAgICAgICBpZiAodGhpcy5yZWZyZXNoQ291bnRkb3duIDw9IDApIHsKICAgICAgICAgIHRoaXMucmVmcmVzaENvdW50ZG93biA9IHRoaXMucmVmcmVzaEludGVydmFsIC8gMTAwMDsKICAgICAgICB9CiAgICAgIH0sIDEwMDApOwogICAgfSwKCiAgICBzdG9wQXV0b1JlZnJlc2goKSB7CiAgICAgIGlmICh0aGlzLnJlZnJlc2hUaW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5yZWZyZXNoVGltZXIpOwogICAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gbnVsbDsKICAgICAgfQogICAgICBpZiAodGhpcy5jb3VudGRvd25UaW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5jb3VudGRvd25UaW1lcik7CiAgICAgICAgdGhpcy5jb3VudGRvd25UaW1lciA9IG51bGw7CiAgICAgIH0KICAgICAgdGhpcy5yZWZyZXNoQ291bnRkb3duID0gMDsKICAgIH0sCgogICAgYXN5bmMgcmVmcmVzaERhdGEoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRyeSB7CiAgICAgICAgYXdhaXQgdGhpcy5sb2FkRGF0YSgpOwogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIt+aWsOaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5om56YeP5pON5L2c55u45YWz5pa55rOVCiAgICB0b2dnbGVTZWxlY3RBbGwoKSB7CiAgICAgIGlmICh0aGlzLmlzQWxsU2VsZWN0ZWQpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkUm91bmRzID0gW107CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFJvdW5kcyA9IHRoaXMucm91bmRzTGlzdC5kYXRhLm1hcChyb3VuZCA9PiByb3VuZC5pZCk7CiAgICAgIH0KICAgIH0sCgogICAgdXBkYXRlU2VsZWN0aW9uKCkgewogICAgICAvLyDov5nkuKrmlrnms5XkvJrlnKjlpI3pgInmoYbnirbmgIHmlLnlj5jml7boh6rliqjosIPnlKgKICAgIH0sCgogICAgY2xlYXJTZWxlY3Rpb24oKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3VuZHMgPSBbXTsKICAgIH0sCgogICAgYXN5bmMgYmF0Y2hDbG9zZVJvdW5kcygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3VuZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHlhbPpl63nmoTova7mrKEnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgY29uZmlybVJlc3VsdCA9IGF3YWl0IHRoaXMuJGNvbmZpcm0oCiAgICAgICAgICBg56Gu5a6a6KaB5YWz6Zet6YCJ5Lit55qEICR7dGhpcy5zZWxlY3RlZFJvdW5kcy5sZW5ndGh9IOS4qui9ruasoeWQl++8n2AsCiAgICAgICAgICAn5om56YeP5YWz6Zet56Gu6K6kJywKICAgICAgICAgIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICB9CiAgICAgICAgKTsKCiAgICAgICAgaWYgKGNvbmZpcm1SZXN1bHQpIHsKICAgICAgICAgIGxldCBzdWNjZXNzQ291bnQgPSAwOwogICAgICAgICAgbGV0IGZhaWxDb3VudCA9IDA7CgogICAgICAgICAgZm9yIChjb25zdCByb3VuZElkIG9mIHRoaXMuc2VsZWN0ZWRSb3VuZHMpIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuJGh0dHAucG9zdCgnL2FkbWluL2ZsYXNoc2FsZW11bHRpL2Nsb3NlJywgewogICAgICAgICAgICAgICAgcm91bmRfaWQ6IHJvdW5kSWQKICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgICAgICAgIHN1Y2Nlc3NDb3VudCsrOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBmYWlsQ291bnQrKzsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgZmFpbENvdW50Kys7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaJuemHj+WFs+mXreWujOaIkO+8muaIkOWKnyAke3N1Y2Nlc3NDb3VudH0g5Liq77yM5aSx6LSlICR7ZmFpbENvdW50fSDkuKpgKTsKICAgICAgICAgIHRoaXMuY2xlYXJTZWxlY3Rpb24oKTsKICAgICAgICAgIHRoaXMubG9hZERhdGEoKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgaWYgKGVycm9yICE9PSAnY2FuY2VsJykgewogICAgICAgICAgY29uc29sZS5lcnJvcign5om56YeP5YWz6Zet5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aJuemHj+WFs+mXreWksei0pScpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICBhc3luYyBiYXRjaERlbGV0ZVJvdW5kcygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3VuZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliKDpmaTnmoTova7mrKEnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgY29uZmlybVJlc3VsdCA9IGF3YWl0IHRoaXMuJGNvbmZpcm0oCiAgICAgICAgICBg56Gu5a6a6KaB5Yig6Zmk6YCJ5Lit55qEICR7dGhpcy5zZWxlY3RlZFJvdW5kcy5sZW5ndGh9IOS4qui9ruasoeWQl++8n+atpOaTjeS9nOS4jeWPr+aBouWkje+8gVxuXG7ms6jmhI/vvJrov5vooYzkuK3miJbljbPlsIblvIDlp4vnmoTova7mrKHlsIblhYjooqvlhbPpl63vvIznhLblkI7liKDpmaTjgIJgLAogICAgICAgICAgJ+aJuemHj+WIoOmZpOehruiupCcsCiAgICAgICAgICB7CiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgfQogICAgICAgICk7CgogICAgICAgIGlmIChjb25maXJtUmVzdWx0KSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOaJuemHj+WIoOmZpOi9ruasoS4uLicpOwoKICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCdmbGFzaHNhbGVtdWx0aS9iYXRjaERlbGV0ZScsIHsKICAgICAgICAgICAgcm91bmRfaWRzOiB0aGlzLnNlbGVjdGVkUm91bmRzCiAgICAgICAgICB9KTsKCiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+aJuemHj+WIoOmZpOWujOaIkCcpOwogICAgICAgICAgICB0aGlzLmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgICAgIHRoaXMubG9hZERhdGEoKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+aJuemHj+WIoOmZpOWksei0pScpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBpZiAoZXJyb3IgIT09ICdjYW5jZWwnKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/liKDpmaTlpLHotKU6JywgZXJyb3IpOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5om56YeP5Yig6Zmk5aSx6LSlJyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOW7tuacn+i9ruasoeebuOWFs+aWueazlQogICAgc2hvd0V4dGVuZE1vZGFsKHJvdW5kKSB7CiAgICAgIHRoaXMuZXh0ZW5kUm91bmQgPSByb3VuZDsKICAgICAgdGhpcy5leHRlbmRNaW51dGVzID0gbnVsbDsKICAgICAgdGhpcy5zaG93RXh0ZW5kTW9kYWxGbGFnID0gdHJ1ZTsKICAgIH0sCgogICAgY2xvc2VFeHRlbmRNb2RhbCgpIHsKICAgICAgdGhpcy5zaG93RXh0ZW5kTW9kYWxGbGFnID0gZmFsc2U7CiAgICAgIHRoaXMuZXh0ZW5kUm91bmQgPSBudWxsOwogICAgICB0aGlzLmV4dGVuZE1pbnV0ZXMgPSBudWxsOwogICAgfSwKCiAgICBhc3luYyBjb25maXJtRXh0ZW5kKCkgewogICAgICBpZiAoIXRoaXMuZXh0ZW5kTWludXRlcyB8fCB0aGlzLmV4dGVuZE1pbnV0ZXMgPD0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl5pyJ5pWI55qE5bu25pyf5pe26Ze0Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcvYWRtaW4vZmxhc2hzYWxlbXVsdGkvZXh0ZW5kJywgewogICAgICAgICAgcm91bmRfaWQ6IHRoaXMuZXh0ZW5kUm91bmQuaWQsCiAgICAgICAgICBleHRlbmRfbWludXRlczogdGhpcy5leHRlbmRNaW51dGVzCiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOi9ruasoeW3suW7tuacnyR7dGhpcy5leHRlbmRNaW51dGVzfeWIhumSn2ApOwogICAgICAgICAgdGhpcy5jbG9zZUV4dGVuZE1vZGFsKCk7CiAgICAgICAgICB0aGlzLmxvYWREYXRhKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+W7tuacn+Wksei0pScpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCflu7bmnJ/ova7mrKHlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W7tuacn+Wksei0pScpOwogICAgICB9CiAgICB9LAoKICAgIC8vIOmHjeWQr+i9ruasoeebuOWFs+aWueazlQogICAgc2hvd1Jlc3RhcnRNb2RhbChyb3VuZCkgewogICAgICB0aGlzLnJlc3RhcnRSb3VuZCA9IHJvdW5kOwogICAgICAvLyDorr7nva7pu5jorqTml7bpl7TkuLrlvZPliY3ml7bpl7TlkI4x5bCP5pe25ZKMMuWwj+aXtgogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBvbmVIb3VyTGF0ZXIgPSBuZXcgRGF0ZShub3cuZ2V0VGltZSgpICsgNjAgKiA2MCAqIDEwMDApOwogICAgICBjb25zdCB0d29Ib3Vyc0xhdGVyID0gbmV3IERhdGUobm93LmdldFRpbWUoKSArIDIgKiA2MCAqIDYwICogMTAwMCk7CgogICAgICB0aGlzLm5ld1N0YXJ0VGltZSA9IHRoaXMuZm9ybWF0RGF0ZVRpbWVMb2NhbChvbmVIb3VyTGF0ZXIpOwogICAgICB0aGlzLm5ld0VuZFRpbWUgPSB0aGlzLmZvcm1hdERhdGVUaW1lTG9jYWwodHdvSG91cnNMYXRlcik7CiAgICAgIHRoaXMuc2hvd1Jlc3RhcnRNb2RhbEZsYWcgPSB0cnVlOwogICAgfSwKCiAgICBjbG9zZVJlc3RhcnRNb2RhbCgpIHsKICAgICAgdGhpcy5zaG93UmVzdGFydE1vZGFsRmxhZyA9IGZhbHNlOwogICAgICB0aGlzLnJlc3RhcnRSb3VuZCA9IG51bGw7CiAgICAgIHRoaXMubmV3U3RhcnRUaW1lID0gJyc7CiAgICAgIHRoaXMubmV3RW5kVGltZSA9ICcnOwogICAgfSwKCiAgICBhc3luYyBjb25maXJtUmVzdGFydCgpIHsKICAgICAgaWYgKCF0aGlzLm5ld1N0YXJ0VGltZSB8fCAhdGhpcy5uZXdFbmRUaW1lKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7forr7nva7lvIDlp4vlkoznu5PmnZ/ml7bpl7QnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGlmIChuZXcgRGF0ZSh0aGlzLm5ld1N0YXJ0VGltZSkgPj0gbmV3IERhdGUodGhpcy5uZXdFbmRUaW1lKSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5byA5aeL5pe26Ze05b+F6aG75pep5LqO57uT5p2f5pe26Ze0Jyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0cnkgewogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCcvYWRtaW4vZmxhc2hzYWxlbXVsdGkvcmVzdGFydCcsIHsKICAgICAgICAgIHJvdW5kX2lkOiB0aGlzLnJlc3RhcnRSb3VuZC5pZCwKICAgICAgICAgIG5ld19zdGFydF90aW1lOiB0aGlzLm5ld1N0YXJ0VGltZSwKICAgICAgICAgIG5ld19lbmRfdGltZTogdGhpcy5uZXdFbmRUaW1lCiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+i9ruasoeW3sumHjeaWsOWQr+WKqCcpOwogICAgICAgICAgdGhpcy5jbG9zZVJlc3RhcnRNb2RhbCgpOwogICAgICAgICAgdGhpcy5sb2FkRGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLmRhdGEuZXJybXNnIHx8ICfph43lkK/lpLHotKUnKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6YeN5ZCv6L2u5qyh5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfph43lkK/lpLHotKUnKTsKICAgICAgfQogICAgfSwKCiAgICAvLyDlpI3liLbova7mrKHnm7jlhbPmlrnms5UKICAgIHNob3dDb3B5TW9kYWwocm91bmQpIHsKICAgICAgdGhpcy5jb3B5Um91bmQgPSByb3VuZDsKICAgICAgdGhpcy5uZXdSb3VuZE5hbWUgPSByb3VuZC5yb3VuZF9uYW1lICsgJyAo5aSN5Yi2KSc7CgogICAgICAvLyDorr7nva7pu5jorqTml7bpl7TkuLrlvZPliY3ml7bpl7TlkI4x5bCP5pe25ZKMMuWwj+aXtgogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBvbmVIb3VyTGF0ZXIgPSBuZXcgRGF0ZShub3cuZ2V0VGltZSgpICsgNjAgKiA2MCAqIDEwMDApOwogICAgICBjb25zdCB0d29Ib3Vyc0xhdGVyID0gbmV3IERhdGUobm93LmdldFRpbWUoKSArIDIgKiA2MCAqIDYwICogMTAwMCk7CgogICAgICB0aGlzLmNvcHlTdGFydFRpbWUgPSB0aGlzLmZvcm1hdERhdGVUaW1lTG9jYWwob25lSG91ckxhdGVyKTsKICAgICAgdGhpcy5jb3B5RW5kVGltZSA9IHRoaXMuZm9ybWF0RGF0ZVRpbWVMb2NhbCh0d29Ib3Vyc0xhdGVyKTsKICAgICAgdGhpcy5zaG93Q29weU1vZGFsRmxhZyA9IHRydWU7CiAgICB9LAoKICAgIGNsb3NlQ29weU1vZGFsKCkgewogICAgICB0aGlzLnNob3dDb3B5TW9kYWxGbGFnID0gZmFsc2U7CiAgICAgIHRoaXMuY29weVJvdW5kID0gbnVsbDsKICAgICAgdGhpcy5uZXdSb3VuZE5hbWUgPSAnJzsKICAgICAgdGhpcy5jb3B5U3RhcnRUaW1lID0gJyc7CiAgICAgIHRoaXMuY29weUVuZFRpbWUgPSAnJzsKICAgIH0sCgogICAgYXN5bmMgY29uZmlybUNvcHkoKSB7CiAgICAgIGlmICghdGhpcy5jb3B5U3RhcnRUaW1lIHx8ICF0aGlzLmNvcHlFbmRUaW1lKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7forr7nva7lvIDlp4vlkoznu5PmnZ/ml7bpl7QnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGlmIChuZXcgRGF0ZSh0aGlzLmNvcHlTdGFydFRpbWUpID49IG5ldyBEYXRlKHRoaXMuY29weUVuZFRpbWUpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvIDlp4vml7bpl7Tlv4Xpobvml6nkuo7nu5PmnZ/ml7bpl7QnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLiRodHRwLnBvc3QoJy9hZG1pbi9mbGFzaHNhbGVtdWx0aS9jb3B5JywgewogICAgICAgICAgcm91bmRfaWQ6IHRoaXMuY29weVJvdW5kLmlkLAogICAgICAgICAgbmV3X3JvdW5kX25hbWU6IHRoaXMubmV3Um91bmROYW1lLAogICAgICAgICAgbmV3X3N0YXJ0X3RpbWU6IHRoaXMuY29weVN0YXJ0VGltZSwKICAgICAgICAgIG5ld19lbmRfdGltZTogdGhpcy5jb3B5RW5kVGltZQogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfova7mrKHlpI3liLbmiJDlip8nKTsKICAgICAgICAgIHRoaXMuY2xvc2VDb3B5TW9kYWwoKTsKICAgICAgICAgIHRoaXMubG9hZERhdGEoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5kYXRhLmVycm1zZyB8fCAn5aSN5Yi25aSx6LSlJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WkjeWItui9ruasoeWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5aSN5Yi25aSx6LSlJyk7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5Yig6Zmk6L2u5qyhCiAgICBhc3luYyBkZWxldGVSb3VuZChyb3VuZCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHN0YXR1c1RleHQgPSB7CiAgICAgICAgICAndXBjb21pbmcnOiAn5Y2z5bCG5byA5aeLJywKICAgICAgICAgICdhY3RpdmUnOiAn6L+b6KGM5LitJywKICAgICAgICAgICdlbmRlZCc6ICflt7Lnu5PmnZ8nCiAgICAgICAgfTsKCiAgICAgICAgbGV0IGNvbmZpcm1NZXNzYWdlID0gYOehruWumuimgeWIoOmZpOi9ruasoSIke3JvdW5kLnJvdW5kX25hbWV9IuWQl++8n+atpOaTjeS9nOS4jeWPr+aBouWkje+8gWA7CgogICAgICAgIGlmIChyb3VuZC5zdGF0dXMgPT09ICdhY3RpdmUnIHx8IHJvdW5kLnN0YXR1cyA9PT0gJ3VwY29taW5nJykgewogICAgICAgICAgY29uZmlybU1lc3NhZ2UgKz0gYFxuXG7ms6jmhI/vvJror6Xova7mrKHlvZPliY3nirbmgIHkuLoiJHtzdGF0dXNUZXh0W3JvdW5kLnN0YXR1c119Iu+8jOWIoOmZpOWJjeWwhuWFiOiHquWKqOWFs+mXrei9ruasoeOAgmA7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBjb25maXJtUmVzdWx0ID0gYXdhaXQgdGhpcy4kY29uZmlybSgKICAgICAgICAgIGNvbmZpcm1NZXNzYWdlLAogICAgICAgICAgJ+WIoOmZpOi9ruasoeehruiupCcsCiAgICAgICAgICB7CiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5Yig6ZmkJywKICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgfQogICAgICAgICk7CgogICAgICAgIGlmIChjb25maXJtUmVzdWx0KSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOWIoOmZpOi9ruasoS4uLicpOwoKICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy4kaHR0cC5wb3N0KCdmbGFzaHNhbGVtdWx0aS9kZWxldGUnLCB7CiAgICAgICAgICAgIHJvdW5kX2lkOiByb3VuZC5pZAogICAgICAgICAgfSk7CgogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfova7mrKHliKDpmaTmiJDlip8nKTsKICAgICAgICAgICAgdGhpcy5sb2FkRGF0YSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5kYXRhLmVycm1zZyB8fCAn5Yig6Zmk5aSx6LSlJyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGlmIChlcnJvciAhPT0gJ2NhbmNlbCcpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOi9ruasoeWksei0pTonLCBlcnJvcik7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKUnKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLy8g57yW6L6R6L2u5qyhCiAgICBlZGl0Um91bmQocm91bmQpIHsKICAgICAgLy8g6L+Z6YeM5Y+v5Lul6Lez6L2s5Yiw57yW6L6R6aG16Z2i5oiW5omT5byA57yW6L6R5qih5oCB5qGGCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn57yW6L6R5Yqf6IO95b6F5a6e546wJyk7CiAgICB9LAoKICAgIC8vIOagvOW8j+WMluaXpeacn+aXtumXtOS4uuacrOWcsOagvOW8j++8iOeUqOS6jmRhdGV0aW1lLWxvY2Fs6L6T5YWl5qGG77yJCiAgICBmb3JtYXREYXRlVGltZUxvY2FsKGRhdGUpIHsKICAgICAgY29uc3QgZCA9IG5ldyBEYXRlKGRhdGUpOwogICAgICBjb25zdCB5ZWFyID0gZC5nZXRGdWxsWWVhcigpOwogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhkLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpOwogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZC5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGQuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTsKICAgICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhkLmdldE1pbnV0ZXMoKSkucGFkU3RhcnQoMiwgJzAnKTsKCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1UJHtob3Vyc306JHttaW51dGVzfWA7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["FlashSaleMultiPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FlashSaleMultiPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"flash-sale-multi-page\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>限时秒杀管理（多商品轮次）</h2>\n      <button @click=\"openCreateModal\" class=\"btn btn-primary\">\n        创建新轮次\n      </button>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <h3>总轮次</h3>\n        <p class=\"stat-number\">{{ statistics.totalRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>进行中</h3>\n        <p class=\"stat-number active\">{{ statistics.activeRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>即将开始</h3>\n        <p class=\"stat-number upcoming\">{{ statistics.upcomingRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>总销售额</h3>\n        <p class=\"stat-number\">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>\n      </div>\n    </div>\n\n    <!-- 当前轮次 -->\n    <div class=\"current-rounds\" v-if=\"currentRounds.current && currentRounds.current.length > 0\">\n      <h3>当前进行中的轮次</h3>\n      <div class=\"round-list\">\n        <div v-for=\"round in currentRounds.current\" :key=\"round.id\" class=\"round-item active\">\n          <div class=\"round-info\">\n            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>\n            <p>商品数量: {{ round.goods_count }}</p>\n            <p>剩余时间: {{ formatTime(round.countdown) }}</p>\n          </div>\n          <div class=\"goods-preview\">\n            <div v-for=\"goods in round.goods_list.slice(0, 3)\" :key=\"goods.id\" class=\"goods-item\">\n              <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n              <span>{{ goods.goods_name }}</span>\n              <span class=\"price\">¥{{ goods.flash_price }}</span>\n            </div>\n            <span v-if=\"round.goods_list.length > 3\" class=\"more-goods\">\n              +{{ round.goods_list.length - 3 }}个商品\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次列表 -->\n    <div class=\"rounds-table\">\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\n        <h3>轮次列表</h3>\n        <div class=\"table-controls\">\n          <div class=\"auto-refresh-control\">\n            <label class=\"form-check-label\">\n              <input\n                type=\"checkbox\"\n                v-model=\"autoRefresh\"\n                @change=\"toggleAutoRefresh\"\n                class=\"form-check-input\"\n              >\n              自动刷新 ({{ refreshInterval / 1000 }}秒)\n            </label>\n            <span v-if=\"autoRefresh\" class=\"refresh-countdown\">\n              下次刷新: {{ refreshCountdown }}秒\n            </span>\n          </div>\n          <button @click=\"refreshData\" class=\"btn btn-secondary btn-sm\">\n            <i class=\"fa fa-refresh\" :class=\"{ 'fa-spin': loading }\"></i>\n            手动刷新\n          </button>\n        </div>\n      </div>\n\n      <!-- 批量操作区域 -->\n      <div v-if=\"selectedRounds.length > 0\" class=\"batch-operations mb-3\">\n        <div class=\"alert alert-info\">\n          已选择 {{ selectedRounds.length }} 个轮次\n          <div class=\"batch-actions\">\n            <button @click=\"batchCloseRounds\" class=\"btn btn-warning btn-sm\">批量关闭</button>\n            <button @click=\"batchDeleteRounds\" class=\"btn btn-danger btn-sm\">批量删除</button>\n            <button @click=\"clearSelection\" class=\"btn btn-secondary btn-sm\">取消选择</button>\n          </div>\n        </div>\n      </div>\n\n      <table class=\"table\">\n        <thead>\n          <tr>\n            <th>\n              <input\n                type=\"checkbox\"\n                @change=\"toggleSelectAll\"\n                :checked=\"isAllSelected\"\n                :indeterminate.prop=\"isIndeterminate\"\n              >\n            </th>\n            <th>轮次编号</th>\n            <th>轮次名称</th>\n            <th>商品数量</th>\n            <th>总库存</th>\n            <th>已售出</th>\n            <th>开始时间</th>\n            <th>结束时间</th>\n            <th>状态</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n            <td>\n              <input\n                type=\"checkbox\"\n                :value=\"round.id\"\n                v-model=\"selectedRounds\"\n                @change=\"updateSelection\"\n              >\n            </td>\n            <td>#{{ round.round_number }}</td>\n            <td>{{ round.round_name }}</td>\n            <td>{{ round.goods_count }}</td>\n            <td>{{ round.total_stock }}</td>\n            <td>{{ round.total_sold }}</td>\n            <td>{{ formatDateTime(round.start_time) }}</td>\n            <td>{{ formatDateTime(round.end_time) }}</td>\n            <td>\n              <div class=\"status-info\">\n                <span :class=\"getStatusClass(round.status)\">{{ getStatusText(round.status) }}</span>\n                <div class=\"time-info\">\n                  <small v-if=\"round.status === 'upcoming'\">\n                    开始时间: {{ formatTime(round.start_time) }}\n                    <span v-if=\"getCountdown(round.start_time)\" class=\"countdown\">\n                      ({{ getCountdown(round.start_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'active'\">\n                    结束时间: {{ formatTime(round.end_time) }}\n                    <span v-if=\"getCountdown(round.end_time)\" class=\"countdown text-danger\">\n                      ({{ getCountdown(round.end_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'ended'\">\n                    已于 {{ formatTime(round.end_time) }} 结束\n                  </small>\n                </div>\n              </div>\n            </td>\n            <td>\n              <div class=\"round-actions\">\n                <button @click=\"viewRoundDetails(round)\" class=\"btn btn-info btn-sm\">查看详情</button>\n\n                <!-- 即将开始的轮次 -->\n                <template v-if=\"round.status === 'upcoming'\">\n                  <button @click=\"editRound(round)\" class=\"btn btn-success btn-sm\">编辑</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">取消轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 进行中的轮次 -->\n                <template v-if=\"round.status === 'active'\">\n                  <button @click=\"showExtendModal(round)\" class=\"btn btn-primary btn-sm\">延期</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">立即结束</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 已结束的轮次 -->\n                <template v-if=\"round.status === 'ended'\">\n                  <button @click=\"showRestartModal(round)\" class=\"btn btn-success btn-sm\">重新启动</button>\n                  <button @click=\"showCopyModal(round)\" class=\"btn btn-secondary btn-sm\">复制轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 延期轮次模态框 -->\n    <div v-if=\"showExtendModalFlag\" class=\"modal-overlay\" @click=\"closeExtendModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>延期轮次</h4>\n          <button @click=\"closeExtendModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ extendRound && extendRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>延期时间 (分钟):</label>\n            <input\n              type=\"number\"\n              v-model=\"extendMinutes\"\n              class=\"form-control\"\n              min=\"1\"\n              max=\"1440\"\n              placeholder=\"请输入延期分钟数\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmExtend\" class=\"btn btn-primary\" :disabled=\"!extendMinutes\">确认延期</button>\n          <button @click=\"closeExtendModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重新启动轮次模态框 -->\n    <div v-if=\"showRestartModalFlag\" class=\"modal-overlay\" @click=\"closeRestartModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>重新启动轮次</h4>\n          <button @click=\"closeRestartModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ restartRound && restartRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>新结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmRestart\" class=\"btn btn-primary\">确认重启</button>\n          <button @click=\"closeRestartModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 复制轮次模态框 -->\n    <div v-if=\"showCopyModalFlag\" class=\"modal-overlay\" @click=\"closeCopyModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>复制轮次</h4>\n          <button @click=\"closeCopyModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>源轮次: {{ copyRound && copyRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新轮次名称:</label>\n            <input\n              type=\"text\"\n              v-model=\"newRoundName\"\n              class=\"form-control\"\n              :placeholder=\"copyRound && copyRound.round_name ? copyRound.round_name + ' (复制)' : ''\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmCopy\" class=\"btn btn-primary\">确认复制</button>\n          <button @click=\"closeCopyModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>创建新轮次</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <!-- 整点秒杀时间设置 -->\n          <div class=\"hourly-flash-settings\">\n            <div class=\"setting-header\">\n              <h4>整点秒杀设置</h4>\n              <p class=\"setting-description\">每小时整点开始，持续55分钟，24小时不间断</p>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label>活动开始日期 *</label>\n                <input\n                  v-model=\"newRound.start_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  required\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>活动结束日期 *</label>\n                <input\n                  v-model=\"newRound.end_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  :min=\"newRound.start_date\"\n                  required\n                />\n              </div>\n            </div>\n\n            <!-- 自动生成的轮次名称预览 -->\n            <div v-if=\"generatedRoundName\" class=\"round-name-preview\">\n              <h5>轮次名称：</h5>\n              <div class=\"name-display\">{{ generatedRoundName }}</div>\n            </div>\n\n            <!-- 时段预览 -->\n            <div v-if=\"hourlySlotPreview.length > 0\" class=\"slot-preview\">\n              <h5>将生成以下秒杀时段：</h5>\n              <div class=\"slot-list-container\">\n                <div class=\"slot-list\">\n                  <div v-for=\"(slot, index) in hourlySlotPreview\" :key=\"index\" class=\"slot-item\">\n                    <span class=\"slot-number\">第{{ index + 1 }}场</span>\n                    <span class=\"slot-time\">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>\n                    <span class=\"slot-duration\">55分钟</span>\n                    <span v-if=\"isSlotInPast(slot.start)\" class=\"slot-status past\">已过期</span>\n                    <span v-else-if=\"isSlotActive(slot.start, slot.end)\" class=\"slot-status active\">进行中</span>\n                    <span v-else class=\"slot-status upcoming\">待开始</span>\n                  </div>\n                  <div v-if=\"hourlySlotPreview.length > 10\" class=\"more-slots\">\n                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段\n                  </div>\n                </div>\n              </div>\n              <div class=\"slot-summary\">\n                <span>共 {{ hourlySlotPreview.length }} 个时段</span>\n                <span class=\"valid-slots\">（有效时段：{{ validSlotsCount }} 个）</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 商品选择 -->\n          <div class=\"form-group\">\n            <label>选择商品 * (点击商品卡片进行选择)</label>\n\n            <!-- 加载状态 -->\n            <div v-if=\"loadingGoods\" class=\"loading-state\">\n              <i class=\"loading-icon\">⏳</i>\n              <span>正在加载商品列表...</span>\n            </div>\n\n            <!-- 商品列表 -->\n            <div v-else-if=\"goodsList && goodsList.length > 0\" class=\"goods-selection-grid\">\n              <div\n                v-for=\"goods in goodsList\"\n                :key=\"goods.id\"\n                class=\"goods-card\"\n                :class=\"{\n                  'selected': isGoodsSelected(goods.id),\n                  'disabled': !goods.can_select\n                }\"\n                @click=\"toggleGoods(goods)\"\n              >\n                <!-- 商品基本信息 -->\n                <div class=\"goods-card-header\">\n                  <div class=\"goods-image-container\">\n                    <img :src=\"goods.list_pic_url\" :alt=\"goods.name\" class=\"goods-card-image\" />\n                    <div v-if=\"isGoodsSelected(goods.id)\" class=\"selected-badge\">\n                      <i class=\"checkmark\">✓</i>\n                    </div>\n                    <div v-if=\"!goods.can_select\" class=\"disabled-overlay\">\n                      <span>已参与其他秒杀</span>\n                    </div>\n                  </div>\n                  <div class=\"goods-card-info\">\n                    <h4 class=\"goods-name\">{{ goods.name }}</h4>\n                    <p class=\"original-price\">原价: ¥{{ goods.retail_price }}</p>\n                    <div v-if=\"!goods.can_select\" class=\"warning-text\">\n                      <i class=\"warning-icon\">⚠</i>\n                      <span>已参与其他秒杀活动</span>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 秒杀设置面板 -->\n                <div v-if=\"isGoodsSelected(goods.id)\" class=\"goods-settings-panel\">\n                  <div class=\"settings-title\">秒杀设置</div>\n                  <div class=\"settings-grid\">\n                    <div class=\"setting-item full-width\">\n                      <label>折扣设置</label>\n                      <div class=\"discount-setting\">\n                        <div class=\"discount-input-group\">\n                          <input\n                            v-model.number=\"getSelectedGoods(goods.id).discount_rate\"\n                            type=\"number\"\n                            step=\"1\"\n                            min=\"10\"\n                            max=\"90\"\n                            class=\"discount-input\"\n                            @input=\"updateFlashPriceByDiscount(goods.id)\"\n                            @click.stop\n                          />\n                          <span class=\"discount-unit\">% OFF</span>\n                        </div>\n                        <div class=\"price-preview\">\n                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →\n                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}\n                        </div>\n                        <div class=\"price-range-hint\" v-if=\"goods.price_range\">\n                          商品价格区间: {{ goods.price_range }}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>秒杀库存</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).stock\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"9999\"\n                        class=\"stock-input\"\n                        @click.stop\n                      />\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>限购数量</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).limit_quantity\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"99\"\n                        class=\"limit-input\"\n                        @click.stop\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 空状态显示 -->\n            <div v-else class=\"empty-state\">\n              <div class=\"empty-icon\">📦</div>\n              <p class=\"empty-text\">暂无可选商品</p>\n              <p class=\"empty-hint\">请确保有上架的商品，且未参与其他秒杀活动</p>\n              <button @click=\"loadGoodsList\" class=\"btn btn-secondary btn-sm\">重新加载</button>\n            </div>\n\n            <!-- 选择提示 -->\n            <div class=\"selection-hint\">\n              <p v-if=\"newRound.goods_list.length === 0\" class=\"hint-text\">\n                <i class=\"info-icon\">ℹ</i>\n                请点击商品卡片选择参与秒杀的商品，可以选择多个商品\n              </p>\n              <p v-else class=\"selected-count\">\n                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品\n              </p>\n            </div>\n          </div>\n\n          <!-- 已选商品汇总 -->\n          <div v-if=\"newRound.goods_list.length > 0\" class=\"selected-summary\">\n            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>\n            <div class=\"summary-list\">\n              <div v-for=\"goods in newRound.goods_list\" :key=\"goods.goods_id\" class=\"summary-item\">\n                <span>{{ goods.goods_name }}</span>\n                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>\n                <span>库存: {{ goods.stock }}</span>\n                <button @click=\"removeGoods(goods.goods_id)\" class=\"remove-btn\">移除</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"modal-footer\">\n          <button @click=\"closeModal\" class=\"btn btn-secondary\">取消</button>\n          <button\n            @click=\"createRound\"\n            class=\"btn btn-primary\"\n            :disabled=\"!canCreateRound || isCreating\"\n          >\n            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次详情模态框 -->\n    <div v-if=\"showDetailModal\" class=\"modal-overlay\" @click=\"showDetailModal = false\">\n      <div class=\"modal-content large\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>\n          <button @click=\"showDetailModal = false\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"round-details\">\n            <div class=\"detail-section\">\n              <h4>基本信息</h4>\n              <p>轮次编号: #{{ selectedRound.round_number }}</p>\n              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>\n              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>\n              <p>状态: {{ getStatusText(selectedRound.status) }}</p>\n            </div>\n            \n            <div class=\"detail-section\">\n              <h4>商品列表</h4>\n              <table class=\"table\">\n                <thead>\n                  <tr>\n                    <th>商品</th>\n                    <th>原价</th>\n                    <th>秒杀价</th>\n                    <th>折扣</th>\n                    <th>库存</th>\n                    <th>已售</th>\n                    <th>限购</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"goods in selectedRound.goods_list\" :key=\"goods.id\">\n                    <td>\n                      <div class=\"goods-cell\">\n                        <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n                        <span>{{ goods.goods_name }}</span>\n                      </div>\n                    </td>\n                    <td>¥{{ goods.original_price }}</td>\n                    <td>¥{{ goods.flash_price }}</td>\n                    <td>{{ goods.discount_rate }}%</td>\n                    <td>{{ goods.stock }}</td>\n                    <td>{{ goods.sold_count }}</td>\n                    <td>{{ goods.limit_quantity }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleMultiPage',\n  data() {\n    return {\n      statistics: {},\n      currentRounds: { current: [], upcoming: [] },\n      roundsList: { data: [], count: 0 },\n      goodsList: [],\n      loadingGoods: false,\n      showAddModal: false,\n      showDetailModal: false,\n      selectedRound: {},\n      isCreating: false,\n      newRound: {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      },\n      refreshTimer: null,\n      creationProgress: {\n        current: 0,\n        total: 0\n      },\n\n      // 批量操作相关\n      selectedRounds: [],\n\n      // 自动刷新相关\n      autoRefresh: false,\n      refreshInterval: 30000, // 30秒\n      refreshCountdown: 0,\n      countdownTimer: null,\n      loading: false,\n\n      // 延期模态框\n      showExtendModalFlag: false,\n      extendRound: null,\n      extendMinutes: null,\n\n      // 重启模态框\n      showRestartModalFlag: false,\n      restartRound: null,\n      newStartTime: '',\n      newEndTime: '',\n\n      // 复制模态框\n      showCopyModalFlag: false,\n      copyRound: null,\n      newRoundName: '',\n      copyStartTime: '',\n      copyEndTime: ''\n    };\n  },\n  \n  computed: {\n    canCreateRound() {\n      const hasStartDate = this.newRound.start_date;\n      const hasEndDate = this.newRound.end_date;\n      const hasGoods = this.newRound.goods_list.length > 0;\n      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);\n      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);\n\n      console.log('canCreateRound检查:', {\n        hasStartDate,\n        hasEndDate,\n        hasGoods,\n        goodsValid,\n        dateValid,\n        goodsList: this.newRound.goods_list\n      });\n\n      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;\n    },\n\n    // 批量选择相关计算属性\n    isAllSelected() {\n      return this.roundsList.data.length > 0 && this.selectedRounds.length === this.roundsList.data.length;\n    },\n\n    isIndeterminate() {\n      return this.selectedRounds.length > 0 && this.selectedRounds.length < this.roundsList.data.length;\n    },\n\n    // 自动生成轮次名称\n    generatedRoundName() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return '';\n      }\n\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      if (startDate.getTime() === endDate.getTime()) {\n        // 单日活动\n        const dateStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${dateStr}整点秒杀`;\n      } else {\n        // 多日活动\n        const startStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        const endStr = endDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${startStr}至${endStr}整点秒杀`;\n      }\n    },\n\n    // 整点秒杀时段预览（24小时全天候）\n    hourlySlotPreview() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return [];\n      }\n\n      const slots = [];\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      // 设置结束日期为当天的23:59:59\n      endDate.setHours(23, 59, 59, 999);\n\n      let currentDate = new Date(startDate);\n      currentDate.setHours(0, 0, 0, 0); // 从00:00开始\n\n      while (currentDate <= endDate) {\n        for (let hour = 0; hour < 24; hour++) {\n          const slotStart = new Date(currentDate);\n          slotStart.setHours(hour, 0, 0, 0);\n\n          const slotEnd = new Date(currentDate);\n          slotEnd.setHours(hour, 55, 0, 0);\n\n          // 检查轮次开始时间是否超出结束日期\n          if (slotStart > endDate) {\n            break;\n          }\n\n          slots.push({\n            start: this.formatLocalDateTime(slotStart),\n            end: this.formatLocalDateTime(slotEnd),\n            startTime: slotStart,\n            endTime: slotEnd\n          });\n        }\n\n        // 移动到下一天\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n      return slots;\n    },\n\n    // 有效时段数量（未过期的时段）\n    validSlotsCount() {\n      const now = new Date();\n      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;\n    }\n  },\n\n  mounted() {\n    console.log('FlashSaleMultiPage组件已挂载');\n    console.log('初始showAddModal值:', this.showAddModal);\n    this.loadData();\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n    if (this.countdownTimer) {\n      clearInterval(this.countdownTimer);\n    }\n  },\n\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList()\n      ]);\n    },\n\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalemulti/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalemulti/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    async loadRoundsList() {\n      try {\n        const response = await this.axios.get('flashsalemulti/list');\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    async loadGoodsList() {\n      try {\n        this.loadingGoods = true;\n        console.log('开始加载商品列表...');\n\n        const response = await this.axios.get('flashsalemulti/goods');\n        console.log('商品列表API响应:', response.data);\n\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data || [];\n          console.log('商品列表加载成功，数量:', this.goodsList.length);\n\n          if (this.goodsList.length === 0) {\n            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');\n          }\n        } else {\n          console.error('API返回错误:', response.data.errmsg);\n          this.$message.error(response.data.errmsg || '加载商品列表失败');\n          this.goodsList = [];\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n        this.$message.error('网络错误，请检查服务器连接');\n        this.goodsList = [];\n      } finally {\n        this.loadingGoods = false;\n      }\n    },\n\n    startAutoRefresh() {\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 30000); // 30秒刷新一次\n    },\n\n    isGoodsSelected(goodsId) {\n      return this.newRound.goods_list.some(g => g.goods_id === goodsId);\n    },\n\n    getSelectedGoods(goodsId) {\n      return this.newRound.goods_list.find(g => g.goods_id === goodsId);\n    },\n\n    toggleGoods(goods) {\n      if (!goods.can_select) return;\n\n      if (this.isGoodsSelected(goods.id)) {\n        this.removeGoods(goods.id);\n      } else {\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const defaultDiscount = 20; // 默认20%折扣\n        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;\n\n        this.newRound.goods_list.push({\n          goods_id: goods.id,\n          goods_name: goods.name,\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: defaultDiscount,\n          stock: 100,\n          limit_quantity: 1\n        });\n      }\n    },\n\n    removeGoods(goodsId) {\n      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);\n      if (index > -1) {\n        this.newRound.goods_list.splice(index, 1);\n      }\n    },\n\n    calculateDiscountRate(originalPrice, flashPrice) {\n      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;\n      const rate = Math.round((1 - flashPrice / originalPrice) * 100);\n      return isNaN(rate) ? 0 : rate;\n    },\n\n    updateFlashPriceByDiscount(goodsId) {\n      const selectedGoods = this.getSelectedGoods(goodsId);\n      if (selectedGoods && selectedGoods.original_price > 0) {\n        const discountRate = selectedGoods.discount_rate || 0;\n        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;\n        selectedGoods.flash_price = flashPrice;\n      }\n    },\n\n    // 格式化本地日期时间为字符串（避免时区问题）\n    formatLocalDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n\n    // 格式化本地日期为字符串（避免时区问题）\n    formatLocalDate(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    getCurrentDateTime() {\n      const now = new Date();\n      return this.formatLocalDateTime(now).slice(0, 16).replace(' ', 'T');\n    },\n\n    getCurrentDate() {\n      const now = new Date();\n      return this.formatLocalDate(now);\n    },\n\n    openCreateModal() {\n      console.log('点击创建新轮次按钮');\n      // 设置默认日期为今天\n      const today = this.getCurrentDate();\n      this.newRound.start_date = today;\n      this.newRound.end_date = today;\n      this.showAddModal = true;\n      console.log('showAddModal设置为:', this.showAddModal);\n    },\n\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatSlotTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    isSlotInPast(startTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      return slotStart < now;\n    },\n\n    isSlotActive(startTime, endTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      const slotEnd = new Date(endTime);\n      return now >= slotStart && now <= slotEnd;\n    },\n\n    async createRound() {\n      if (!this.canCreateRound) {\n        this.$message.error('请完善轮次信息');\n        return;\n      }\n\n      // 生成整点秒杀时段数据\n      const hourlySlots = this.hourlySlotPreview;\n      const now = new Date();\n\n      // 过滤掉已完全结束的时段，保留当前正在进行的和未来的时段\n      const validSlots = hourlySlots.filter(slot => new Date(slot.end) > now);\n\n      if (validSlots.length === 0) {\n        this.$message.error('所选时间段内没有有效的秒杀时段');\n        return;\n      }\n\n      // 如果轮次数量过多，询问用户确认\n      if (validSlots.length > 50) {\n        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);\n        if (!confirmed) {\n          return;\n        }\n      }\n\n      try {\n        this.isCreating = true;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = validSlots.length;\n\n        let createdCount = 0;\n        let failedCount = 0;\n        const failedReasons = [];\n\n        // 批量处理，每次处理10个轮次\n        const batchSize = 10;\n        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {\n          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);\n          const batch = validSlots.slice(batchStart, batchEnd);\n\n          // 并行创建当前批次的轮次\n          const batchPromises = batch.map(async (slot, batchIndex) => {\n            const globalIndex = batchStart + batchIndex;\n\n            // 重试逻辑\n            const maxRetries = 3;\n            let lastError = null;\n\n            for (let retry = 0; retry < maxRetries; retry++) {\n              try {\n                const roundData = {\n                  round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,\n                  start_time: slot.start,\n                  end_time: slot.end,\n                  is_hourly_flash: true,\n                  slot_index: globalIndex + 1,\n                  total_slots: validSlots.length,\n                  goods_list: this.newRound.goods_list.map(goods => ({\n                    goods_id: goods.goods_id,\n                    goods_name: goods.goods_name,\n                    goods_image: goods.goods_image,\n                    original_price: goods.original_price,\n                    flash_price: goods.flash_price,\n                    stock: goods.stock,\n                    discount_rate: goods.discount_rate\n                  }))\n                };\n\n                const response = await this.axios.post('flashsalemulti/create', roundData);\n\n                if (response.data.errno === 0) {\n                  return { success: true, index: globalIndex + 1 };\n                } else {\n                  lastError = response.data.errmsg;\n                  // 如果是重复键错误，等待一段时间后重试\n                  if (lastError.includes('Duplicate entry') && retry < maxRetries - 1) {\n                    await new Promise(resolve => setTimeout(resolve, 500 * (retry + 1)));\n                    continue;\n                  }\n                  return { success: false, index: globalIndex + 1, error: lastError };\n                }\n              } catch (error) {\n                lastError = error.message;\n                // 如果是网络错误或服务器错误，等待后重试\n                if (retry < maxRetries - 1) {\n                  await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));\n                  continue;\n                }\n              }\n            }\n\n            return { success: false, index: globalIndex + 1, error: `创建失败 (已重试${maxRetries}次): ${lastError}` };\n          });\n\n          // 等待当前批次完成\n          const batchResults = await Promise.all(batchPromises);\n\n          // 统计结果\n          batchResults.forEach(result => {\n            this.creationProgress.current++;\n            if (result.success) {\n              createdCount++;\n            } else {\n              failedCount++;\n              if (failedReasons.length < 5) { // 只记录前5个错误\n                failedReasons.push(`第${result.index}场: ${result.error}`);\n              }\n            }\n          });\n\n          // 短暂延迟，避免服务器压力过大\n          if (batchEnd < validSlots.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n          }\n        }\n\n        // 显示结果\n        if (createdCount > 0) {\n          let message = `成功创建${createdCount}个整点秒杀轮次`;\n          if (failedCount > 0) {\n            message += `，${failedCount}个失败`;\n            if (failedReasons.length > 0) {\n              console.warn('创建失败的轮次:', failedReasons);\n              message += `\\n主要错误: ${failedReasons[0]}`;\n            }\n          }\n          this.$message.success(message);\n          this.closeModal();\n          this.loadData();\n        } else {\n          let errorMessage = '所有轮次创建失败';\n          if (failedReasons.length > 0) {\n            errorMessage += `\\n错误信息: ${failedReasons[0]}`;\n          }\n          this.$message.error(errorMessage);\n        }\n\n      } catch (error) {\n        console.error('创建整点秒杀轮次失败:', error);\n        this.$message.error('创建过程中发生错误: ' + error.message);\n      } finally {\n        this.isCreating = false;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = 0;\n      }\n    },\n\n    closeModal() {\n      this.showAddModal = false;\n      this.newRound = {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      };\n    },\n\n    viewRoundDetails(round) {\n      this.selectedRound = round;\n      this.showDetailModal = true;\n    },\n\n    async closeRound(round) {\n      if (!confirm(`确定要关闭轮次\"${round.round_name}\"吗？关闭后轮次将立即结束。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在关闭轮次...');\n        const response = await this.axios.post('flashsalemulti/close', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已关闭');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '关闭失败');\n        }\n      } catch (error) {\n        console.error('关闭轮次失败:', error);\n        this.$message.error('关闭失败');\n      }\n    },\n\n\n\n    formatTime(seconds) {\n      if (!seconds || seconds <= 0) return '00:00:00';\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor((seconds % 3600) / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return new Date(dateTime).toLocaleString('zh-CN');\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束'\n      };\n      return statusMap[status] || status;\n    },\n\n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'upcoming': 'badge badge-warning',\n        'active': 'badge badge-success',\n        'ended': 'badge badge-secondary'\n      };\n      return classMap[status] || 'badge badge-light';\n    },\n\n    // 获取倒计时\n    getCountdown(timeStr) {\n      if (!timeStr) return '';\n      const targetTime = new Date(timeStr);\n      const now = new Date();\n      const diff = targetTime - now;\n\n      if (diff <= 0) return '';\n\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n\n      if (hours > 0) {\n        return `${hours}小时${minutes}分钟`;\n      } else if (minutes > 0) {\n        return `${minutes}分钟${seconds}秒`;\n      } else {\n        return `${seconds}秒`;\n      }\n    },\n\n    // 自动刷新相关方法\n    toggleAutoRefresh() {\n      if (this.autoRefresh) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先停止之前的定时器\n\n      this.refreshTimer = setInterval(() => {\n        this.refreshData();\n      }, this.refreshInterval);\n\n      // 启动倒计时\n      this.refreshCountdown = this.refreshInterval / 1000;\n      this.countdownTimer = setInterval(() => {\n        this.refreshCountdown--;\n        if (this.refreshCountdown <= 0) {\n          this.refreshCountdown = this.refreshInterval / 1000;\n        }\n      }, 1000);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n      this.refreshCountdown = 0;\n    },\n\n    async refreshData() {\n      this.loading = true;\n      try {\n        await this.loadData();\n      } catch (error) {\n        console.error('刷新数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 批量操作相关方法\n    toggleSelectAll() {\n      if (this.isAllSelected) {\n        this.selectedRounds = [];\n      } else {\n        this.selectedRounds = this.roundsList.data.map(round => round.id);\n      }\n    },\n\n    updateSelection() {\n      // 这个方法会在复选框状态改变时自动调用\n    },\n\n    clearSelection() {\n      this.selectedRounds = [];\n    },\n\n    async batchCloseRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要关闭的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要关闭选中的 ${this.selectedRounds.length} 个轮次吗？`,\n          '批量关闭确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          let successCount = 0;\n          let failCount = 0;\n\n          for (const roundId of this.selectedRounds) {\n            try {\n              const response = await this.$http.post('/admin/flashsalemulti/close', {\n                round_id: roundId\n              });\n\n              if (response.data.errno === 0) {\n                successCount++;\n              } else {\n                failCount++;\n              }\n            } catch (error) {\n              failCount++;\n            }\n          }\n\n          this.$message.success(`批量关闭完成：成功 ${successCount} 个，失败 ${failCount} 个`);\n          this.clearSelection();\n          this.loadData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量关闭失败:', error);\n          this.$message.error('批量关闭失败');\n        }\n      }\n    },\n\n    async batchDeleteRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要删除的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要删除选中的 ${this.selectedRounds.length} 个轮次吗？此操作不可恢复！\\n\\n注意：进行中或即将开始的轮次将先被关闭，然后删除。`,\n          '批量删除确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在批量删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/batchDelete', {\n            round_ids: this.selectedRounds\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success(response.data.errmsg || '批量删除完成');\n            this.clearSelection();\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '批量删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error);\n          this.$message.error('批量删除失败');\n        }\n      }\n    },\n\n    // 延期轮次相关方法\n    showExtendModal(round) {\n      this.extendRound = round;\n      this.extendMinutes = null;\n      this.showExtendModalFlag = true;\n    },\n\n    closeExtendModal() {\n      this.showExtendModalFlag = false;\n      this.extendRound = null;\n      this.extendMinutes = null;\n    },\n\n    async confirmExtend() {\n      if (!this.extendMinutes || this.extendMinutes <= 0) {\n        this.$message.warning('请输入有效的延期时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/extend', {\n          round_id: this.extendRound.id,\n          extend_minutes: this.extendMinutes\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success(`轮次已延期${this.extendMinutes}分钟`);\n          this.closeExtendModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '延期失败');\n        }\n      } catch (error) {\n        console.error('延期轮次失败:', error);\n        this.$message.error('延期失败');\n      }\n    },\n\n    // 重启轮次相关方法\n    showRestartModal(round) {\n      this.restartRound = round;\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.newStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.newEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showRestartModalFlag = true;\n    },\n\n    closeRestartModal() {\n      this.showRestartModalFlag = false;\n      this.restartRound = null;\n      this.newStartTime = '';\n      this.newEndTime = '';\n    },\n\n    async confirmRestart() {\n      if (!this.newStartTime || !this.newEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.newStartTime) >= new Date(this.newEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/restart', {\n          round_id: this.restartRound.id,\n          new_start_time: this.newStartTime,\n          new_end_time: this.newEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已重新启动');\n          this.closeRestartModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '重启失败');\n        }\n      } catch (error) {\n        console.error('重启轮次失败:', error);\n        this.$message.error('重启失败');\n      }\n    },\n\n    // 复制轮次相关方法\n    showCopyModal(round) {\n      this.copyRound = round;\n      this.newRoundName = round.round_name + ' (复制)';\n\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.copyStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.copyEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showCopyModalFlag = true;\n    },\n\n    closeCopyModal() {\n      this.showCopyModalFlag = false;\n      this.copyRound = null;\n      this.newRoundName = '';\n      this.copyStartTime = '';\n      this.copyEndTime = '';\n    },\n\n    async confirmCopy() {\n      if (!this.copyStartTime || !this.copyEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.copyStartTime) >= new Date(this.copyEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/copy', {\n          round_id: this.copyRound.id,\n          new_round_name: this.newRoundName,\n          new_start_time: this.copyStartTime,\n          new_end_time: this.copyEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次复制成功');\n          this.closeCopyModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '复制失败');\n        }\n      } catch (error) {\n        console.error('复制轮次失败:', error);\n        this.$message.error('复制失败');\n      }\n    },\n\n    // 删除轮次\n    async deleteRound(round) {\n      try {\n        const statusText = {\n          'upcoming': '即将开始',\n          'active': '进行中',\n          'ended': '已结束'\n        };\n\n        let confirmMessage = `确定要删除轮次\"${round.round_name}\"吗？此操作不可恢复！`;\n\n        if (round.status === 'active' || round.status === 'upcoming') {\n          confirmMessage += `\\n\\n注意：该轮次当前状态为\"${statusText[round.status]}\"，删除前将先自动关闭轮次。`;\n        }\n\n        const confirmResult = await this.$confirm(\n          confirmMessage,\n          '删除轮次确认',\n          {\n            confirmButtonText: '确定删除',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/delete', {\n            round_id: round.id\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success('轮次删除成功');\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除轮次失败:', error);\n          this.$message.error('删除失败');\n        }\n      }\n    },\n\n    // 编辑轮次\n    editRound(round) {\n      // 这里可以跳转到编辑页面或打开编辑模态框\n      this.$message.info('编辑功能待实现');\n    },\n\n    // 格式化日期时间为本地格式（用于datetime-local输入框）\n    formatDateTimeLocal(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n\n      return `${year}-${month}-${day}T${hours}:${minutes}`;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.flash-sale-multi-page {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n/* 表格控制区域 */\n.table-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.auto-refresh-control {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-size: 14px;\n}\n\n.refresh-countdown {\n  color: #666;\n  font-size: 12px;\n}\n\n/* 批量操作区域 */\n.batch-operations {\n  margin-bottom: 15px;\n}\n\n.batch-actions {\n  margin-top: 10px;\n  display: flex;\n  gap: 10px;\n}\n\n/* 状态信息显示 */\n.status-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.time-info {\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.countdown {\n  font-weight: bold;\n  color: #f56c6c;\n}\n\n/* 操作按钮组 */\n.round-actions {\n  display: flex;\n  gap: 5px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin-bottom: 2px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.round-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin: 0;\n}\n\n.btn-info {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: white;\n}\n\n.btn-info:hover {\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #212529;\n}\n\n.btn-warning:hover {\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n/* 整点秒杀设置样式 */\n.hourly-flash-settings {\n  margin-bottom: 20px;\n}\n\n.setting-header {\n  margin-bottom: 15px;\n}\n\n.setting-header h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.setting-description {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.time-range-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  color: #666;\n  font-weight: bold;\n}\n\n.slot-preview {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 5px;\n  border: 1px solid #e9ecef;\n}\n\n.slot-preview h5 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.slot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.slot-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 8px 12px;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.slot-number {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 60px;\n}\n\n.slot-time {\n  flex: 1;\n  color: #333;\n}\n\n.slot-duration {\n  color: #28a745;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.slot-summary {\n  margin-top: 10px;\n  padding-top: 10px;\n  border-top: 1px solid #dee2e6;\n  text-align: center;\n  color: #666;\n  font-weight: bold;\n}\n\n.valid-slots {\n  color: #28a745;\n  margin-left: 10px;\n}\n\n.round-name-preview {\n  margin-bottom: 20px;\n}\n\n.round-name-preview h5 {\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: bold;\n}\n\n.name-display {\n  padding: 10px 15px;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n.slot-list-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.slot-status {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n}\n\n.slot-status.past {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.slot-status.active {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.slot-status.upcoming {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.more-slots {\n  padding: 10px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.stat-card h3 {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.stat-number.active {\n  color: #28a745;\n}\n\n.stat-number.upcoming {\n  color: #ffc107;\n}\n\n/* 当前轮次 */\n.current-rounds {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.round-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.round-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n}\n\n.round-item.active {\n  border-color: #28a745;\n  background-color: #f8fff9;\n}\n\n.round-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.round-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.goods-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 80px;\n}\n\n.goods-item img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n  margin-bottom: 4px;\n}\n\n.goods-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n.goods-item .price {\n  color: #e74c3c;\n  font-weight: bold;\n}\n\n.more-goods {\n  color: #007bff;\n  font-size: 12px;\n}\n\n/* 表格 */\n.rounds-table {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.status-upcoming {\n  color: #ffc107;\n  font-weight: bold;\n}\n\n.status-active {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.status-ended {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n/* 模态框 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content.large {\n  max-width: 1000px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n}\n\n.modal-body {\n  padding: 20px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 表单 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: flex;\n  gap: 15px;\n}\n\n.form-row .form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control.small {\n  width: 80px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 商品选择网格 */\n.goods-selection-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n}\n\n/* 商品卡片 */\n.goods-card {\n  background: white;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.goods-card:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.goods-card.selected {\n  border-color: #28a745;\n  background-color: #f8fff9;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);\n}\n\n.goods-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background-color: #f5f5f5;\n  border-color: #dee2e6;\n}\n\n.goods-card.disabled:hover {\n  transform: none;\n  box-shadow: none;\n  border-color: #dee2e6;\n}\n\n/* 商品卡片头部 */\n.goods-card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.goods-image-container {\n  position: relative;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.goods-card-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.selected-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background-color: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.2);\n}\n\n.checkmark {\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.disabled-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 商品信息 */\n.goods-card-info {\n  flex: 1;\n}\n\n.goods-name {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.original-price {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.warning-text {\n  display: flex;\n  align-items: center;\n  color: #dc3545;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.warning-icon {\n  margin-right: 4px;\n  font-size: 14px;\n}\n\n/* 商品设置面板 */\n.goods-settings-panel {\n  border-top: 1px solid #e9ecef;\n  padding-top: 15px;\n  margin-top: 15px;\n}\n\n.settings-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.settings-title::before {\n  content: \"⚙\";\n  margin-right: 6px;\n  color: #007bff;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n}\n\n.setting-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-item.full-width {\n  grid-column: 1 / -1;\n}\n\n.setting-item label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n/* 价格输入组 */\n.price-input-group {\n  display: flex;\n  align-items: center;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n  background: white;\n}\n\n.currency {\n  background-color: #f8f9fa;\n  padding: 6px 8px;\n  border-right: 1px solid #ddd;\n  font-size: 14px;\n  color: #666;\n}\n\n.price-input {\n  border: none;\n  padding: 6px 8px;\n  font-size: 14px;\n  flex: 1;\n  outline: none;\n}\n\n.price-input:focus {\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.discount-display {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #e74c3c;\n  font-weight: bold;\n  text-align: center;\n  background-color: #fff5f5;\n  padding: 2px 6px;\n  border-radius: 12px;\n  border: 1px solid #fecaca;\n}\n\n/* 库存和限购输入 */\n.stock-input,\n.limit-input {\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 6px 8px;\n  font-size: 14px;\n  outline: none;\n  background: white;\n}\n\n.stock-input:focus,\n.limit-input:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 折扣设置样式 */\n.discount-setting {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.discount-input-group {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.discount-input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.discount-unit {\n  margin-left: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.price-preview {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.price-range-hint {\n  font-size: 12px;\n  color: #28a745;\n  font-style: italic;\n}\n\n/* 选择提示 */\n.selection-hint {\n  margin-top: 15px;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #007bff;\n}\n\n.hint-text {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-right: 8px;\n  color: #007bff;\n  font-size: 16px;\n}\n\n.selected-count {\n  margin: 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.selected-count strong {\n  color: #28a745;\n  font-size: 16px;\n}\n\n/* 已选商品汇总 */\n.selected-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.selected-summary h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n}\n\n.summary-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.remove-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n/* 轮次详情 */\n.round-details {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.detail-section h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.detail-section p {\n  margin: 5px 0;\n  color: #666;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  color: #6c757d;\n  font-size: 16px;\n}\n\n.loading-state .loading-icon {\n  margin-right: 10px;\n  font-size: 20px;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n.empty-state .empty-text {\n  font-size: 18px;\n  font-weight: 500;\n  color: #495057;\n  margin: 0 0 8px 0;\n}\n\n.empty-state .empty-hint {\n  font-size: 14px;\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.empty-state .btn-sm {\n  padding: 6px 16px;\n  font-size: 14px;\n}\n\n.goods-cell {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-cell img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.goods-cell span {\n  font-size: 14px;\n}\n\n/* 状态徽章样式 */\n.badge {\n  display: inline-block;\n  padding: 4px 8px;\n  font-size: 12px;\n  font-weight: bold;\n  border-radius: 4px;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.badge-warning {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.badge-success {\n  background-color: #28a745;\n  color: white;\n}\n\n.badge-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.badge-light {\n  background-color: #f8f9fa;\n  color: #6c757d;\n  border: 1px solid #dee2e6;\n}\n\n/* 模态框样式增强 */\n.modal-content.small {\n  max-width: 500px;\n}\n\n.modal-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 18px;\n}\n\n/* 表单控件增强 */\n.form-control[type=\"number\"] {\n  text-align: center;\n}\n\n.form-control[type=\"datetime-local\"] {\n  font-family: monospace;\n}\n\n/* 按钮样式增强 */\n.btn:hover {\n  opacity: 0.9;\n  transform: translateY(-1px);\n  transition: all 0.2s ease;\n}\n\n.btn-success {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: white;\n}\n\n.btn-success:hover {\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n</style>\n</style>\n"]}]}