{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_round_goods.js"], "names": ["module", "exports", "think", "Model", "addRoundGoods", "roundId", "goodsList", "goodsData", "map", "goods", "index", "round_id", "goods_id", "goods_name", "goods_image", "original_price", "flash_price", "discount_rate", "calculateDiscountRate", "stock", "sold_count", "limit_quantity", "sort_order", "addMany", "originalPrice", "flashPrice", "Math", "round", "getRoundGoodsList", "where", "order", "select", "checkGoodsInActiveRounds", "goodsIds", "roundModel", "model", "activeGoods", "alias", "join", "table", "as", "on", "field", "checkGoodsInOverlappingRounds", "startTime", "endTime", "sql", "tablePrefix", "params", "overlappingGoods", "query", "error", "console", "updateSoldCount", "roundGoodsId", "quantity", "id", "increment", "getAvailableStock", "roundGoods", "find", "isEmpty", "max", "getUserPurchaseCount", "goodsId", "userId", "orderModel", "result", "user_id", "sum"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGMC,eAAN,CAAoBC,OAApB,EAA6BC,SAA7B,EAAwC;AAAA;;AAAA;AACtC,YAAMC,YAAYD,UAAUE,GAAV,CAAc,UAACC,KAAD,EAAQC,KAAR;AAAA,eAAmB;AACjDC,oBAAUN,OADuC;AAEjDO,oBAAUH,MAAMG,QAFiC;AAGjDC,sBAAYJ,MAAMI,UAH+B;AAIjDC,uBAAaL,MAAMK,WAAN,IAAqB,EAJe;AAKjDC,0BAAgBN,MAAMM,cAL2B;AAMjDC,uBAAaP,MAAMO,WAN8B;AAOjDC,yBAAeR,MAAMQ,aAAN,IAAuB,MAAKC,qBAAL,CAA2BT,MAAMM,cAAjC,EAAiDN,MAAMO,WAAvD,CAPW;AAQjDG,iBAAOV,MAAMU,KARoC;AASjDC,sBAAY,CATqC;AAUjDC,0BAAgBZ,MAAMY,cAAN,IAAwB,CAVS;AAWjDC,sBAAYZ;AAXqC,SAAnB;AAAA,OAAd,CAAlB;;AAcA,aAAO,MAAM,MAAKa,OAAL,CAAahB,SAAb,CAAb;AAfsC;AAgBvC;;AAED;;;AAGAW,wBAAsBM,aAAtB,EAAqCC,UAArC,EAAiD;AAC/C,QAAI,CAACD,aAAD,IAAkBA,iBAAiB,CAAvC,EAA0C,OAAO,CAAP;AAC1C,WAAOE,KAAKC,KAAL,CAAW,CAAC,IAAIF,aAAaD,aAAlB,IAAmC,GAAnC,GAAyC,GAApD,IAA2D,GAAlE,CAF+C,CAEwB;AACxE;;AAED;;;AAGMI,mBAAN,CAAwBvB,OAAxB,EAAiC;AAAA;;AAAA;AAC/B,aAAO,MAAM,OAAKwB,KAAL,CAAW,EAAElB,UAAUN,OAAZ,EAAX,EACVyB,KADU,CACJ,gBADI,EAEVC,MAFU,EAAb;AAD+B;AAIhC;;AAED;;;AAGMC,0BAAN,CAA+BC,QAA/B,EAAyC;AAAA;;AAAA;AACvC,YAAMC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,YAAMC,cAAc,MAAM,OAAKC,KAAL,CAAW,IAAX,EACvBC,IADuB,CAClB;AACJC,eAAO,mBADH;AAEJD,cAAM,OAFF;AAGJE,YAAI,GAHA;AAIJC,YAAI,CAAC,aAAD,EAAgB,MAAhB;AAJA,OADkB,EAOvBZ,KAPuB,CAOjB;AACL,uBAAe,CAAC,IAAD,EAAOI,QAAP,CADV;AAEL,oBAAY,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAFP,OAPiB,EAWvBS,KAXuB,CAWjB,iEAXiB,EAYvBX,MAZuB,EAA1B;;AAcA,aAAOK,WAAP;AAlBuC;AAmBxC;;AAED;;;AAGMO,+BAAN,CAAoCV,QAApC,EAA8CW,SAA9C,EAAyDC,OAAzD,EAAkE;AAAA;;AAAA;AAChE,UAAI;AACF;AACA,cAAMC,MAAO;;eAEJ,OAAKC,WAAY;qBACX,OAAKA,WAAY;gCACNd,SAASzB,GAAT,CAAa;AAAA,iBAAM,GAAN;AAAA,SAAb,EAAwB8B,IAAxB,CAA6B,GAA7B,CAAkC;;;;;;;;OAJ5D;;AAcA,cAAMU,SAAS,CACb,GAAGf,QADU,EAEbW,SAFa,EAEFA,SAFE,EAEU;AACvBC,eAHa,EAGJA,OAHI,EAGU;AACvBD,iBAJa,EAIFC,OAJE,EAIU;AACvBD,iBALa,EAKFC,OALE,CAKU;AALV,SAAf;;AAQA,cAAMI,mBAAmB,MAAM,OAAKC,KAAL,CAAWJ,GAAX,EAAgBE,MAAhB,CAA/B;AACA,eAAOC,oBAAoB,EAA3B;AAED,OA3BD,CA2BE,OAAOE,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA;AACA,eAAO,EAAP;AACD;AAhC+D;AAiCjE;;AAED;;;AAGME,iBAAN,CAAsBC,YAAtB,EAAoCC,QAApC,EAA8C;AAAA;;AAAA;AAC5C,aAAO,MAAM,OAAK1B,KAAL,CAAW,EAAE2B,IAAIF,YAAN,EAAX,EACVG,SADU,CACA,YADA,EACcF,QADd,CAAb;AAD4C;AAG7C;;AAED;;;AAGMG,mBAAN,CAAwBJ,YAAxB,EAAsC;AAAA;;AAAA;AACpC,YAAMK,aAAa,MAAM,OAAK9B,KAAL,CAAW,EAAE2B,IAAIF,YAAN,EAAX,EAAiCM,IAAjC,EAAzB;AACA,UAAI1D,MAAM2D,OAAN,CAAcF,UAAd,CAAJ,EAA+B;AAC7B,eAAO,CAAP;AACD;AACD,aAAOjC,KAAKoC,GAAL,CAAS,CAAT,EAAYH,WAAWxC,KAAX,GAAmBwC,WAAWvC,UAA1C,CAAP;AALoC;AAMrC;;AAED;;;AAGM2C,sBAAN,CAA2B1D,OAA3B,EAAoC2D,OAApC,EAA6CC,MAA7C,EAAqD;AAAA;;AAAA;AACnD,YAAMC,aAAa,OAAK/B,KAAL,CAAW,mBAAX,CAAnB;AACA,YAAMgC,SAAS,MAAMD,WAAWrC,KAAX,CAAiB;AACpClB,kBAAUN,OAD0B;AAEpCO,kBAAUoD,OAF0B;AAGpCI,iBAASH;AAH2B,OAAjB,EAIlBI,GAJkB,CAId,UAJc,CAArB;;AAMA,aAAOF,UAAU,CAAjB;AARmD;AASpD;AArIwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_round_goods.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 批量添加轮次商品\n   */\n  async addRoundGoods(roundId, goodsList) {\n    const goodsData = goodsList.map((goods, index) => ({\n      round_id: roundId,\n      goods_id: goods.goods_id,\n      goods_name: goods.goods_name,\n      goods_image: goods.goods_image || '',\n      original_price: goods.original_price,\n      flash_price: goods.flash_price,\n      discount_rate: goods.discount_rate || this.calculateDiscountRate(goods.original_price, goods.flash_price),\n      stock: goods.stock,\n      sold_count: 0,\n      limit_quantity: goods.limit_quantity || 1,\n      sort_order: index\n    }));\n    \n    return await this.addMany(goodsData);\n  }\n  \n  /**\n   * 计算折扣率\n   */\n  calculateDiscountRate(originalPrice, flashPrice) {\n    if (!originalPrice || originalPrice <= 0) return 0;\n    return Math.round((1 - flashPrice / originalPrice) * 100 * 100) / 100; // 保留2位小数\n  }\n  \n  /**\n   * 获取轮次商品列表\n   */\n  async getRoundGoodsList(roundId) {\n    return await this.where({ round_id: roundId })\n      .order('sort_order ASC')\n      .select();\n  }\n  \n  /**\n   * 检查商品是否已参与其他活跃轮次\n   */\n  async checkGoodsInActiveRounds(goodsIds) {\n    const roundModel = this.model('flash_sale_rounds');\n\n    // 查询活跃轮次中的商品\n    const activeGoods = await this.alias('rg')\n      .join({\n        table: 'flash_sale_rounds',\n        join: 'inner',\n        as: 'r',\n        on: ['rg.round_id', 'r.id']\n      })\n      .where({\n        'rg.goods_id': ['IN', goodsIds],\n        'r.status': ['IN', ['upcoming', 'active']]\n      })\n      .field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status')\n      .select();\n\n    return activeGoods;\n  }\n\n  /**\n   * 检查商品在指定时间段是否有重叠的轮次\n   */\n  async checkGoodsInOverlappingRounds(goodsIds, startTime, endTime) {\n    try {\n      // 使用原生SQL查询，避免复杂的ORM语法问题\n      const sql = `\n        SELECT rg.goods_id, r.round_number, r.start_time, r.end_time, r.status\n        FROM ${this.tablePrefix}flash_sale_round_goods rg\n        INNER JOIN ${this.tablePrefix}flash_sale_rounds r ON rg.round_id = r.id\n        WHERE rg.goods_id IN (${goodsIds.map(() => '?').join(',')})\n          AND r.status IN ('upcoming', 'active')\n          AND (\n            (r.start_time <= ? AND r.end_time > ?) OR\n            (r.start_time < ? AND r.end_time >= ?) OR\n            (r.start_time >= ? AND r.end_time <= ?) OR\n            (r.start_time <= ? AND r.end_time >= ?)\n          )\n      `;\n\n      const params = [\n        ...goodsIds,\n        startTime, startTime,  // 新轮次开始时间在现有轮次范围内\n        endTime, endTime,      // 新轮次结束时间在现有轮次范围内\n        startTime, endTime,    // 新轮次完全包含现有轮次\n        startTime, endTime     // 现有轮次完全包含新轮次\n      ];\n\n      const overlappingGoods = await this.query(sql, params);\n      return overlappingGoods || [];\n\n    } catch (error) {\n      console.error('检查商品时间重叠失败:', error);\n      // 如果查询失败，返回空数组，允许创建轮次\n      return [];\n    }\n  }\n  \n  /**\n   * 更新商品销售数量\n   */\n  async updateSoldCount(roundGoodsId, quantity) {\n    return await this.where({ id: roundGoodsId })\n      .increment('sold_count', quantity);\n  }\n  \n  /**\n   * 获取商品在轮次中的剩余库存\n   */\n  async getAvailableStock(roundGoodsId) {\n    const roundGoods = await this.where({ id: roundGoodsId }).find();\n    if (think.isEmpty(roundGoods)) {\n      return 0;\n    }\n    return Math.max(0, roundGoods.stock - roundGoods.sold_count);\n  }\n  \n  /**\n   * 获取用户在某轮次某商品的购买数量\n   */\n  async getUserPurchaseCount(roundId, goodsId, userId) {\n    const orderModel = this.model('flash_sale_orders');\n    const result = await orderModel.where({\n      round_id: roundId,\n      goods_id: goodsId,\n      user_id: userId\n    }).sum('quantity');\n    \n    return result || 0;\n  }\n};\n"]}