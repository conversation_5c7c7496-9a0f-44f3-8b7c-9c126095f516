{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue", "mtime": 1754305881391}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgcG9pbnRzR29vZHNBcGkgZnJvbSAnQC9hcGkvcG9pbnRzLWdvb2RzJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQb2ludHNHb29kc1BhZ2UnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHN0YXRpc3RpY3M6IHt9LA0KICAgICAgcG9pbnRzR29vZHNMaXN0OiBbXSwNCiAgICAgIGNhdGVnb3JpZXM6IFtdLA0KICAgICAgYXZhaWxhYmxlR29vZHM6IFtdLA0KICAgICAgc2VsZWN0ZWRQcm9kdWN0OiBudWxsLA0KDQogICAgICAvLyDliIbpobUNCiAgICAgIHBhZ2luYXRpb246IHsNCiAgICAgICAgcGFnZTogMSwNCiAgICAgICAgbGltaXQ6IDIwLA0KICAgICAgICB0b3RhbDogMA0KICAgICAgfSwNCg0KICAgICAgLy8g5pCc57Si5ZKM562b6YCJDQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIHN0YXR1c0ZpbHRlcjogJycsDQoNCiAgICAgIC8vIOWVhuWTgemAieaLqeWZqA0KICAgICAgc2hvd0dvb2RzU2VsZWN0b3I6IGZhbHNlLA0KICAgICAgZ29vZHNTZWFyY2hLZXl3b3JkOiAnJywNCiAgICAgIGdvb2RzU2VhcmNoQ2F0ZWdvcnk6ICcnLA0KICAgICAgZ29vZHNQYWdlOiAxLA0KICAgICAgZ29vZHNMaW1pdDogMTIsDQogICAgICBnb29kc1RvdGFsOiAwLA0KDQogICAgICAvLyDlvLnnqpcNCiAgICAgIHNob3dNb2RhbDogZmFsc2UsDQogICAgICBlZGl0aW5nR29vZHM6IG51bGwsDQoNCiAgICAgIC8vIOihqOWNleaVsOaNrg0KICAgICAgZm9ybURhdGE6IHsNCiAgICAgICAgZ29vZHNfaWQ6ICcnLA0KICAgICAgICBwcm9kdWN0X2lkOiAnJywNCiAgICAgICAgcG9pbnRzX3ByaWNlOiAnJywNCiAgICAgICAgY2FzaF9wcmljZTogMCwNCiAgICAgICAgc3RvY2tfbGltaXQ6IDAsDQogICAgICAgIGRhaWx5X2xpbWl0OiAwLA0KICAgICAgICB1c2VyX2xpbWl0OiAwLA0KICAgICAgICBzb3J0OiAwLA0KICAgICAgICBzdGF0dXM6IDEsDQogICAgICAgIGlzX2hvdDogMCwNCiAgICAgICAgZGVzY3JpcHRpb246ICcnDQogICAgICB9DQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0KCkNCiAgfSwNCg0KICBtZXRob2RzOiB7DQogICAgYXN5bmMgaW5pdCgpIHsNCiAgICAgIGF3YWl0IHRoaXMubG9hZFN0YXRpc3RpY3MoKQ0KICAgICAgYXdhaXQgdGhpcy5sb2FkUG9pbnRzR29vZHMoKQ0KICAgICAgYXdhaXQgdGhpcy5sb2FkQ2F0ZWdvcmllcygpDQogICAgfSwNCg0KICAgIC8vIOWKoOi9vee7n+iuoeaVsOaNrg0KICAgIGFzeW5jIGxvYWRTdGF0aXN0aWNzKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS5nZXRTdGF0aXN0aWNzKCkNCiAgICAgICAgaWYgKHJlc3BvbnNlLmVycm5vID09PSAwKSB7DQogICAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3nu5/orqHmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veenr+WIhuWVhuWTgeWIl+ihqA0KICAgIGFzeW5jIGxvYWRQb2ludHNHb29kcyhwYWdlID0gMSkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHBhZ2U6IHBhZ2UsDQogICAgICAgICAgbGltaXQ6IHRoaXMucGFnaW5hdGlvbi5saW1pdCwNCiAgICAgICAgICBrZXl3b3JkOiB0aGlzLnNlYXJjaEtleXdvcmQsDQogICAgICAgICAgc3RhdHVzOiB0aGlzLnN0YXR1c0ZpbHRlcg0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS5nZXRMaXN0KHBhcmFtcykNCiAgICAgICAgaWYgKHJlc3BvbnNlLmVycm5vID09PSAwKSB7DQogICAgICAgICAgdGhpcy5wb2ludHNHb29kc0xpc3QgPSByZXNwb25zZS5kYXRhLmxpc3QNCiAgICAgICAgICB0aGlzLnBhZ2luYXRpb24gPSB7DQogICAgICAgICAgICBwYWdlOiByZXNwb25zZS5kYXRhLnBhZ2UsDQogICAgICAgICAgICBsaW1pdDogcmVzcG9uc2UuZGF0YS5saW1pdCwNCiAgICAgICAgICAgIHRvdGFsOiByZXNwb25zZS5kYXRhLnRvdGFsDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZXJybXNnIHx8ICfliqDovb3np6/liIbllYblk4HliJfooajlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3np6/liIbllYblk4HliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veenr+WIhuWVhuWTgeWIl+ihqOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3liIbnsbsNCiAgICBhc3luYyBsb2FkQ2F0ZWdvcmllcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcG9pbnRzR29vZHNBcGkuZ2V0Q2F0ZWdvcmllcygpDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuY2F0ZWdvcmllcyA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295YiG57G75aSx6LSlOicsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmkJzntKLllYblk4ENCiAgICBzZWFyY2hHb29kcygpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnNlYXJjaFRpbWVyKQ0KICAgICAgdGhpcy5zZWFyY2hUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRQb2ludHNHb29kcygxKQ0KICAgICAgfSwgNTAwKQ0KICAgIH0sDQoNCiAgICAvLyDliIbpobUNCiAgICBjaGFuZ2VQYWdlKHBhZ2UpIHsNCiAgICAgIGlmIChwYWdlID49IDEgJiYgcGFnZSA8PSBNYXRoLmNlaWwodGhpcy5wYWdpbmF0aW9uLnRvdGFsIC8gdGhpcy5wYWdpbmF0aW9uLmxpbWl0KSkgew0KICAgICAgICB0aGlzLmxvYWRQb2ludHNHb29kcyhwYWdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmiZPlvIDllYblk4HpgInmi6nlmagNCiAgICBhc3luYyBvcGVuR29vZHNTZWxlY3RvcigpIHsNCiAgICAgIHRoaXMuc2hvd0dvb2RzU2VsZWN0b3IgPSB0cnVlDQogICAgICBhd2FpdCB0aGlzLmxvYWRBdmFpbGFibGVHb29kcygxKQ0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63llYblk4HpgInmi6nlmagNCiAgICBjbG9zZUdvb2RzU2VsZWN0b3IoKSB7DQogICAgICB0aGlzLnNob3dHb29kc1NlbGVjdG9yID0gZmFsc2UNCiAgICAgIHRoaXMuZ29vZHNTZWFyY2hLZXl3b3JkID0gJycNCiAgICAgIHRoaXMuZ29vZHNTZWFyY2hDYXRlZ29yeSA9ICcnDQogICAgICB0aGlzLmdvb2RzUGFnZSA9IDENCiAgICAgIHRoaXMuYXZhaWxhYmxlR29vZHMgPSBbXQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3lj6/pgInllYblk4ENCiAgICBhc3luYyBsb2FkQXZhaWxhYmxlR29vZHMocGFnZSA9IDEpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBwYWdlOiBwYWdlLA0KICAgICAgICAgIGxpbWl0OiB0aGlzLmdvb2RzTGltaXQsDQogICAgICAgICAga2V5d29yZDogdGhpcy5nb29kc1NlYXJjaEtleXdvcmQsDQogICAgICAgICAgY2F0ZWdvcnlfaWQ6IHRoaXMuZ29vZHNTZWFyY2hDYXRlZ29yeQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS5nZXRBdmFpbGFibGVHb29kcyhwYXJhbXMpDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuYXZhaWxhYmxlR29vZHMgPSByZXNwb25zZS5kYXRhLmxpc3QNCiAgICAgICAgICB0aGlzLmdvb2RzUGFnZSA9IHJlc3BvbnNlLmRhdGEucGFnZQ0KICAgICAgICAgIHRoaXMuZ29vZHNUb3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWwNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLmVycm1zZyB8fCAn5Yqg6L295Y+v6YCJ5ZWG5ZOB5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295Y+v6YCJ5ZWG5ZOB5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3lj6/pgInllYblk4HlpLHotKUnKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pCc57Si5Y+v6YCJ5ZWG5ZOBDQogICAgc2VhcmNoQXZhaWxhYmxlR29vZHMoKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy5nb29kc1NlYXJjaFRpbWVyKQ0KICAgICAgdGhpcy5nb29kc1NlYXJjaFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZEF2YWlsYWJsZUdvb2RzKDEpDQogICAgICB9LCA1MDApDQogICAgfSwNCg0KICAgIC8vIOWVhuWTgeWIhumhtQ0KICAgIGNoYW5nZUdvb2RzUGFnZShwYWdlKSB7DQogICAgICBpZiAocGFnZSA+PSAxICYmIHBhZ2UgPD0gTWF0aC5jZWlsKHRoaXMuZ29vZHNUb3RhbCAvIHRoaXMuZ29vZHNMaW1pdCkpIHsNCiAgICAgICAgdGhpcy5sb2FkQXZhaWxhYmxlR29vZHMocGFnZSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6YCJ5oup5ZWG5ZOBDQogICAgYXN5bmMgc2VsZWN0R29vZHMoZ29vZHMpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPluWVhuWTgeeahOinhOagvOWIl+ihqA0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHBvaW50c0dvb2RzQXBpLmdldEdvb2RzUHJvZHVjdHMoZ29vZHMuaWQpDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIGNvbnN0IHByb2R1Y3RzID0gcmVzcG9uc2UuZGF0YQ0KDQogICAgICAgICAgaWYgKHByb2R1Y3RzLmxlbmd0aCA9PT0gMSkgew0KICAgICAgICAgICAgLy8g5Y+q5pyJ5LiA5Liq6KeE5qC877yM55u05o6l6YCJ5oupDQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IHsNCiAgICAgICAgICAgICAgZ29vZHNfaWQ6IGdvb2RzLmlkLA0KICAgICAgICAgICAgICBnb29kc19uYW1lOiBnb29kcy5uYW1lLA0KICAgICAgICAgICAgICBnb29kc19pbWFnZTogZ29vZHMubGlzdF9waWNfdXJsLA0KICAgICAgICAgICAgICBnb29kc19icmllZjogZ29vZHMuZ29vZHNfYnJpZWYsDQogICAgICAgICAgICAgIHByb2R1Y3RfaWQ6IHByb2R1Y3RzWzBdLmlkLA0KICAgICAgICAgICAgICBvcmlnaW5hbF9wcmljZTogcHJvZHVjdHNbMF0ucmV0YWlsX3ByaWNlLA0KICAgICAgICAgICAgICBzdG9jazogcHJvZHVjdHNbMF0uZ29vZHNfbnVtYmVyLA0KICAgICAgICAgICAgICBzcGVjaWZpY2F0aW9uX2luZm86IHByb2R1Y3RzWzBdLnNwZWNpZmljYXRpb25faW5mbyB8fCAn6buY6K6k6KeE5qC8Jw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5vcGVuTW9kYWwoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDlpJrkuKrop4TmoLzvvIzmmL7npLrop4TmoLzpgInmi6kNCiAgICAgICAgICAgIHRoaXMuc2hvd1Byb2R1Y3RTZWxlY3Rvcihnb29kcywgcHJvZHVjdHMpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZXJybXNnIHx8ICfojrflj5bllYblk4Hop4TmoLzlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bllYblk4Hop4TmoLzlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWVhuWTgeinhOagvOWksei0pScpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmmL7npLrop4TmoLzpgInmi6nlmagNCiAgICBzaG93UHJvZHVjdFNlbGVjdG9yKGdvb2RzLCBwcm9kdWN0cykgew0KICAgICAgLy8g5Yib5bu66KeE5qC86YCJ5oup55qESFRNTOWGheWuuQ0KICAgICAgY29uc3QgcHJvZHVjdE9wdGlvbnMgPSBwcm9kdWN0cy5tYXAocHJvZHVjdCA9Pg0KICAgICAgICBgPGRpdiBjbGFzcz0iYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtMyBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTUwIG1iLTIiIG9uY2xpY2s9IndpbmRvdy5zZWxlY3RQcm9kdWN0KCR7cHJvZHVjdC5pZH0pIj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIiPg0KICAgICAgICAgICAgPHNwYW4gY2xhc3M9InRleHQtc20gZm9udC1tZWRpdW0iPiR7cHJvZHVjdC5zcGVjaWZpY2F0aW9uX2luZm8gfHwgJ+m7mOiupOinhOagvCd9PC9zcGFuPg0KICAgICAgICAgICAgPHNwYW4gY2xhc3M9InRleHQtc20gdGV4dC1ncmF5LTUwMCI+wqUke3Byb2R1Y3QucmV0YWlsX3ByaWNlfTwvc3Bhbj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMSI+5bqT5a2YOiAke3Byb2R1Y3QuZ29vZHNfbnVtYmVyfTwvZGl2Pg0KICAgICAgICA8L2Rpdj5gDQogICAgICApLmpvaW4oJycpOw0KDQogICAgICBjb25zdCBjb250ZW50ID0gYA0KICAgICAgICA8ZGl2Pg0KICAgICAgICAgIDxwIGNsYXNzPSJtYi00IHRleHQtZ3JheS02MDAiPuivt+mAieaLqSIke2dvb2RzLm5hbWV9IueahOinhOagvDo8L3A+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3BhY2UteS0yIj4ke3Byb2R1Y3RPcHRpb25zfTwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICAgIGA7DQoNCiAgICAgIC8vIOS4tOaXtuiuvue9ruWFqOWxgOmAieaLqeWHveaVsA0KICAgICAgd2luZG93LnNlbGVjdFByb2R1Y3QgPSAocHJvZHVjdElkKSA9PiB7DQogICAgICAgIGNvbnN0IHByb2R1Y3QgPSBwcm9kdWN0cy5maW5kKHAgPT4gcC5pZCA9PT0gcHJvZHVjdElkKTsNCiAgICAgICAgaWYgKHByb2R1Y3QpIHsNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IHsNCiAgICAgICAgICAgIGdvb2RzX2lkOiBnb29kcy5pZCwNCiAgICAgICAgICAgIGdvb2RzX25hbWU6IGdvb2RzLm5hbWUsDQogICAgICAgICAgICBnb29kc19pbWFnZTogZ29vZHMubGlzdF9waWNfdXJsLA0KICAgICAgICAgICAgZ29vZHNfYnJpZWY6IGdvb2RzLmdvb2RzX2JyaWVmLA0KICAgICAgICAgICAgcHJvZHVjdF9pZDogcHJvZHVjdC5pZCwNCiAgICAgICAgICAgIG9yaWdpbmFsX3ByaWNlOiBwcm9kdWN0LnJldGFpbF9wcmljZSwNCiAgICAgICAgICAgIHN0b2NrOiBwcm9kdWN0Lmdvb2RzX251bWJlciwNCiAgICAgICAgICAgIHNwZWNpZmljYXRpb25faW5mbzogcHJvZHVjdC5zcGVjaWZpY2F0aW9uX2luZm8gfHwgJ+m7mOiupOinhOagvCcNCiAgICAgICAgICB9Ow0KICAgICAgICAgIHRoaXMub3Blbk1vZGFsKCk7DQogICAgICAgICAgLy8g5riF55CG5YWo5bGA5Ye95pWwDQogICAgICAgICAgZGVsZXRlIHdpbmRvdy5zZWxlY3RQcm9kdWN0Ow0KICAgICAgICB9DQogICAgICB9Ow0KDQogICAgICB0aGlzLiRhbGVydChjb250ZW50LCAn6YCJ5oup5ZWG5ZOB6KeE5qC8Jywgew0KICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUsDQogICAgICAgIHNob3dDb25maXJtQnV0dG9uOiBmYWxzZSwNCiAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgIGN1c3RvbUNsYXNzOiAncHJvZHVjdC1zZWxlY3Rvci1tb2RhbCcNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmiZPlvIDlvLnnqpcNCiAgICBvcGVuTW9kYWwoKSB7DQogICAgICB0aGlzLnNob3dHb29kc1NlbGVjdG9yID0gZmFsc2UNCiAgICAgIHRoaXMuc2hvd01vZGFsID0gdHJ1ZQ0KICAgICAgdGhpcy5yZXNldEZvcm0oKQ0KDQogICAgICBpZiAodGhpcy5zZWxlY3RlZFByb2R1Y3QpIHsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5nb29kc19pZCA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0Lmdvb2RzX2lkDQogICAgICAgIHRoaXMuZm9ybURhdGEucHJvZHVjdF9pZCA9IHRoaXMuc2VsZWN0ZWRQcm9kdWN0LnByb2R1Y3RfaWQNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YWz6Zet5by556qXDQogICAgY2xvc2VNb2RhbCgpIHsNCiAgICAgIHRoaXMuc2hvd01vZGFsID0gZmFsc2UNCiAgICAgIHRoaXMuZWRpdGluZ0dvb2RzID0gbnVsbA0KICAgICAgdGhpcy5zZWxlY3RlZFByb2R1Y3QgPSBudWxsDQogICAgICB0aGlzLnJlc2V0Rm9ybSgpDQogICAgfSwNCg0KICAgIC8vIOmHjee9ruihqOWNlQ0KICAgIHJlc2V0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZm9ybURhdGEgPSB7DQogICAgICAgIGdvb2RzX2lkOiAnJywNCiAgICAgICAgcHJvZHVjdF9pZDogJycsDQogICAgICAgIHBvaW50c19wcmljZTogJycsDQogICAgICAgIGNhc2hfcHJpY2U6IDAsDQogICAgICAgIHN0b2NrX2xpbWl0OiAwLA0KICAgICAgICBkYWlseV9saW1pdDogMCwNCiAgICAgICAgdXNlcl9saW1pdDogMCwNCiAgICAgICAgc29ydDogMCwNCiAgICAgICAgc3RhdHVzOiAxLA0KICAgICAgICBpc19ob3Q6IDAsDQogICAgICAgIGRlc2NyaXB0aW9uOiAnJw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnvJbovpHnp6/liIbllYblk4ENCiAgICBlZGl0UG9pbnRzR29vZHMoaXRlbSkgew0KICAgICAgdGhpcy5lZGl0aW5nR29vZHMgPSBpdGVtDQogICAgICB0aGlzLnNlbGVjdGVkUHJvZHVjdCA9IHsNCiAgICAgICAgZ29vZHNfaWQ6IGl0ZW0uZ29vZHNfaWQsDQogICAgICAgIGdvb2RzX25hbWU6IGl0ZW0uZ29vZHNfbmFtZSwNCiAgICAgICAgZ29vZHNfaW1hZ2U6IGl0ZW0uZ29vZHNfaW1hZ2UsDQogICAgICAgIGdvb2RzX2JyaWVmOiBpdGVtLmdvb2RzX2JyaWVmLA0KICAgICAgICBwcm9kdWN0X2lkOiBpdGVtLnByb2R1Y3RfaWQsDQogICAgICAgIG9yaWdpbmFsX3ByaWNlOiBpdGVtLm9yaWdpbmFsX3ByaWNlLA0KICAgICAgICBzdG9jazogaXRlbS5zdG9jaywNCiAgICAgICAgc3BlY2lmaWNhdGlvbl9pbmZvOiBpdGVtLnNwZWNpZmljYXRpb25faW5mbw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZvcm1EYXRhID0gew0KICAgICAgICBnb29kc19pZDogaXRlbS5nb29kc19pZCwNCiAgICAgICAgcHJvZHVjdF9pZDogaXRlbS5wcm9kdWN0X2lkLA0KICAgICAgICBwb2ludHNfcHJpY2U6IGl0ZW0ucG9pbnRzX3ByaWNlLA0KICAgICAgICBjYXNoX3ByaWNlOiBpdGVtLmNhc2hfcHJpY2UsDQogICAgICAgIHN0b2NrX2xpbWl0OiBpdGVtLnN0b2NrX2xpbWl0LA0KICAgICAgICBkYWlseV9saW1pdDogaXRlbS5kYWlseV9saW1pdCwNCiAgICAgICAgdXNlcl9saW1pdDogaXRlbS51c2VyX2xpbWl0LA0KICAgICAgICBzb3J0OiBpdGVtLnNvcnQsDQogICAgICAgIHN0YXR1czogaXRlbS5zdGF0dXMsDQogICAgICAgIGlzX2hvdDogaXRlbS5pc19ob3QsDQogICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uIHx8ICcnDQogICAgICB9DQoNCiAgICAgIHRoaXMuc2hvd01vZGFsID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjnp6/liIbllYblk4ENCiAgICBhc3luYyBzYXZlUG9pbnRzR29vZHMoKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybURhdGEucG9pbnRzX3ByaWNlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+i+k+WFpeenr+WIhuS7t+agvCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICB0cnkgew0KICAgICAgICBsZXQgcmVzcG9uc2UNCiAgICAgICAgaWYgKHRoaXMuZWRpdGluZ0dvb2RzKSB7DQogICAgICAgICAgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS51cGRhdGUodGhpcy5lZGl0aW5nR29vZHMuaWQsIHRoaXMuZm9ybURhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS5hZGQodGhpcy5mb3JtRGF0YSkNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyh0aGlzLmVkaXRpbmdHb29kcyA/ICfmm7TmlrDmiJDlip8nIDogJ+a3u+WKoOaIkOWKnycpDQogICAgICAgICAgdGhpcy5jbG9zZU1vZGFsKCkNCiAgICAgICAgICBhd2FpdCB0aGlzLmxvYWRQb2ludHNHb29kcygpDQogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkU3RhdGlzdGljcygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5lcnJtc2cgfHwgJ+aTjeS9nOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOenr+WIhuWVhuWTgeWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pON5L2c5aSx6LSlJykNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWIh+aNoueKtuaAgQ0KICAgIGFzeW5jIHRvZ2dsZVN0YXR1cyhpdGVtKSB7DQogICAgICBjb25zdCBuZXdTdGF0dXMgPSBpdGVtLnN0YXR1cyA9PT0gMSA/IDAgOiAxDQogICAgICBjb25zdCBhY3Rpb24gPSBuZXdTdGF0dXMgPT09IDEgPyAn5LiK5p62JyA6ICfkuIvmnrYnDQoNCiAgICAgIHRyeSB7DQogICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oYOehruWumuimgSR7YWN0aW9ufei/meS4quenr+WIhuWVhuWTgeWQl++8n2AsICfnoa7orqTmk43kvZwnLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KDQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBwb2ludHNHb29kc0FwaS51cGRhdGVTdGF0dXMoaXRlbS5pZCwgbmV3U3RhdHVzKQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhgJHthY3Rpb2595oiQ5YqfYCkNCiAgICAgICAgICBhd2FpdCB0aGlzLmxvYWRQb2ludHNHb29kcygpDQogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkU3RhdGlzdGljcygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5lcnJtc2cgfHwgYCR7YWN0aW9ufeWksei0pWApDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGlmIChlcnJvciAhPT0gJ2NhbmNlbCcpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfliIfmjaLnirbmgIHlpLHotKU6JywgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHthY3Rpb2595aSx6LSlYCkNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk56ev5YiG5ZWG5ZOBDQogICAgYXN5bmMgZGVsZXRlUG9pbnRzR29vZHMoaXRlbSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq56ev5YiG5ZWG5ZOB5ZCX77yf5Yig6Zmk5ZCO5LiN5Y+v5oGi5aSN44CCJywgJ+ehruiupOWIoOmZpCcsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgIH0pDQoNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHBvaW50c0dvb2RzQXBpLmRlbGV0ZShpdGVtLmlkKQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5lcnJubyA9PT0gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICBhd2FpdCB0aGlzLmxvYWRQb2ludHNHb29kcygpDQogICAgICAgICAgYXdhaXQgdGhpcy5sb2FkU3RhdGlzdGljcygpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5lcnJtc2cgfHwgJ+WIoOmZpOWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGlmIChlcnJvciAhPT0gJ2NhbmNlbCcpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTnp6/liIbllYblk4HlpLHotKU6JywgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["PointsGoodsPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8fA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PointsGoodsPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\r\n  <div class=\"p-6\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"mb-6\">\r\n      <h1 class=\"text-2xl font-bold text-gray-900\">积分兑好礼管理</h1>\r\n      <p class=\"text-gray-600 mt-1\">从商品库中选择商品规格，设置积分兑换条件</p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-blue-100 rounded-lg\">\r\n            <i class=\"ri-gift-line text-2xl text-blue-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">总商品数</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_goods || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-green-100 rounded-lg\">\r\n            <i class=\"ri-check-line text-2xl text-green-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">上架商品</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.online_goods || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-yellow-100 rounded-lg\">\r\n            <i class=\"ri-shopping-cart-line text-2xl text-yellow-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">兑换订单</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_orders || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-red-100 rounded-lg\">\r\n            <i class=\"ri-coin-line text-2xl text-red-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">消耗积分</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_points || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 操作栏 -->\r\n    <div class=\"bg-white rounded-lg shadow mb-6\">\r\n      <div class=\"px-6 py-4 border-b border-gray-200\">\r\n        <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0\">\r\n          <div class=\"flex items-center space-x-4\">\r\n            <button\r\n              @click=\"openGoodsSelector\"\r\n              class=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            >\r\n              <i class=\"ri-add-line mr-2\"></i>\r\n              添加积分商品\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"relative\">\r\n              <input\r\n                v-model=\"searchKeyword\"\r\n                @input=\"searchGoods\"\r\n                type=\"text\"\r\n                placeholder=\"搜索商品名称...\"\r\n                class=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              />\r\n              <i class=\"ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"></i>\r\n            </div>\r\n\r\n            <select\r\n              v-model=\"statusFilter\"\r\n              @change=\"loadPointsGoods\"\r\n              class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            >\r\n              <option value=\"\">全部状态</option>\r\n              <option value=\"1\">上架</option>\r\n              <option value=\"0\">下架</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div class=\"overflow-x-auto\">\r\n        <table class=\"min-w-full divide-y divide-gray-200\">\r\n          <thead class=\"bg-gray-50\">\r\n            <tr>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                商品信息\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                规格信息\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                积分价格\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                库存/销量\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                状态\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                操作\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody class=\"bg-white divide-y divide-gray-200\">\r\n            <tr v-for=\"item in pointsGoodsList\" :key=\"item.id\" class=\"hover:bg-gray-50\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-shrink-0 h-16 w-16\">\r\n                    <img\r\n                      :src=\"item.goods_image || '/static/images/default-goods.png'\"\r\n                      :alt=\"item.goods_name\"\r\n                      class=\"h-16 w-16 rounded-lg object-cover\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"ml-4\">\r\n                    <div class=\"text-sm font-medium text-gray-900 max-w-xs truncate\">\r\n                      {{ item.goods_name }}\r\n                    </div>\r\n                    <div class=\"text-sm text-gray-500\">\r\n                      商品ID: {{ item.goods_id }}\r\n                    </div>\r\n                    <div v-if=\"item.is_hot\" class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1\">\r\n                      热门\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">\r\n                  {{ item.specification_info || '默认规格' }}\r\n                </div>\r\n                <div class=\"text-sm text-gray-500\">\r\n                  规格ID: {{ item.product_id }}\r\n                </div>\r\n                <div class=\"text-sm text-gray-500\">\r\n                  原价: ¥{{ item.original_price }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm font-medium text-red-600\">\r\n                  {{ item.points_price }} 积分\r\n                </div>\r\n                <div v-if=\"item.cash_price > 0\" class=\"text-sm text-gray-500\">\r\n                  + ¥{{ item.cash_price }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                <div>库存: {{ item.stock_limit > 0 ? `${item.stock_limit - item.sold_count}/${item.stock_limit}` : item.stock }}</div>\r\n                <div>销量: {{ item.sold_count }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <span\r\n                  :class=\"[\r\n                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\r\n                    item.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\r\n                  ]\"\r\n                >\r\n                  {{ item.status === 1 ? '上架' : '下架' }}\r\n                </span>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\r\n                <button\r\n                  @click=\"editPointsGoods(item)\"\r\n                  class=\"text-blue-600 hover:text-blue-900\"\r\n                >\r\n                  编辑\r\n                </button>\r\n                <button\r\n                  @click=\"toggleStatus(item)\"\r\n                  :class=\"[\r\n                    'hover:opacity-75',\r\n                    item.status === 1 ? 'text-red-600' : 'text-green-600'\r\n                  ]\"\r\n                >\r\n                  {{ item.status === 1 ? '下架' : '上架' }}\r\n                </button>\r\n                <button\r\n                  @click=\"deletePointsGoods(item)\"\r\n                  class=\"text-red-600 hover:text-red-900\"\r\n                >\r\n                  删除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div v-if=\"pagination.total > 0\" class=\"px-6 py-4 border-t border-gray-200\">\r\n        <div class=\"flex items-center justify-between\">\r\n          <div class=\"text-sm text-gray-700\">\r\n            显示 {{ (pagination.page - 1) * pagination.limit + 1 }} 到\r\n            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，\r\n            共 {{ pagination.total }} 条记录\r\n          </div>\r\n          <div class=\"flex items-center space-x-2\">\r\n            <button\r\n              @click=\"changePage(pagination.page - 1)\"\r\n              :disabled=\"pagination.page <= 1\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              上一页\r\n            </button>\r\n            <span class=\"px-3 py-1 text-sm\">\r\n              第 {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }} 页\r\n            </span>\r\n            <button\r\n              @click=\"changePage(pagination.page + 1)\"\r\n              :disabled=\"pagination.page >= Math.ceil(pagination.total / pagination.limit)\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              下一页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 空状态 -->\r\n      <div v-if=\"pointsGoodsList.length === 0 && !loading\" class=\"text-center py-12\">\r\n        <i class=\"ri-gift-line text-6xl text-gray-300 mb-4\"></i>\r\n        <p class=\"text-gray-500 text-lg mb-2\">暂无积分商品</p>\r\n        <p class=\"text-gray-400 text-sm\">点击\"添加积分商品\"开始配置</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品选择器弹窗 -->\r\n    <div v-if=\"showGoodsSelector\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden\">\r\n        <div class=\"px-6 py-4 border-b border-gray-200 flex items-center justify-between\">\r\n          <h3 class=\"text-lg font-medium text-gray-900\">选择商品规格</h3>\r\n          <button @click=\"closeGoodsSelector\" class=\"text-gray-400 hover:text-gray-600\">\r\n            <i class=\"ri-close-line text-2xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\">\r\n          <!-- 搜索栏 -->\r\n          <div class=\"mb-6 flex items-center space-x-4\">\r\n            <div class=\"relative flex-1\">\r\n              <input\r\n                v-model=\"goodsSearchKeyword\"\r\n                @input=\"searchAvailableGoods\"\r\n                type=\"text\"\r\n                placeholder=\"搜索商品名称...\"\r\n                class=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              />\r\n              <i class=\"ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"></i>\r\n            </div>\r\n\r\n            <select\r\n              v-model=\"goodsSearchCategory\"\r\n              @change=\"searchAvailableGoods\"\r\n              class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            >\r\n              <option value=\"\">全部分类</option>\r\n              <option v-for=\"category in categories\" :key=\"category.id\" :value=\"category.id\">\r\n                {{ category.name }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n\r\n          <!-- 商品网格 -->\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            <div\r\n              v-for=\"goods in availableGoods\"\r\n              :key=\"goods.id\"\r\n              class=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer\"\r\n              @click=\"selectGoods(goods)\"\r\n            >\r\n              <div class=\"flex items-start space-x-4\">\r\n                <img\r\n                  :src=\"goods.list_pic_url || '/static/images/default-goods.png'\"\r\n                  :alt=\"goods.name\"\r\n                  class=\"w-16 h-16 rounded-lg object-cover flex-shrink-0\"\r\n                />\r\n                <div class=\"flex-1 min-w-0\">\r\n                  <h4 class=\"text-sm font-medium text-gray-900 truncate\">{{ goods.name }}</h4>\r\n                  <p class=\"text-xs text-gray-500 mt-1 line-clamp-2\">{{ goods.goods_brief }}</p>\r\n                  <div class=\"mt-2 text-xs text-gray-600\">\r\n                    <div>商品ID: {{ goods.id }}</div>\r\n                    <div>规格数: {{ goods.product_count || 0 }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 商品分页 -->\r\n          <div v-if=\"goodsTotal > goodsLimit\" class=\"mt-6 flex items-center justify-center space-x-2\">\r\n            <button\r\n              @click=\"changeGoodsPage(goodsPage - 1)\"\r\n              :disabled=\"goodsPage <= 1\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              上一页\r\n            </button>\r\n            <span class=\"px-3 py-1 text-sm\">\r\n              第 {{ goodsPage }} / {{ Math.ceil(goodsTotal / goodsLimit) }} 页\r\n            </span>\r\n            <button\r\n              @click=\"changeGoodsPage(goodsPage + 1)\"\r\n              :disabled=\"goodsPage >= Math.ceil(goodsTotal / goodsLimit)\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              下一页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 规格选择弹窗 -->\r\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\r\n        <div class=\"px-6 py-4 border-b border-gray-200 flex items-center justify-between\">\r\n          <h3 class=\"text-lg font-medium text-gray-900\">\r\n            {{ editingGoods ? '编辑积分商品' : '添加积分商品' }}\r\n          </h3>\r\n          <button @click=\"closeModal\" class=\"text-gray-400 hover:text-gray-600\">\r\n            <i class=\"ri-close-line text-2xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\">\r\n          <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            <!-- 商品信息 -->\r\n            <div class=\"space-y-4\">\r\n              <h4 class=\"text-md font-medium text-gray-900\">商品信息</h4>\r\n\r\n              <div v-if=\"selectedProduct\" class=\"border border-gray-200 rounded-lg p-4\">\r\n                <div class=\"flex items-start space-x-4\">\r\n                  <img\r\n                    :src=\"selectedProduct.goods_image || '/static/images/default-goods.png'\"\r\n                    :alt=\"selectedProduct.goods_name\"\r\n                    class=\"w-20 h-20 rounded-lg object-cover flex-shrink-0\"\r\n                  />\r\n                  <div class=\"flex-1\">\r\n                    <h5 class=\"text-sm font-medium text-gray-900\">{{ selectedProduct.goods_name }}</h5>\r\n                    <p class=\"text-xs text-gray-500 mt-1\">{{ selectedProduct.goods_brief }}</p>\r\n                    <div class=\"mt-2 text-xs text-gray-600\">\r\n                      <div>商品ID: {{ selectedProduct.goods_id }}</div>\r\n                      <div>规格ID: {{ selectedProduct.product_id }}</div>\r\n                      <div>原价: ¥{{ selectedProduct.original_price }}</div>\r\n                      <div>库存: {{ selectedProduct.stock }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 积分设置 -->\r\n            <div class=\"space-y-4\">\r\n              <h4 class=\"text-md font-medium text-gray-900\">积分设置</h4>\r\n\r\n              <div class=\"grid grid-cols-1 gap-4\">\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">积分价格 *</label>\r\n                  <input\r\n                    v-model=\"formData.points_price\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"请输入积分价格\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">现金价格</label>\r\n                  <input\r\n                    v-model=\"formData.cash_price\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    step=\"0.01\"\r\n                    placeholder=\"可选，积分+现金模式\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">库存限制</label>\r\n                  <input\r\n                    v-model=\"formData.stock_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制，使用商品原库存\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">每日限购</label>\r\n                  <input\r\n                    v-model=\"formData.daily_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">用户限购</label>\r\n                  <input\r\n                    v-model=\"formData.user_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">排序</label>\r\n                  <input\r\n                    v-model=\"formData.sort\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"数字越大排序越靠前\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div class=\"flex items-center space-x-6\">\r\n                  <label class=\"flex items-center\">\r\n                    <input\r\n                      v-model=\"formData.status\"\r\n                      type=\"checkbox\"\r\n                      :true-value=\"1\"\r\n                      :false-value=\"0\"\r\n                      class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span class=\"ml-2 text-sm text-gray-700\">立即上架</span>\r\n                  </label>\r\n\r\n                  <label class=\"flex items-center\">\r\n                    <input\r\n                      v-model=\"formData.is_hot\"\r\n                      type=\"checkbox\"\r\n                      :true-value=\"1\"\r\n                      :false-value=\"0\"\r\n                      class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span class=\"ml-2 text-sm text-gray-700\">热门商品</span>\r\n                  </label>\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">商品描述</label>\r\n                  <textarea\r\n                    v-model=\"formData.description\"\r\n                    rows=\"3\"\r\n                    placeholder=\"可选，积分商品的特殊描述\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  ></textarea>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mt-6 flex items-center justify-end space-x-3\">\r\n            <button\r\n              @click=\"closeModal\"\r\n              class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\r\n            >\r\n              取消\r\n            </button>\r\n            <button\r\n              @click=\"savePointsGoods\"\r\n              :disabled=\"!formData.points_price || loading\"\r\n              class=\"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {{ editingGoods ? '更新' : '添加' }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\r\n        <div class=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\r\n        <span class=\"text-gray-700\">加载中...</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport pointsGoodsApi from '@/api/points-goods'\r\n\r\nexport default {\r\n  name: 'PointsGoodsPage',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      statistics: {},\r\n      pointsGoodsList: [],\r\n      categories: [],\r\n      availableGoods: [],\r\n      selectedProduct: null,\r\n\r\n      // 分页\r\n      pagination: {\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n\r\n      // 搜索和筛选\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n\r\n      // 商品选择器\r\n      showGoodsSelector: false,\r\n      goodsSearchKeyword: '',\r\n      goodsSearchCategory: '',\r\n      goodsPage: 1,\r\n      goodsLimit: 12,\r\n      goodsTotal: 0,\r\n\r\n      // 弹窗\r\n      showModal: false,\r\n      editingGoods: null,\r\n\r\n      // 表单数据\r\n      formData: {\r\n        goods_id: '',\r\n        product_id: '',\r\n        points_price: '',\r\n        cash_price: 0,\r\n        stock_limit: 0,\r\n        daily_limit: 0,\r\n        user_limit: 0,\r\n        sort: 0,\r\n        status: 1,\r\n        is_hot: 0,\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.init()\r\n  },\r\n\r\n  methods: {\r\n    async init() {\r\n      await this.loadStatistics()\r\n      await this.loadPointsGoods()\r\n      await this.loadCategories()\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      try {\r\n        const response = await pointsGoodsApi.getStatistics()\r\n        if (response.errno === 0) {\r\n          this.statistics = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载积分商品列表\r\n    async loadPointsGoods(page = 1) {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: page,\r\n          limit: this.pagination.limit,\r\n          keyword: this.searchKeyword,\r\n          status: this.statusFilter\r\n        }\r\n\r\n        const response = await pointsGoodsApi.getList(params)\r\n        if (response.errno === 0) {\r\n          this.pointsGoodsList = response.data.list\r\n          this.pagination = {\r\n            page: response.data.page,\r\n            limit: response.data.limit,\r\n            total: response.data.total\r\n          }\r\n        } else {\r\n          this.$message.error(response.errmsg || '加载积分商品列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载积分商品列表失败:', error)\r\n        this.$message.error('加载积分商品列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 加载分类\r\n    async loadCategories() {\r\n      try {\r\n        const response = await pointsGoodsApi.getCategories()\r\n        if (response.errno === 0) {\r\n          this.categories = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载分类失败:', error)\r\n      }\r\n    },\r\n\r\n    // 搜索商品\r\n    searchGoods() {\r\n      clearTimeout(this.searchTimer)\r\n      this.searchTimer = setTimeout(() => {\r\n        this.loadPointsGoods(1)\r\n      }, 500)\r\n    },\r\n\r\n    // 分页\r\n    changePage(page) {\r\n      if (page >= 1 && page <= Math.ceil(this.pagination.total / this.pagination.limit)) {\r\n        this.loadPointsGoods(page)\r\n      }\r\n    },\r\n\r\n    // 打开商品选择器\r\n    async openGoodsSelector() {\r\n      this.showGoodsSelector = true\r\n      await this.loadAvailableGoods(1)\r\n    },\r\n\r\n    // 关闭商品选择器\r\n    closeGoodsSelector() {\r\n      this.showGoodsSelector = false\r\n      this.goodsSearchKeyword = ''\r\n      this.goodsSearchCategory = ''\r\n      this.goodsPage = 1\r\n      this.availableGoods = []\r\n    },\r\n\r\n    // 加载可选商品\r\n    async loadAvailableGoods(page = 1) {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: page,\r\n          limit: this.goodsLimit,\r\n          keyword: this.goodsSearchKeyword,\r\n          category_id: this.goodsSearchCategory\r\n        }\r\n\r\n        const response = await pointsGoodsApi.getAvailableGoods(params)\r\n        if (response.errno === 0) {\r\n          this.availableGoods = response.data.list\r\n          this.goodsPage = response.data.page\r\n          this.goodsTotal = response.data.total\r\n        } else {\r\n          this.$message.error(response.errmsg || '加载可选商品失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载可选商品失败:', error)\r\n        this.$message.error('加载可选商品失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索可选商品\r\n    searchAvailableGoods() {\r\n      clearTimeout(this.goodsSearchTimer)\r\n      this.goodsSearchTimer = setTimeout(() => {\r\n        this.loadAvailableGoods(1)\r\n      }, 500)\r\n    },\r\n\r\n    // 商品分页\r\n    changeGoodsPage(page) {\r\n      if (page >= 1 && page <= Math.ceil(this.goodsTotal / this.goodsLimit)) {\r\n        this.loadAvailableGoods(page)\r\n      }\r\n    },\r\n\r\n    // 选择商品\r\n    async selectGoods(goods) {\r\n      this.loading = true\r\n      try {\r\n        // 获取商品的规格列表\r\n        const response = await pointsGoodsApi.getGoodsProducts(goods.id)\r\n        if (response.errno === 0) {\r\n          const products = response.data\r\n\r\n          if (products.length === 1) {\r\n            // 只有一个规格，直接选择\r\n            this.selectedProduct = {\r\n              goods_id: goods.id,\r\n              goods_name: goods.name,\r\n              goods_image: goods.list_pic_url,\r\n              goods_brief: goods.goods_brief,\r\n              product_id: products[0].id,\r\n              original_price: products[0].retail_price,\r\n              stock: products[0].goods_number,\r\n              specification_info: products[0].specification_info || '默认规格'\r\n            }\r\n            this.openModal()\r\n          } else {\r\n            // 多个规格，显示规格选择\r\n            this.showProductSelector(goods, products)\r\n          }\r\n        } else {\r\n          this.$message.error(response.errmsg || '获取商品规格失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取商品规格失败:', error)\r\n        this.$message.error('获取商品规格失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 显示规格选择器\r\n    showProductSelector(goods, products) {\r\n      // 创建规格选择的HTML内容\r\n      const productOptions = products.map(product =>\r\n        `<div class=\"border border-gray-200 rounded-lg p-3 cursor-pointer hover:bg-gray-50 mb-2\" onclick=\"window.selectProduct(${product.id})\">\r\n          <div class=\"flex justify-between items-center\">\r\n            <span class=\"text-sm font-medium\">${product.specification_info || '默认规格'}</span>\r\n            <span class=\"text-sm text-gray-500\">¥${product.retail_price}</span>\r\n          </div>\r\n          <div class=\"text-xs text-gray-400 mt-1\">库存: ${product.goods_number}</div>\r\n        </div>`\r\n      ).join('');\r\n\r\n      const content = `\r\n        <div>\r\n          <p class=\"mb-4 text-gray-600\">请选择\"${goods.name}\"的规格:</p>\r\n          <div class=\"space-y-2\">${productOptions}</div>\r\n        </div>\r\n      `;\r\n\r\n      // 临时设置全局选择函数\r\n      window.selectProduct = (productId) => {\r\n        const product = products.find(p => p.id === productId);\r\n        if (product) {\r\n          this.selectedProduct = {\r\n            goods_id: goods.id,\r\n            goods_name: goods.name,\r\n            goods_image: goods.list_pic_url,\r\n            goods_brief: goods.goods_brief,\r\n            product_id: product.id,\r\n            original_price: product.retail_price,\r\n            stock: product.goods_number,\r\n            specification_info: product.specification_info || '默认规格'\r\n          };\r\n          this.openModal();\r\n          // 清理全局函数\r\n          delete window.selectProduct;\r\n        }\r\n      };\r\n\r\n      this.$alert(content, '选择商品规格', {\r\n        dangerouslyUseHTMLString: true,\r\n        showConfirmButton: false,\r\n        showCancelButton: false,\r\n        customClass: 'product-selector-modal'\r\n      });\r\n    },\r\n\r\n    // 打开弹窗\r\n    openModal() {\r\n      this.showGoodsSelector = false\r\n      this.showModal = true\r\n      this.resetForm()\r\n\r\n      if (this.selectedProduct) {\r\n        this.formData.goods_id = this.selectedProduct.goods_id\r\n        this.formData.product_id = this.selectedProduct.product_id\r\n      }\r\n    },\r\n\r\n    // 关闭弹窗\r\n    closeModal() {\r\n      this.showModal = false\r\n      this.editingGoods = null\r\n      this.selectedProduct = null\r\n      this.resetForm()\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        goods_id: '',\r\n        product_id: '',\r\n        points_price: '',\r\n        cash_price: 0,\r\n        stock_limit: 0,\r\n        daily_limit: 0,\r\n        user_limit: 0,\r\n        sort: 0,\r\n        status: 1,\r\n        is_hot: 0,\r\n        description: ''\r\n      }\r\n    },\r\n\r\n    // 编辑积分商品\r\n    editPointsGoods(item) {\r\n      this.editingGoods = item\r\n      this.selectedProduct = {\r\n        goods_id: item.goods_id,\r\n        goods_name: item.goods_name,\r\n        goods_image: item.goods_image,\r\n        goods_brief: item.goods_brief,\r\n        product_id: item.product_id,\r\n        original_price: item.original_price,\r\n        stock: item.stock,\r\n        specification_info: item.specification_info\r\n      }\r\n\r\n      this.formData = {\r\n        goods_id: item.goods_id,\r\n        product_id: item.product_id,\r\n        points_price: item.points_price,\r\n        cash_price: item.cash_price,\r\n        stock_limit: item.stock_limit,\r\n        daily_limit: item.daily_limit,\r\n        user_limit: item.user_limit,\r\n        sort: item.sort,\r\n        status: item.status,\r\n        is_hot: item.is_hot,\r\n        description: item.description || ''\r\n      }\r\n\r\n      this.showModal = true\r\n    },\r\n\r\n    // 保存积分商品\r\n    async savePointsGoods() {\r\n      if (!this.formData.points_price) {\r\n        this.$message.error('请输入积分价格')\r\n        return\r\n      }\r\n\r\n      this.loading = true\r\n      try {\r\n        let response\r\n        if (this.editingGoods) {\r\n          response = await pointsGoodsApi.update(this.editingGoods.id, this.formData)\r\n        } else {\r\n          response = await pointsGoodsApi.add(this.formData)\r\n        }\r\n\r\n        if (response.errno === 0) {\r\n          this.$message.success(this.editingGoods ? '更新成功' : '添加成功')\r\n          this.closeModal()\r\n          await this.loadPointsGoods()\r\n          await this.loadStatistics()\r\n        } else {\r\n          this.$message.error(response.errmsg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存积分商品失败:', error)\r\n        this.$message.error('操作失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 切换状态\r\n    async toggleStatus(item) {\r\n      const newStatus = item.status === 1 ? 0 : 1\r\n      const action = newStatus === 1 ? '上架' : '下架'\r\n\r\n      try {\r\n        await this.$confirm(`确定要${action}这个积分商品吗？`, '确认操作', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        this.loading = true\r\n        const response = await pointsGoodsApi.updateStatus(item.id, newStatus)\r\n\r\n        if (response.errno === 0) {\r\n          this.$message.success(`${action}成功`)\r\n          await this.loadPointsGoods()\r\n          await this.loadStatistics()\r\n        } else {\r\n          this.$message.error(response.errmsg || `${action}失败`)\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('切换状态失败:', error)\r\n          this.$message.error(`${action}失败`)\r\n        }\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 删除积分商品\r\n    async deletePointsGoods(item) {\r\n      try {\r\n        await this.$confirm('确定要删除这个积分商品吗？删除后不可恢复。', '确认删除', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        this.loading = true\r\n        const response = await pointsGoodsApi.delete(item.id)\r\n\r\n        if (response.errno === 0) {\r\n          this.$message.success('删除成功')\r\n          await this.loadPointsGoods()\r\n          await this.loadStatistics()\r\n        } else {\r\n          this.$message.error(response.errmsg || '删除失败')\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('删除积分商品失败:', error)\r\n          this.$message.error('删除失败')\r\n        }\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.line-clamp-2 {\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.animate-spin {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 自定义滚动条 */\r\n.overflow-y-auto::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.overflow-y-auto::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.overflow-y-auto::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.overflow-y-auto::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n/* 表格样式优化 */\r\n.table-container {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 状态标签样式 */\r\n.status-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-badge.online {\r\n  background-color: #dcfce7;\r\n  color: #166534;\r\n}\r\n\r\n.status-badge.offline {\r\n  background-color: #fef2f2;\r\n  color: #991b1b;\r\n}\r\n\r\n/* 按钮组样式 */\r\n.btn-group {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn-group button {\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.btn-group button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 弹窗样式优化 */\r\n.modal-overlay {\r\n  backdrop-filter: blur(4px);\r\n}\r\n\r\n/* 商品卡片样式 */\r\n.goods-card {\r\n  transition: all 0.2s;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.goods-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-color: #3b82f6;\r\n}\r\n\r\n/* 统计卡片样式 */\r\n.stats-card {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 12px;\r\n  padding: 1.5rem;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.stats-card:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.stats-card.blue {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stats-card.green {\r\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\r\n}\r\n\r\n.stats-card.yellow {\r\n  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);\r\n}\r\n\r\n.stats-card.red {\r\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\r\n}\r\n\r\n.stats-card.purple {\r\n  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);\r\n}\r\n\r\n/* 响应式优化 */\r\n@media (max-width: 768px) {\r\n  .stats-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .goods-grid {\r\n    grid-template-columns: repeat(1, 1fr);\r\n  }\r\n\r\n  .modal-content {\r\n    margin: 1rem;\r\n    max-height: calc(100vh - 2rem);\r\n  }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .stats-grid {\r\n    grid-template-columns: repeat(1, 1fr);\r\n  }\r\n\r\n  .btn-group {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .table-container {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n</style>"]}]}