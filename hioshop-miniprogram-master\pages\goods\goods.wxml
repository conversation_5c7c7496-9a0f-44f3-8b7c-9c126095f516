<wxs module="filters" src="../../utils/fixed.wxs"></wxs>
<view class="container" wx:if="{{loading == 1}}">
    <view class='banner-wrap'>
        <view class='current-item' wx:if="{{gallery.length > 0}}">
            <view class='in-item'>
                <view class='current-mask'></view>
                <view class='current-content'>
                    <view class='icon'>
                        <image class='img' src='/images/icon/img-icon.png'></image>
                    </view>
                    <view class='num'>{{current + 1}}/{{gallery.length}}</view>
                </view>
            </view>
        </view>
        <block wx:if="{{gallery.length > 0}}">
            <swiper bindchange="bindchange" class="banner banner-style1" indicator-dots="{{false}}" autoplay="{{true}}" current="{{current}}" circular="{{true}}" interval="3000" duration="1000" display-multiple-items="1">
                <swiper-item class="item" wx:for="{{gallery}}" wx:key="id">
                    <image bindtap="previewImage" data-src="{{item.img_url}}" src="{{item.img_url}}" class="slide-image" mode="aspectFit" />
                </swiper-item>
            </swiper>
        </block>
        <block wx:else>
            <view class='no-image'>
                <image class='img' src='/images/icon/no-img.png'></image>
            </view>
        </block>
    </view>
    <!-- 价格容器 - 独立显示 -->
    <view class="price-container">
        <!-- 秒杀价格显示 -->
        <view class="price-display" wx:if="{{isSeckill && seckillInfo}}">
            <view class="seckill-price-section">
                <view class="seckill-left">
                    <view class="seckill-label">秒杀价</view>
                    <view class="price-amount seckill-price">
                        <view class="sym">¥</view>
                        <view class="num">{{seckillInfo.flash_price}}</view>
                    </view>
                    <view class="original-price">¥{{seckillInfo.original_price}}</view>
                </view>
                <view class="seckill-right">
                    <view class="seckill-countdown" wx:if="{{seckillInfo.is_active}}">
                        <view class="countdown-label">距结束</view>
                        <view class="countdown-time">
                            <text class="time-unit">{{(seckillTimeLeft.hours < 10 ? '0' + seckillTimeLeft.hours : seckillTimeLeft.hours) + ':' + (seckillTimeLeft.minutes < 10 ? '0' + seckillTimeLeft.minutes : seckillTimeLeft.minutes) + ':' + (seckillTimeLeft.seconds < 10 ? '0' + seckillTimeLeft.seconds : seckillTimeLeft.seconds)}}</text>
                        </view>
                    </view>
                    <view class="seckill-status" wx:else>
                        <text class="status-text">{{seckillInfo.status === 'upcoming' ? '即将开始' : '已结束'}}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 普通价格显示 -->
        <view class="price-display" wx:else>
            <view class="price-amount">
                <view class="sym">¥</view>
                <view class="num">{{goods.min_retail_price || goods.retail_price}}</view>
                <view class="price-suffix">起</view>
            </view>
            <view class="sales-info">已售{{goods.sell_volume || 0}}件</view>
        </view>
    </view>

    <!-- 标题容器 -->
    <view class='title-container'>
        <view class='brand-badge'>品牌</view>
        <view class='goods-title-full'>{{goods.name}}</view>
        <view class='goods-intro'>{{goods.goods_brief}}</view>
    </view>

    <!-- 分享和库存信息 -->
    <view class='info-actions'>
        <view class="stock-info">
            <view class='stock'>库存{{goods.goods_number}}</view>
            <view class='sales'>已售{{goods.sell_volume}}份</view>
        </view>
        <view class='share-section'>
            <view wx:if="{{goods.is_distributed === 1}}" bindtap="shareTo" class='share'>
                <image class='icon share-icon-small' src='/images/icon/share.png'></image>
                <view class='text share-gift-text'>分享获得{{commissionAmount}}元奖励</view>
            </view>
            <view wx:else class='share share-disabled'>
                <image class='icon share-icon-small' src='/images/icon/share.png'></image>
                <view class='text share-disabled-text'>该商品不支持推广</view>
            </view>
        </view>
    </view>
    <view class='section-nav' bindtap="switchAttrPop">
        <view class="t">{{checkedSpecText}}</view>
        <view class="arrow"></view>
    </view>
    <view class='details-wrap'>
        <view class="title">商品详情</view>
        <!-- <ad unit-id="adunit-2d961509e15e91d7" ad-type="grid" grid-opacity="0.8" grid-count="5" ad-theme="white"></ad> -->
        <view class="show">
            <view class='details-image-wrap'>
                <import src="../../lib/wxParse/wxParse.wxml" />
                <template is="wxParse" data="{{wxParseData:goodsDetail.nodes}}" />
            </view>
        </view>
    </view>
    <view class="attr-pop-box" hidden="{{!openAttr}}" bindtap='closeAttr'>
        <view class="attr-pop" catchtap="handleTap">
            <view class="close" catchtap="closeAttr">
                <image class="icon" src="/images/icon/icon-close.png"></image>
            </view>
            <view class="img-info">
                <view class="img-wrap">
                    <image class="img" src="{{gallery[0].img_url}}"></image>
                </view>
                <view class="info">
                    <view class='price-range' wx:if="{{priceChecked == false}}">
                        <!-- todo 原价不等于0的时候要补充 -->
                        <view class='retail-price'>
                            <view class='p-title'>零售价：</view>
                            <view class='g-price'>{{checkedSpecPrice}}元</view>
                        </view>
                    </view>
                    <view class='price-range' wx:elif="{{priceChecked == true}}">
                        <view class='retail-price'>
                            <view class='p-title'>零售价：</view>
                            <view class='g-price'>{{checkedSpecPrice}}元</view>
                        </view>
                    </view>
                    <view class="a" wx:if="{{productList.length>0}}">{{tmpSpecText}}</view>
                </view>
            </view>
            <view class="spec-con">
                <view class='spec-item'>
                    <view class='name'>{{specificationList.name}}</view>
                    <view class="values">
                        <view class="value {{item.checked ? 'selected' : ''}} {{item.goods_number <=0?'out-stock':''}}" bindtap="clickSkuValue" wx:for="{{specificationList.valueList}}" wx:key="id" data-value-id="{{item.id}}" data-index="{{index}}" data-name-id="{{item.specification_id}}">{{item.value}}</view>
                    </view>
                </view>
                <view class="number-item">
                    <view class="name">{{isSeckill ? '轮次库存' : '库存'}}</view>
                    <view class='stock-num'>{{goodsNumber}}</view>
                </view>
                <view class="number-item">
                    <view class="name">数量 ({{goods.goods_unit}})</view>
                    <view class="selnum">
                        <button class="cut" bindtap="cutNumber">-</button>
                        <input value="{{number}}" class="number" type="number" cursor-spacing="100" bindblur="inputNumber" />
                        <button class="add" bindtap="addNumber" disabled='{{disabled}}'>+</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="cart-add-box">
        <view class='l'>
            <view class='left-icon form-button' bindtap='goIndexPage'>
                <form report-submit="true">
                    <button bindtap='goIndexPage' formType="submit" class='index-btn' hover-class="none">
                        <image class='icon' src='/images/nav/icon-index-b.png'></image>
                        <view class='icon-text'>主页</view>
                    </button>
                </form>
            </view>
            <button class='left-icon contact-button' session-from='{"nickName":"{{userInfo.nickname}}","avatarUrl":"{{userInfo.avatar}}"}' open-type="contact" show-message-card="true" hover-class="none">
                <image class='icon' src='/images/icon/chat.png'></image>
                <view class='icon-text'>客服</view>
            </button>
            <view class="left-icon" bindtap='openCartPage'>
                <text class="cart-count">{{cartGoodsCount}}</text>
                <image class='icon' src='/images/nav/icon-cart-b.png'></image>
                <view class='icon-text'>购物车</view>
            </view>
        </view>
        <block wx:if="{{goods.goods_number > 0 && goods.is_on_sale == 1}}">
            <!-- 秒杀商品的按钮显示逻辑 -->
            <block wx:if="{{isSeckill}}">
                <block wx:if="{{seckillInfo.stock > 0 && seckillInfo.can_buy}}">
                    <view class="to-cart-btn" bindtap='addToCart'>加入购物车</view>
                    <view class="to-pay-btn" bindtap='fastToCart'>立即购买</view>
                </block>
                <block wx:else>
                    <view class="cart-empty">商品已售罄</view>
                </block>
            </block>
            <!-- 普通商品的按钮显示逻辑 -->
            <block wx:else>
                <block wx:if="{{goodsNumber > 0}}">
                    <view class="to-cart-btn" bindtap='addToCart'>加入购物车</view>
                    <view class="to-pay-btn" bindtap='fastToCart'>立即购买</view>
                </block>
                <block wx:else>
                    <view class="cart-empty">商品已售罄</view>
                </block>
            </block>
        </block>
        <block wx:elif="{{!isSeckill && goods.goods_number <= 0}}">
            <view class="cart-empty">商品已售罄</view>
        </block>
        <block wx:elif="{{goods.is_on_sale == 0}}">
            <view class="cart-empty">商品已下架</view>
        </block>
    </view>
    <view class="dialog {{ showShareDialog ? 'dialog_show' : '' }}">
        <view class="dialog-mask2" bindtap="hideDialog"></view>
        <view class="dialog-fixed dialog-share">
            <view class="share-wrap">
                <view class='content'>
                    <view class="share-block">
                        <button class='block share-btn' hover-class="none" open-type='share'>
                            <image class="img" src="/images/icon/weixin.png"></image>
                            <view class="text">发给好友/发到微信群</view>
                        </button>
                        <view class="block" catchtap="createShareImage">
                            <image class="img" src="/images/icon/pyq.png"></image>
                            <view class="text">保存分享图发朋友圈</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="cancel" bindtap="hideDialog">取消</view>
        </view>
    </view>
</view>
<loading show="{{loading != 1}}" text="商品加载中..."></loading>