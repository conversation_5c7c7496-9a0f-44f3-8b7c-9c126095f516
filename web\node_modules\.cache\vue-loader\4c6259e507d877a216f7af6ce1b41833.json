{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue?vue&type=template&id=da052a4e&scoped=true&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue", "mtime": 1754302540662}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}