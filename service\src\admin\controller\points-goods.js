const Base = require('./base.js');

module.exports = class extends Base {
  
  /**
   * 获取积分商品列表
   * GET /admin/points-goods/list
   */
  async listAction() {
    console.log('=== 获取积分商品列表 ===');
    
    try {
      const page = this.get('page') || 1;
      const limit = this.get('limit') || 20;
      const status = this.get('status') || '';
      const category_id = this.get('category_id') || '';
      const keyword = this.get('keyword') || '';
      
      console.log('查询参数:', { page, limit, status, category_id, keyword });
      
      // 构建查询条件
      const where = { 'pg.is_delete': 0 };
      
      if (status !== '') {
        where['pg.status'] = parseInt(status);
      }
      
      if (category_id !== '') {
        where['g.category_id'] = parseInt(category_id);
      }
      
      if (keyword !== '') {
        where['g.name'] = ['like', `%${keyword}%`];
      }
      
      // 联表查询积分商品列表（关联商品表和规格表）
      const pointsGoodsModel = this.model('points_goods');
      const list = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where(where)
        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')
        .order('pg.sort DESC, pg.id DESC')
        .page(page, limit)
        .select();
      
      // 获取总数
      const total = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where(where)
        .count();
      
      // 处理规格信息
      for (let item of list) {
        if (item.goods_specification_ids) {
          // 获取规格详细信息
          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);
          item.specification_info = specInfo;
        }
      }
      
      console.log(`查询到 ${list.length} 条记录，总计 ${total} 条`);
      
      return this.success({
        list: list,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
    } catch (error) {
      console.error('获取积分商品列表失败:', error);
      return this.fail(500, '获取积分商品列表失败');
    }
  }

  /**
   * 获取可选择的商品列表（用于添加积分商品）
   * GET /admin/points-goods/available-goods
   */
  async availableGoodsAction() {
    console.log('=== 获取可选择的商品列表 ===');
    
    try {
      const page = this.get('page') || 1;
      const limit = this.get('limit') || 20;
      const keyword = this.get('keyword') || '';
      const category_id = this.get('category_id') || '';
      
      // 构建查询条件
      const where = { 
        'g.is_delete': 0,
        'g.is_on_sale': 1,
        'p.is_delete': 0,
        'p.is_on_sale': 1
      };
      
      if (keyword !== '') {
        where['g.name'] = ['like', `%${keyword}%`];
      }
      
      if (category_id !== '') {
        where['g.category_id'] = parseInt(category_id);
      }
      
      // 查询商品及其规格
      const goodsModel = this.model('goods');
      const list = await goodsModel.alias('g')
        .join('hiolabs_product p ON g.id = p.goods_id')
        .leftJoin('hiolabs_points_goods pg ON p.id = pg.product_id AND pg.is_delete = 0')
        .where(where)
        .field('g.id as goods_id, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.category_id, p.id as product_id, p.goods_specification_ids, p.retail_price, p.goods_number as stock, pg.id as points_goods_id')
        .order('g.sort_order DESC, g.id DESC')
        .page(page, limit)
        .select();
      
      // 获取总数
      const total = await goodsModel.alias('g')
        .join('hiolabs_product p ON g.id = p.goods_id')
        .leftJoin('hiolabs_points_goods pg ON p.id = pg.product_id AND pg.is_delete = 0')
        .where(where)
        .count();
      
      // 处理规格信息和状态
      for (let item of list) {
        if (item.goods_specification_ids) {
          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);
          item.specification_info = specInfo;
        }
        
        // 标记是否已添加到积分商城
        item.is_points_goods = !!item.points_goods_id;
      }
      
      console.log(`查询到 ${list.length} 条可选商品，总计 ${total} 条`);
      
      return this.success({
        list: list,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
    } catch (error) {
      console.error('获取可选择商品列表失败:', error);
      return this.fail(500, '获取可选择商品列表失败');
    }
  }

  /**
   * 获取规格信息
   */
  async getSpecificationInfo(specificationIds) {
    if (!specificationIds) return '';
    
    try {
      const specIds = specificationIds.split(',').map(id => parseInt(id));
      const specModel = this.model('goods_specification');
      const specs = await specModel.where({
        id: ['IN', specIds],
        is_delete: 0
      }).select();
      
      return specs.map(spec => spec.value).join(' / ');
    } catch (error) {
      console.error('获取规格信息失败:', error);
      return '';
    }
  }

  /**
   * 添加积分商品
   * POST /admin/points-goods/add
   */
  async addAction() {
    console.log('=== 添加积分商品 ===');
    
    try {
      const data = this.post();
      console.log('接收到的数据:', data);
      
      // 验证必填字段
      if (!data.goods_id || !data.product_id || !data.points_price) {
        return this.fail(400, '商品ID、规格ID和积分价格不能为空');
      }
      
      // 检查该规格是否已经添加
      const pointsGoodsModel = this.model('points_goods');
      const existing = await pointsGoodsModel.where({
        product_id: data.product_id,
        is_delete: 0
      }).find();
      
      if (!think.isEmpty(existing)) {
        return this.fail(400, '该商品规格已经添加到积分商城');
      }
      
      // 验证商品和规格是否存在
      const goodsModel = this.model('goods');
      const goods = await goodsModel.where({
        id: data.goods_id,
        is_delete: 0
      }).find();
      
      if (think.isEmpty(goods)) {
        return this.fail(400, '商品不存在');
      }
      
      const productModel = this.model('product');
      const product = await productModel.where({
        id: data.product_id,
        goods_id: data.goods_id,
        is_delete: 0
      }).find();
      
      if (think.isEmpty(product)) {
        return this.fail(400, '商品规格不存在');
      }
      
      // 准备插入数据
      const insertData = {
        goods_id: parseInt(data.goods_id),
        product_id: parseInt(data.product_id),
        points_price: parseInt(data.points_price),
        cash_price: parseFloat(data.cash_price || 0),
        stock_limit: parseInt(data.stock_limit || 0),
        daily_limit: parseInt(data.daily_limit || 0),
        user_limit: parseInt(data.user_limit || 0),
        sort: parseInt(data.sort || 0),
        status: parseInt(data.status || 1),
        is_hot: parseInt(data.is_hot || 0),
        start_time: data.start_time || null,
        end_time: data.end_time || null,
        description: data.description || '',
        created_at: new Date(),
        updated_at: new Date()
      };
      
      const result = await pointsGoodsModel.add(insertData);
      
      if (result) {
        console.log('积分商品添加成功，ID:', result);
        return this.success({ id: result }, '添加成功');
      } else {
        return this.fail(500, '添加失败');
      }
      
    } catch (error) {
      console.error('添加积分商品失败:', error);
      return this.fail(500, '添加积分商品失败');
    }
  }

  /**
   * 获取积分商品详情
   * GET /admin/points-goods/detail/:id
   */
  async detailAction() {
    console.log('=== 获取积分商品详情 ===');
    
    try {
      const id = this.get('id');
      
      if (!id) {
        return this.fail(400, '积分商品ID不能为空');
      }
      
      // 联表查询积分商品详情
      const pointsGoodsModel = this.model('points_goods');
      const detail = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where({
          'pg.id': id,
          'pg.is_delete': 0
        })
        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.goods_desc, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')
        .find();
      
      if (think.isEmpty(detail)) {
        return this.fail(404, '积分商品不存在');
      }
      
      // 获取规格信息
      if (detail.goods_specification_ids) {
        const specInfo = await this.getSpecificationInfo(detail.goods_specification_ids);
        detail.specification_info = specInfo;
      }
      
      console.log('积分商品详情:', detail);
      
      return this.success(detail);

    } catch (error) {
      console.error('获取积分商品详情失败:', error);
      return this.fail(500, '获取积分商品详情失败');
    }
  }

  /**
   * 更新积分商品
   * PUT /admin/points-goods/update/:id
   */
  async updateAction() {
    console.log('=== 更新积分商品 ===');

    try {
      const id = this.get('id');
      const data = this.post();

      if (!id) {
        return this.fail(400, '积分商品ID不能为空');
      }

      console.log('更新数据:', data);

      // 检查积分商品是否存在
      const pointsGoodsModel = this.model('points_goods');
      const existing = await pointsGoodsModel.where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(existing)) {
        return this.fail(404, '积分商品不存在');
      }

      // 准备更新数据
      const updateData = {
        updated_at: new Date()
      };

      // 只更新传入的字段
      if (data.points_price !== undefined) {
        updateData.points_price = parseInt(data.points_price);
      }
      if (data.cash_price !== undefined) {
        updateData.cash_price = parseFloat(data.cash_price);
      }
      if (data.stock_limit !== undefined) {
        updateData.stock_limit = parseInt(data.stock_limit);
      }
      if (data.daily_limit !== undefined) {
        updateData.daily_limit = parseInt(data.daily_limit);
      }
      if (data.user_limit !== undefined) {
        updateData.user_limit = parseInt(data.user_limit);
      }
      if (data.sort !== undefined) {
        updateData.sort = parseInt(data.sort);
      }
      if (data.status !== undefined) {
        updateData.status = parseInt(data.status);
      }
      if (data.is_hot !== undefined) {
        updateData.is_hot = parseInt(data.is_hot);
      }
      if (data.start_time !== undefined) {
        updateData.start_time = data.start_time || null;
      }
      if (data.end_time !== undefined) {
        updateData.end_time = data.end_time || null;
      }
      if (data.description !== undefined) {
        updateData.description = data.description;
      }

      const result = await pointsGoodsModel.where({ id: id }).update(updateData);

      if (result) {
        console.log('积分商品更新成功');
        return this.success({}, '更新成功');
      } else {
        return this.fail(500, '更新失败');
      }

    } catch (error) {
      console.error('更新积分商品失败:', error);
      return this.fail(500, '更新积分商品失败');
    }
  }

  /**
   * 删除积分商品
   * DELETE /admin/points-goods/delete/:id
   */
  async deleteAction() {
    console.log('=== 删除积分商品 ===');

    try {
      const id = this.get('id');

      if (!id) {
        return this.fail(400, '积分商品ID不能为空');
      }

      // 检查积分商品是否存在
      const pointsGoodsModel = this.model('points_goods');
      const existing = await pointsGoodsModel.where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(existing)) {
        return this.fail(404, '积分商品不存在');
      }

      // 软删除
      const result = await pointsGoodsModel.where({ id: id }).update({
        is_delete: 1,
        updated_at: new Date()
      });

      if (result) {
        console.log('积分商品删除成功');
        return this.success({}, '删除成功');
      } else {
        return this.fail(500, '删除失败');
      }

    } catch (error) {
      console.error('删除积分商品失败:', error);
      return this.fail(500, '删除积分商品失败');
    }
  }

  /**
   * 批量更新状态
   * POST /admin/points-goods/batch-status
   */
  async batchStatusAction() {
    console.log('=== 批量更新积分商品状态 ===');

    try {
      const data = this.post();
      const { ids, status } = data;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return this.fail(400, '请选择要操作的积分商品');
      }

      if (status === undefined) {
        return this.fail(400, '请指定状态');
      }

      console.log('批量更新:', { ids, status });

      const pointsGoodsModel = this.model('points_goods');
      const result = await pointsGoodsModel.where({
        id: ['IN', ids],
        is_delete: 0
      }).update({
        status: parseInt(status),
        updated_at: new Date()
      });

      if (result) {
        console.log('批量更新成功');
        return this.success({}, '批量更新成功');
      } else {
        return this.fail(500, '批量更新失败');
      }

    } catch (error) {
      console.error('批量更新积分商品状态失败:', error);
      return this.fail(500, '批量更新失败');
    }
  }

  /**
   * 获取统计数据
   * GET /admin/points-goods/statistics
   */
  async statisticsAction() {
    console.log('=== 获取积分商品统计数据 ===');

    try {
      const pointsGoodsModel = this.model('points_goods');
      const pointsOrdersModel = this.model('points_orders');

      // 统计积分商品数量
      const totalGoods = await pointsGoodsModel.where({ is_delete: 0 }).count();
      const onlineGoods = await pointsGoodsModel.where({ is_delete: 0, status: 1 }).count();

      // 统计兑换数据
      const totalOrders = await pointsOrdersModel.where({ is_delete: 0 }).count();
      const totalPoints = await pointsOrdersModel.where({ is_delete: 0 }).sum('total_points');

      const statistics = {
        total_goods: totalGoods,
        online_goods: onlineGoods,
        offline_goods: totalGoods - onlineGoods,
        total_orders: totalOrders,
        total_points: totalPoints || 0
      };

      console.log('统计数据:', statistics);

      return this.success(statistics);

    } catch (error) {
      console.error('获取统计数据失败:', error);
      return this.fail(500, '获取统计数据失败');
    }
  }

  /**
   * 获取商品分类
   * GET /admin/points-goods/categories
   */
  async categoriesAction() {
    console.log('=== 获取积分商品分类 ===');

    try {
      // 先尝试使用专门的积分商品分类表
      const pointsCategoryModel = this.model('points_categories');
      let categories = await pointsCategoryModel.where({
        status: 1,
        is_delete: 0
      }).field('id, name').order('sort DESC, id ASC').select();

      // 如果积分商品分类表为空，则使用普通商品分类表
      if (!categories || categories.length === 0) {
        console.log('积分商品分类表为空，使用普通商品分类表');
        const categoryModel = this.model('category');
        categories = await categoryModel.where({
          is_show: 1
        }).field('id, name, parent_id').order('sort_order ASC, id ASC').select();
      }

      console.log('分类数据:', categories);

      return this.success(categories);

    } catch (error) {
      console.error('获取分类失败:', error);
      return this.fail(500, '获取分类失败');
    }
  }

  /**
   * 获取商品的规格列表
   * GET /admin/points-goods/goods-products
   */
  async goodsProductsAction() {
    console.log('=== 获取商品规格列表 ===');

    try {
      const goodsId = this.get('goods_id');

      if (!goodsId) {
        return this.fail(400, '商品ID不能为空');
      }

      console.log('商品ID:', goodsId);

      const productModel = this.model('product');
      const specificationModel = this.model('specification');

      // 获取商品的所有规格
      const products = await productModel.where({
        goods_id: goodsId,
        is_delete: 0
      }).field('id, goods_id, goods_specification_ids, retail_price, goods_number').select();

      // 处理规格信息
      for (let product of products) {
        if (product.goods_specification_ids) {
          try {
            const specIds = JSON.parse(product.goods_specification_ids);
            if (Array.isArray(specIds) && specIds.length > 0) {
              const specs = await specificationModel.where({
                id: ['IN', specIds]
              }).field('id, value').select();

              product.specification_info = specs.map(spec => spec.value).join(' / ');
            } else {
              product.specification_info = '默认规格';
            }
          } catch (e) {
            product.specification_info = '默认规格';
          }
        } else {
          product.specification_info = '默认规格';
        }
      }

      console.log('规格列表:', products);

      return this.success(products);

    } catch (error) {
      console.error('获取商品规格失败:', error);
      return this.fail(500, '获取商品规格失败');
    }
  }

  /**
   * 更新单个商品状态
   * POST /admin/points-goods/update-status
   */
  async updateStatusAction() {
    console.log('=== 更新积分商品状态 ===');

    try {
      const data = this.post();
      const { id, status } = data;

      if (!id) {
        return this.fail(400, '积分商品ID不能为空');
      }

      if (status === undefined) {
        return this.fail(400, '状态不能为空');
      }

      console.log('更新状态:', { id, status });

      const pointsGoodsModel = this.model('points_goods');

      // 检查积分商品是否存在
      const existing = await pointsGoodsModel.where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(existing)) {
        return this.fail(404, '积分商品不存在');
      }

      // 更新状态
      const result = await pointsGoodsModel.where({ id: id }).update({
        status: parseInt(status),
        updated_at: new Date()
      });

      if (result) {
        console.log('状态更新成功');
        return this.success({}, '状态更新成功');
      } else {
        return this.fail(500, '状态更新失败');
      }

    } catch (error) {
      console.error('更新积分商品状态失败:', error);
      return this.fail(500, '状态更新失败');
    }
  }
};
