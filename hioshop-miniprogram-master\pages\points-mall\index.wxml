<!--积分商城页面-->
<view class="points-mall-page">
  
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索积分商品" 
        value="{{keyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-btn" bindtap="onSearch">
        <text class="iconfont icon-search"></text>
      </view>
      <view class="clear-btn" wx:if="{{keyword}}" bindtap="onClearSearch">
        <text class="iconfont icon-close"></text>
      </view>
    </view>
  </view>

  <!-- 分类导航 -->
  <scroll-view class="category-nav" scroll-x="true" wx:if="{{categories.length > 0}}">
    <view class="category-list">
      <view 
        class="category-item {{currentCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onCategoryChange"
      >
        {{item.name}}
      </view>
    </view>
  </scroll-view>

  <!-- 排序栏 -->
  <view class="sort-bar">
    <view class="sort-btn" bindtap="showSortMenu">
      <text>{{getCurrentSortName()}}</text>
      <text class="iconfont icon-arrow-down"></text>
    </view>
    <view class="goods-count">
      共{{total}}件商品
    </view>
  </view>

  <!-- 排序菜单 -->
  <view class="sort-menu {{showSortMenu ? 'show' : ''}}" bindtap="hideSortMenu">
    <view class="sort-menu-content" catchtap="">
      <view 
        class="sort-option {{currentSort === item.key ? 'active' : ''}}"
        wx:for="{{sortOptions}}"
        wx:key="key"
        data-key="{{item.key}}"
        bindtap="onSortChange"
      >
        {{item.name}}
        <text class="iconfont icon-check" wx:if="{{currentSort === item.key}}"></text>
      </view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="goods-list">
    <view 
      class="goods-item"
      wx:for="{{goodsList}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="goGoodsDetail"
    >
      <!-- 商品图片 -->
      <view class="goods-image-wrapper">
        <image class="goods-image" src="{{item.goods_image}}" mode="aspectFill" />
        <view class="hot-tag" wx:if="{{item.is_hot}}">热门</view>
      </view>
      
      <!-- 商品信息 -->
      <view class="goods-info">
        <view class="goods-name">{{item.goods_name}}</view>
        <view class="goods-desc" wx:if="{{item.description}}">{{item.description}}</view>
        
        <!-- 价格信息 -->
        <view class="price-info">
          <view class="points-price">
            <text class="price-num">{{item.points_price}}</text>
            <text class="price-unit">积分</text>
          </view>
          <view class="original-price" wx:if="{{item.original_price > 0}}">
            ¥{{item.original_price}}
          </view>
        </view>
        
        <!-- 销量和库存 -->
        <view class="sales-info">
          <text class="sales">已兑{{item.sold_count}}件</text>
          <text class="stock">库存{{item.stock}}件</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-wrapper" wx:if="{{loading && goodsList.length > 0}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && goodsList.length > 0}}">
    <text>没有更多商品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && goodsList.length === 0}}">
    <image class="empty-image" src="/images/empty-goods.png" />
    <view class="empty-text">暂无积分商品</view>
    <view class="empty-desc">快去添加一些商品吧</view>
  </view>

  <!-- 全局加载状态 -->
  <view class="global-loading" wx:if="{{loading && goodsList.length === 0}}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

</view>
