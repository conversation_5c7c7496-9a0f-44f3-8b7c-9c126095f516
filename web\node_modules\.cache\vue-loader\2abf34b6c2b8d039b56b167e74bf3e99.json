{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=style&index=0&id=c8ad4aac&scoped=true&lang=css&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1754251484081}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZsYXNoLXNhbGUtbXVsdGktcGFnZSB7CiAgcGFkZGluZzogMjBweDsKfQoKLnBhZ2UtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi8qIOihqOagvOaOp+WItuWMuuWfnyAqLwoudGFibGUtY29udHJvbHMgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDE1cHg7Cn0KCi5hdXRvLXJlZnJlc2gtY29udHJvbCB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTBweDsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5yZWZyZXNoLWNvdW50ZG93biB7CiAgY29sb3I6ICM2NjY7CiAgZm9udC1zaXplOiAxMnB4Owp9CgovKiDmibnph4/mk43kvZzljLrln58gKi8KLmJhdGNoLW9wZXJhdGlvbnMgewogIG1hcmdpbi1ib3R0b206IDE1cHg7Cn0KCi5iYXRjaC1hY3Rpb25zIHsKICBtYXJnaW4tdG9wOiAxMHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxMHB4Owp9CgovKiDnirbmgIHkv6Hmga/mmL7npLogKi8KLnN0YXR1cy1pbmZvIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7Cn0KCi50aW1lLWluZm8gewogIG1hcmdpbi10b3A6IDVweDsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM2NjY7Cn0KCi5jb3VudGRvd24gewogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjZjU2YzZjOwp9CgovKiDmk43kvZzmjInpkq7nu4QgKi8KLnJvdW5kLWFjdGlvbnMgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiA1cHg7CiAgZmxleC13cmFwOiB3cmFwOwp9Cgoucm91bmQtYWN0aW9ucyAuYnRuIHsKICBtYXJnaW4tYm90dG9tOiAycHg7Cn0KCi5wYWdlLWhlYWRlciBoMiB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjMzMzOwp9CgouYnRuIHsKICBwYWRkaW5nOiA4cHggMTZweDsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5idG4tcHJpbWFyeSB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjsKICBjb2xvcjogd2hpdGU7Cn0KCi5idG4tc2Vjb25kYXJ5IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkOwogIGNvbG9yOiB3aGl0ZTsKfQoKLmJ0bi1zbSB7CiAgcGFkZGluZzogNHB4IDhweDsKICBmb250LXNpemU6IDEycHg7Cn0KCi5yb3VuZC1hY3Rpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogOHB4OwogIGZsZXgtd3JhcDogd3JhcDsKfQoKLnJvdW5kLWFjdGlvbnMgLmJ0biB7CiAgbWFyZ2luOiAwOwp9CgouYnRuLWluZm8gewogIGJhY2tncm91bmQtY29sb3I6ICMxN2EyYjg7CiAgYm9yZGVyLWNvbG9yOiAjMTdhMmI4OwogIGNvbG9yOiB3aGl0ZTsKfQoKLmJ0bi1pbmZvOmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTM4NDk2OwogIGJvcmRlci1jb2xvcjogIzExN2E4YjsKfQoKLmJ0bi13YXJuaW5nIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZjMTA3OwogIGJvcmRlci1jb2xvcjogI2ZmYzEwNzsKICBjb2xvcjogIzIxMjUyOTsKfQoKLmJ0bi13YXJuaW5nOmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTBhODAwOwogIGJvcmRlci1jb2xvcjogI2QzOWUwMDsKfQoKLmJ0bi1kYW5nZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNkYzM1NDU7CiAgYm9yZGVyLWNvbG9yOiAjZGMzNTQ1OwogIGNvbG9yOiB3aGl0ZTsKfQoKLmJ0bi1kYW5nZXI6aG92ZXIgewogIGJhY2tncm91bmQtY29sb3I6ICNjODIzMzM7CiAgYm9yZGVyLWNvbG9yOiAjYmQyMTMwOwp9CgovKiDmlbTngrnnp5LmnYDorr7nva7moLflvI8gKi8KLmhvdXJseS1mbGFzaC1zZXR0aW5ncyB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnNldHRpbmctaGVhZGVyIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9Cgouc2V0dGluZy1oZWFkZXIgaDQgewogIG1hcmdpbjogMCAwIDVweCAwOwogIGNvbG9yOiAjMzMzOwp9Cgouc2V0dGluZy1kZXNjcmlwdGlvbiB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnRpbWUtcmFuZ2Utc2VsZWN0b3IgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDEwcHg7Cn0KCi50aW1lLXNlcGFyYXRvciB7CiAgY29sb3I6ICM2NjY7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5zbG90LXByZXZpZXcgewogIG1hcmdpbi10b3A6IDIwcHg7CiAgcGFkZGluZzogMTVweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwogIGJvcmRlci1yYWRpdXM6IDVweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOwp9Cgouc2xvdC1wcmV2aWV3IGg1IHsKICBtYXJnaW46IDAgMCAxMHB4IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5zbG90LWxpc3QgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDhweDsKfQoKLnNsb3QtaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTVweDsKICBwYWRkaW5nOiA4cHggMTJweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjsKfQoKLnNsb3QtbnVtYmVyIHsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzAwN2JmZjsKICBtaW4td2lkdGg6IDYwcHg7Cn0KCi5zbG90LXRpbWUgewogIGZsZXg6IDE7CiAgY29sb3I6ICMzMzM7Cn0KCi5zbG90LWR1cmF0aW9uIHsKICBjb2xvcjogIzI4YTc0NTsKICBmb250LXNpemU6IDEycHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5zbG90LXN1bW1hcnkgewogIG1hcmdpbi10b3A6IDEwcHg7CiAgcGFkZGluZy10b3A6IDEwcHg7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNkZWUyZTY7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjNjY2OwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9CgoudmFsaWQtc2xvdHMgewogIGNvbG9yOiAjMjhhNzQ1OwogIG1hcmdpbi1sZWZ0OiAxMHB4Owp9Cgoucm91bmQtbmFtZS1wcmV2aWV3IHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9Cgoucm91bmQtbmFtZS1wcmV2aWV3IGg1IHsKICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgY29sb3I6ICMzMzM7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5uYW1lLWRpc3BsYXkgewogIHBhZGRpbmc6IDEwcHggMTVweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwogIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzAwN2JmZjsKfQoKLnNsb3QtbGlzdC1jb250YWluZXIgewogIG1heC1oZWlnaHQ6IDMwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi5zbG90LXN0YXR1cyB7CiAgcGFkZGluZzogMnB4IDhweDsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGZvbnQtc2l6ZTogMTFweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLnNsb3Qtc3RhdHVzLnBhc3QgewogIGJhY2tncm91bmQtY29sb3I6ICNmOGQ3ZGE7CiAgY29sb3I6ICM3MjFjMjQ7Cn0KCi5zbG90LXN0YXR1cy5hY3RpdmUgewogIGJhY2tncm91bmQtY29sb3I6ICNkNGVkZGE7CiAgY29sb3I6ICMxNTU3MjQ7Cn0KCi5zbG90LXN0YXR1cy51cGNvbWluZyB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2QxZWNmMTsKICBjb2xvcjogIzBjNTQ2MDsKfQoKLm1vcmUtc2xvdHMgewogIHBhZGRpbmc6IDEwcHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjNjY2OwogIGZvbnQtc3R5bGU6IGl0YWxpYzsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwp9CgouYnRuOmRpc2FibGVkIHsKICBvcGFjaXR5OiAwLjY7CiAgY3Vyc29yOiBub3QtYWxsb3dlZDsKfQoKLyog57uf6K6h5Y2h54mHICovCi5zdGF0cy1jYXJkcyB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDIwMHB4LCAxZnIpKTsKICBnYXA6IDIwcHg7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKfQoKLnN0YXQtY2FyZCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKTsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5zdGF0LWNhcmQgaDMgewogIG1hcmdpbjogMCAwIDEwcHggMDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5zdGF0LW51bWJlciB7CiAgZm9udC1zaXplOiAyNHB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIG1hcmdpbjogMDsKICBjb2xvcjogIzMzMzsKfQoKLnN0YXQtbnVtYmVyLmFjdGl2ZSB7CiAgY29sb3I6ICMyOGE3NDU7Cn0KCi5zdGF0LW51bWJlci51cGNvbWluZyB7CiAgY29sb3I6ICNmZmMxMDc7Cn0KCi8qIOW9k+WJjei9ruasoSAqLwouY3VycmVudC1yb3VuZHMgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIHBhZGRpbmc6IDIwcHg7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnJvdW5kLWxpc3QgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDE1cHg7Cn0KCi5yb3VuZC1pdGVtIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE1cHg7CiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjsKICBib3JkZXItcmFkaXVzOiA4cHg7Cn0KCi5yb3VuZC1pdGVtLmFjdGl2ZSB7CiAgYm9yZGVyLWNvbG9yOiAjMjhhNzQ1OwogIGJhY2tncm91bmQtY29sb3I6ICNmOGZmZjk7Cn0KCi5yb3VuZC1pbmZvIGg0IHsKICBtYXJnaW46IDAgMCA1cHggMDsKICBjb2xvcjogIzMzMzsKfQoKLnJvdW5kLWluZm8gcCB7CiAgbWFyZ2luOiAycHggMDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5nb29kcy1wcmV2aWV3IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxMHB4Owp9CgouZ29vZHMtaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHdpZHRoOiA4MHB4Owp9CgouZ29vZHMtaXRlbSBpbWcgewogIHdpZHRoOiA0MHB4OwogIGhlaWdodDogNDBweDsKICBvYmplY3QtZml0OiBjb3ZlcjsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgbWFyZ2luLWJvdHRvbTogNHB4Owp9CgouZ29vZHMtaXRlbSBzcGFuIHsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICM2NjY7Cn0KCi5nb29kcy1pdGVtIC5wcmljZSB7CiAgY29sb3I6ICNlNzRjM2M7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5tb3JlLWdvb2RzIHsKICBjb2xvcjogIzAwN2JmZjsKICBmb250LXNpemU6IDEycHg7Cn0KCi8qIOihqOagvCAqLwoucm91bmRzLXRhYmxlIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpOwp9CgoudGFibGUgewogIHdpZHRoOiAxMDAlOwogIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7CiAgbWFyZ2luLXRvcDogMTVweDsKfQoKLnRhYmxlIHRoLAoudGFibGUgdGQgewogIHBhZGRpbmc6IDEycHg7CiAgdGV4dC1hbGlnbjogbGVmdDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLnRhYmxlIHRoIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5zdGF0dXMtdXBjb21pbmcgewogIGNvbG9yOiAjZmZjMTA3OwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9Cgouc3RhdHVzLWFjdGl2ZSB7CiAgY29sb3I6ICMyOGE3NDU7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0KCi5zdGF0dXMtZW5kZWQgewogIGNvbG9yOiAjNmM3NTdkOwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9CgovKiDmqKHmgIHmoYYgKi8KLm1vZGFsLW92ZXJsYXkgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjUpOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB6LWluZGV4OiAxMDAwOwp9CgoubW9kYWwtY29udGVudCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHdpZHRoOiA5MCU7CiAgbWF4LXdpZHRoOiA4MDBweDsKICBtYXgtaGVpZ2h0OiA5MHZoOwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi5tb2RhbC1jb250ZW50LmxhcmdlIHsKICBtYXgtd2lkdGg6IDEwMDBweDsKfQoKLm1vZGFsLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOwp9CgoubW9kYWwtaGVhZGVyIGgzIHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5jbG9zZS1idG4gewogIGJhY2tncm91bmQ6IG5vbmU7CiAgYm9yZGVyOiBub25lOwogIGZvbnQtc2l6ZTogMjRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgY29sb3I6ICM2NjY7Cn0KCi5tb2RhbC1ib2R5IHsKICBwYWRkaW5nOiAyMHB4Owp9CgoubW9kYWwtZm9vdGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgZ2FwOiAxMHB4OwogIHBhZGRpbmc6IDIwcHg7CiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7Cn0KCi8qIOihqOWNlSAqLwouZm9ybS1ncm91cCB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmZvcm0tcm93IHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMTVweDsKfQoKLmZvcm0tcm93IC5mb3JtLWdyb3VwIHsKICBmbGV4OiAxOwp9CgouZm9ybS1ncm91cCBsYWJlbCB7CiAgZGlzcGxheTogYmxvY2s7CiAgbWFyZ2luLWJvdHRvbTogNXB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5mb3JtLWNvbnRyb2wgewogIHdpZHRoOiAxMDAlOwogIHBhZGRpbmc6IDhweCAxMnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLmZvcm0tY29udHJvbC5zbWFsbCB7CiAgd2lkdGg6IDgwcHg7Cn0KCi5mb3JtLWNvbnRyb2w6Zm9jdXMgewogIG91dGxpbmU6IG5vbmU7CiAgYm9yZGVyLWNvbG9yOiAjMDA3YmZmOwogIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDAsIDEyMywgMjU1LCAwLjI1KTsKfQoKLyog5ZWG5ZOB6YCJ5oup572R5qC8ICovCi5nb29kcy1zZWxlY3Rpb24tZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMDBweCwgMWZyKSk7CiAgZ2FwOiAyMHB4OwogIG1heC1oZWlnaHQ6IDUwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgcGFkZGluZzogMTBweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwp9CgovKiDllYblk4HljaHniYcgKi8KLmdvb2RzLWNhcmQgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgouZ29vZHMtY2FyZDpob3ZlciB7CiAgYm9yZGVyLWNvbG9yOiAjMDA3YmZmOwogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAxMjMsIDI1NSwgMC4xNSk7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwp9CgouZ29vZHMtY2FyZC5zZWxlY3RlZCB7CiAgYm9yZGVyLWNvbG9yOiAjMjhhNzQ1OwogIGJhY2tncm91bmQtY29sb3I6ICNmOGZmZjk7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDQwLCAxNjcsIDY5LCAwLjIpOwp9CgouZ29vZHMtY2FyZC5kaXNhYmxlZCB7CiAgb3BhY2l0eTogMC42OwogIGN1cnNvcjogbm90LWFsbG93ZWQ7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsKICBib3JkZXItY29sb3I6ICNkZWUyZTY7Cn0KCi5nb29kcy1jYXJkLmRpc2FibGVkOmhvdmVyIHsKICB0cmFuc2Zvcm06IG5vbmU7CiAgYm94LXNoYWRvdzogbm9uZTsKICBib3JkZXItY29sb3I6ICNkZWUyZTY7Cn0KCi8qIOWVhuWTgeWNoeeJh+WktOmDqCAqLwouZ29vZHMtY2FyZC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQoKLmdvb2RzLWltYWdlLWNvbnRhaW5lciB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIG1hcmdpbi1yaWdodDogMTVweDsKICBmbGV4LXNocmluazogMDsKfQoKLmdvb2RzLWNhcmQtaW1hZ2UgewogIHdpZHRoOiA4MHB4OwogIGhlaWdodDogODBweDsKICBvYmplY3QtZml0OiBjb3ZlcjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLnNlbGVjdGVkLWJhZGdlIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiAtNXB4OwogIHJpZ2h0OiAtNXB4OwogIHdpZHRoOiAyNHB4OwogIGhlaWdodDogMjRweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1OwogIGJvcmRlci1yYWRpdXM6IDUwJTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYm9yZGVyOiAycHggc29saWQgd2hpdGU7CiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwwLDAsMC4yKTsKfQoKLmNoZWNrbWFyayB7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTJweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLmRpc2FibGVkLW92ZXJsYXkgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjcpOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTJweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLyog5ZWG5ZOB5L+h5oGvICovCi5nb29kcy1jYXJkLWluZm8gewogIGZsZXg6IDE7Cn0KCi5nb29kcy1uYW1lIHsKICBtYXJnaW46IDAgMCA4cHggMDsKICBjb2xvcjogIzMzMzsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBsaW5lLWhlaWdodDogMS40OwogIGRpc3BsYXk6IC13ZWJraXQtYm94OwogIC13ZWJraXQtbGluZS1jbGFtcDogMjsKICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5vcmlnaW5hbC1wcmljZSB7CiAgbWFyZ2luOiAwIDAgNXB4IDA7CiAgY29sb3I6ICM2NjY7CiAgZm9udC1zaXplOiAxNHB4Owp9Cgoud2FybmluZy10ZXh0IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgY29sb3I6ICNkYzM1NDU7CiAgZm9udC1zaXplOiAxMnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9Cgoud2FybmluZy1pY29uIHsKICBtYXJnaW4tcmlnaHQ6IDRweDsKICBmb250LXNpemU6IDE0cHg7Cn0KCi8qIOWVhuWTgeiuvue9rumdouadvyAqLwouZ29vZHMtc2V0dGluZ3MtcGFuZWwgewogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOwogIHBhZGRpbmctdG9wOiAxNXB4OwogIG1hcmdpbi10b3A6IDE1cHg7Cn0KCi5zZXR0aW5ncy10aXRsZSB7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgY29sb3I6ICMzMzM7CiAgbWFyZ2luLWJvdHRvbTogMTJweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi5zZXR0aW5ncy10aXRsZTo6YmVmb3JlIHsKICBjb250ZW50OiAi4pqZIjsKICBtYXJnaW4tcmlnaHQ6IDZweDsKICBjb2xvcjogIzAwN2JmZjsKfQoKLnNldHRpbmdzLWdyaWQgewogIGRpc3BsYXk6IGdyaWQ7CiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7CiAgZ2FwOiAxMnB4Owp9Cgouc2V0dGluZy1pdGVtIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi5zZXR0aW5nLWl0ZW0uZnVsbC13aWR0aCB7CiAgZ3JpZC1jb2x1bW46IDEgLyAtMTsKfQoKLnNldHRpbmctaXRlbSBsYWJlbCB7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjNjY2OwogIG1hcmdpbi1ib3R0b206IDRweDsKICBmb250LXdlaWdodDogNTAwOwp9CgovKiDku7fmoLzovpPlhaXnu4QgKi8KLnByaWNlLWlucHV0LWdyb3VwIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgYm9yZGVyOiAxcHggc29saWQgI2RkZDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKfQoKLmN1cnJlbmN5IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOwogIHBhZGRpbmc6IDZweCA4cHg7CiAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2RkZDsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2NjY7Cn0KCi5wcmljZS1pbnB1dCB7CiAgYm9yZGVyOiBub25lOwogIHBhZGRpbmc6IDZweCA4cHg7CiAgZm9udC1zaXplOiAxNHB4OwogIGZsZXg6IDE7CiAgb3V0bGluZTogbm9uZTsKfQoKLnByaWNlLWlucHV0OmZvY3VzIHsKICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgwLCAxMjMsIDI1NSwgMC4yNSk7Cn0KCi5kaXNjb3VudC1kaXNwbGF5IHsKICBtYXJnaW4tdG9wOiA0cHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjZTc0YzNjOwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmNWY1OwogIHBhZGRpbmc6IDJweCA2cHg7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZmVjYWNhOwp9CgovKiDlupPlrZjlkozpmZDotK3ovpPlhaUgKi8KLnN0b2NrLWlucHV0LAoubGltaXQtaW5wdXQgewogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIHBhZGRpbmc6IDZweCA4cHg7CiAgZm9udC1zaXplOiAxNHB4OwogIG91dGxpbmU6IG5vbmU7CiAgYmFja2dyb3VuZDogd2hpdGU7Cn0KCi5zdG9jay1pbnB1dDpmb2N1cywKLmxpbWl0LWlucHV0OmZvY3VzIHsKICBib3JkZXItY29sb3I6ICMwMDdiZmY7CiAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuMjUpOwp9CgovKiDmipjmiaPorr7nva7moLflvI8gKi8KLmRpc2NvdW50LXNldHRpbmcgewogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgcGFkZGluZzogMTJweDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjsKfQoKLmRpc2NvdW50LWlucHV0LWdyb3VwIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogOHB4Owp9CgouZGlzY291bnQtaW5wdXQgewogIHdpZHRoOiA4MHB4OwogIHBhZGRpbmc6IDZweCA4cHg7CiAgYm9yZGVyOiAxcHggc29saWQgI2RkZDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgZm9udC1zaXplOiAxNHB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKICBmb250LXdlaWdodDogNjAwOwp9CgouZGlzY291bnQtdW5pdCB7CiAgbWFyZ2luLWxlZnQ6IDhweDsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogI2RjMzU0NTsKfQoKLnByaWNlLXByZXZpZXcgewogIGZvbnQtc2l6ZTogMTNweDsKICBjb2xvcjogIzY2NjsKICBtYXJnaW4tYm90dG9tOiA0cHg7Cn0KCi5wcmljZS1yYW5nZS1oaW50IHsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICMyOGE3NDU7CiAgZm9udC1zdHlsZTogaXRhbGljOwp9CgovKiDpgInmi6nmj5DnpLogKi8KLnNlbGVjdGlvbi1oaW50IHsKICBtYXJnaW4tdG9wOiAxNXB4OwogIHBhZGRpbmc6IDEycHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMDA3YmZmOwp9CgouaGludC10ZXh0IHsKICBtYXJnaW46IDA7CiAgY29sb3I6ICM2NjY7CiAgZm9udC1zaXplOiAxNHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKfQoKLmluZm8taWNvbiB7CiAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgY29sb3I6ICMwMDdiZmY7CiAgZm9udC1zaXplOiAxNnB4Owp9Cgouc2VsZWN0ZWQtY291bnQgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzMzMzsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKfQoKLnNlbGVjdGVkLWNvdW50IHN0cm9uZyB7CiAgY29sb3I6ICMyOGE3NDU7CiAgZm9udC1zaXplOiAxNnB4Owp9CgovKiDlt7LpgInllYblk4HmsYfmgLsgKi8KLnNlbGVjdGVkLXN1bW1hcnkgewogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7CiAgcGFkZGluZzogMTVweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgbWFyZ2luLXRvcDogMjBweDsKfQoKLnNlbGVjdGVkLXN1bW1hcnkgaDQgewogIG1hcmdpbjogMCAwIDE1cHggMDsKICBjb2xvcjogIzMzMzsKfQoKLnN1bW1hcnktbGlzdCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogMTBweDsKfQoKLnN1bW1hcnktaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxMHB4OwogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5yZW1vdmUtYnRuIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGMzNTQ1OwogIGNvbG9yOiB3aGl0ZTsKICBib3JkZXI6IG5vbmU7CiAgcGFkZGluZzogNHB4IDhweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIGZvbnQtc2l6ZTogMTJweDsKfQoKLyog6L2u5qyh6K+m5oOFICovCi5yb3VuZC1kZXRhaWxzIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAyMHB4Owp9CgouZGV0YWlsLXNlY3Rpb24gaDQgewogIG1hcmdpbjogMCAwIDE1cHggMDsKICBjb2xvcjogIzMzMzsKICBib3JkZXItYm90dG9tOiAycHggc29saWQgIzAwN2JmZjsKICBwYWRkaW5nLWJvdHRvbTogNXB4Owp9CgouZGV0YWlsLXNlY3Rpb24gcCB7CiAgbWFyZ2luOiA1cHggMDsKICBjb2xvcjogIzY2NjsKfQoKLyog5Yqg6L2954q25oCB5qC35byPICovCi5sb2FkaW5nLXN0YXRlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgcGFkZGluZzogNDBweCAyMHB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyOiAycHggZGFzaGVkICNkZWUyZTY7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGNvbG9yOiAjNmM3NTdkOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLmxvYWRpbmctc3RhdGUgLmxvYWRpbmctaWNvbiB7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIGZvbnQtc2l6ZTogMjBweDsKICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlOwp9CgpAa2V5ZnJhbWVzIHNwaW4gewogIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfQogIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfQp9CgovKiDnqbrnirbmgIHmoLflvI8gKi8KLmVtcHR5LXN0YXRlIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBwYWRkaW5nOiA2MHB4IDIwcHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXI6IDJweCBkYXNoZWQgI2RlZTJlNjsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9CgouZW1wdHktc3RhdGUgLmVtcHR5LWljb24gewogIGZvbnQtc2l6ZTogNDhweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIG9wYWNpdHk6IDAuNjsKfQoKLmVtcHR5LXN0YXRlIC5lbXB0eS10ZXh0IHsKICBmb250LXNpemU6IDE4cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzQ5NTA1NzsKICBtYXJnaW46IDAgMCA4cHggMDsKfQoKLmVtcHR5LXN0YXRlIC5lbXB0eS1oaW50IHsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2Yzc1N2Q7CiAgbWFyZ2luOiAwIDAgMjBweCAwOwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCi5lbXB0eS1zdGF0ZSAuYnRuLXNtIHsKICBwYWRkaW5nOiA2cHggMTZweDsKICBmb250LXNpemU6IDE0cHg7Cn0KCi5nb29kcy1jZWxsIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxMHB4Owp9CgouZ29vZHMtY2VsbCBpbWcgewogIHdpZHRoOiA0MHB4OwogIGhlaWdodDogNDBweDsKICBvYmplY3QtZml0OiBjb3ZlcjsKICBib3JkZXItcmFkaXVzOiA0cHg7Cn0KCi5nb29kcy1jZWxsIHNwYW4gewogIGZvbnQtc2l6ZTogMTRweDsKfQoKLyog54q25oCB5b6956ug5qC35byPICovCi5iYWRnZSB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIHBhZGRpbmc6IDRweCA4cHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKfQoKLmJhZGdlLXdhcm5pbmcgewogIGJhY2tncm91bmQtY29sb3I6ICNmZmMxMDc7CiAgY29sb3I6ICMyMTI1Mjk7Cn0KCi5iYWRnZS1zdWNjZXNzIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1OwogIGNvbG9yOiB3aGl0ZTsKfQoKLmJhZGdlLXNlY29uZGFyeSB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzU3ZDsKICBjb2xvcjogd2hpdGU7Cn0KCi5iYWRnZS1saWdodCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKICBjb2xvcjogIzZjNzU3ZDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGVlMmU2Owp9CgovKiDmqKHmgIHmoYbmoLflvI/lop7lvLogKi8KLm1vZGFsLWNvbnRlbnQuc21hbGwgewogIG1heC13aWR0aDogNTAwcHg7Cn0KCi5tb2RhbC1oZWFkZXIgaDQgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzMzMzsKICBmb250LXNpemU6IDE4cHg7Cn0KCi8qIOihqOWNleaOp+S7tuWinuW8uiAqLwouZm9ybS1jb250cm9sW3R5cGU9Im51bWJlciJdIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi5mb3JtLWNvbnRyb2xbdHlwZT0iZGF0ZXRpbWUtbG9jYWwiXSB7CiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsKfQoKLyog5oyJ6ZKu5qC35byP5aKe5by6ICovCi5idG46aG92ZXIgewogIG9wYWNpdHk6IDAuOTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7CiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTsKfQoKLmJ0bi1zdWNjZXNzIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1OwogIGJvcmRlci1jb2xvcjogIzI4YTc0NTsKICBjb2xvcjogd2hpdGU7Cn0KCi5idG4tc3VjY2Vzczpob3ZlciB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzIxODgzODsKICBib3JkZXItY29sb3I6ICMxZTdlMzQ7Cn0K"}, {"version": 3, "sources": ["FlashSaleMultiPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqhDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FlashSaleMultiPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"flash-sale-multi-page\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>限时秒杀管理（多商品轮次）</h2>\n      <button @click=\"openCreateModal\" class=\"btn btn-primary\">\n        创建新轮次\n      </button>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <h3>总轮次</h3>\n        <p class=\"stat-number\">{{ statistics.totalRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>进行中</h3>\n        <p class=\"stat-number active\">{{ statistics.activeRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>即将开始</h3>\n        <p class=\"stat-number upcoming\">{{ statistics.upcomingRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>总销售额</h3>\n        <p class=\"stat-number\">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>\n      </div>\n    </div>\n\n    <!-- 当前轮次 -->\n    <div class=\"current-rounds\" v-if=\"currentRounds.current && currentRounds.current.length > 0\">\n      <h3>当前进行中的轮次</h3>\n      <div class=\"round-list\">\n        <div v-for=\"round in currentRounds.current\" :key=\"round.id\" class=\"round-item active\">\n          <div class=\"round-info\">\n            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>\n            <p>商品数量: {{ round.goods_count }}</p>\n            <p>剩余时间: {{ formatTime(round.countdown) }}</p>\n          </div>\n          <div class=\"goods-preview\">\n            <div v-for=\"goods in round.goods_list.slice(0, 3)\" :key=\"goods.id\" class=\"goods-item\">\n              <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n              <span>{{ goods.goods_name }}</span>\n              <span class=\"price\">¥{{ goods.flash_price }}</span>\n            </div>\n            <span v-if=\"round.goods_list.length > 3\" class=\"more-goods\">\n              +{{ round.goods_list.length - 3 }}个商品\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次列表 -->\n    <div class=\"rounds-table\">\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\n        <h3>轮次列表</h3>\n        <div class=\"table-controls\">\n          <div class=\"auto-refresh-control\">\n            <label class=\"form-check-label\">\n              <input\n                type=\"checkbox\"\n                v-model=\"autoRefresh\"\n                @change=\"toggleAutoRefresh\"\n                class=\"form-check-input\"\n              >\n              自动刷新 ({{ refreshInterval / 1000 }}秒)\n            </label>\n            <span v-if=\"autoRefresh\" class=\"refresh-countdown\">\n              下次刷新: {{ refreshCountdown }}秒\n            </span>\n          </div>\n          <button @click=\"refreshData\" class=\"btn btn-secondary btn-sm\">\n            <i class=\"fa fa-refresh\" :class=\"{ 'fa-spin': loading }\"></i>\n            手动刷新\n          </button>\n        </div>\n      </div>\n\n      <!-- 批量操作区域 -->\n      <div v-if=\"selectedRounds.length > 0\" class=\"batch-operations mb-3\">\n        <div class=\"alert alert-info\">\n          已选择 {{ selectedRounds.length }} 个轮次\n          <div class=\"batch-actions\">\n            <button @click=\"batchCloseRounds\" class=\"btn btn-warning btn-sm\">批量关闭</button>\n            <button @click=\"batchDeleteRounds\" class=\"btn btn-danger btn-sm\">批量删除</button>\n            <button @click=\"clearSelection\" class=\"btn btn-secondary btn-sm\">取消选择</button>\n          </div>\n        </div>\n      </div>\n\n      <table class=\"table\">\n        <thead>\n          <tr>\n            <th>\n              <input\n                type=\"checkbox\"\n                @change=\"toggleSelectAll\"\n                :checked=\"isAllSelected\"\n                :indeterminate.prop=\"isIndeterminate\"\n              >\n            </th>\n            <th>轮次编号</th>\n            <th>轮次名称</th>\n            <th>商品数量</th>\n            <th>总库存</th>\n            <th>已售出</th>\n            <th>开始时间</th>\n            <th>结束时间</th>\n            <th>状态</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n            <td>\n              <input\n                type=\"checkbox\"\n                :value=\"round.id\"\n                v-model=\"selectedRounds\"\n                @change=\"updateSelection\"\n              >\n            </td>\n            <td>#{{ round.round_number }}</td>\n            <td>{{ round.round_name }}</td>\n            <td>{{ round.goods_count }}</td>\n            <td>{{ round.total_stock }}</td>\n            <td>{{ round.total_sold }}</td>\n            <td>{{ formatDateTime(round.start_time) }}</td>\n            <td>{{ formatDateTime(round.end_time) }}</td>\n            <td>\n              <div class=\"status-info\">\n                <span :class=\"getStatusClass(round.status)\">{{ getStatusText(round.status) }}</span>\n                <div class=\"time-info\">\n                  <small v-if=\"round.status === 'upcoming'\">\n                    开始时间: {{ formatTime(round.start_time) }}\n                    <span v-if=\"getCountdown(round.start_time)\" class=\"countdown\">\n                      ({{ getCountdown(round.start_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'active'\">\n                    结束时间: {{ formatTime(round.end_time) }}\n                    <span v-if=\"getCountdown(round.end_time)\" class=\"countdown text-danger\">\n                      ({{ getCountdown(round.end_time) }})\n                    </span>\n                  </small>\n                  <small v-else-if=\"round.status === 'ended'\">\n                    已于 {{ formatTime(round.end_time) }} 结束\n                  </small>\n                </div>\n              </div>\n            </td>\n            <td>\n              <div class=\"round-actions\">\n                <button @click=\"viewRoundDetails(round)\" class=\"btn btn-info btn-sm\">查看详情</button>\n\n                <!-- 即将开始的轮次 -->\n                <template v-if=\"round.status === 'upcoming'\">\n                  <button @click=\"editRound(round)\" class=\"btn btn-success btn-sm\">编辑</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">取消轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 进行中的轮次 -->\n                <template v-if=\"round.status === 'active'\">\n                  <button @click=\"showExtendModal(round)\" class=\"btn btn-primary btn-sm\">延期</button>\n                  <button @click=\"closeRound(round)\" class=\"btn btn-warning btn-sm\">立即结束</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n\n                <!-- 已结束的轮次 -->\n                <template v-if=\"round.status === 'ended'\">\n                  <button @click=\"showRestartModal(round)\" class=\"btn btn-success btn-sm\">重新启动</button>\n                  <button @click=\"showCopyModal(round)\" class=\"btn btn-secondary btn-sm\">复制轮次</button>\n                  <button @click=\"deleteRound(round)\" class=\"btn btn-danger btn-sm\">删除</button>\n                </template>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 延期轮次模态框 -->\n    <div v-if=\"showExtendModalFlag\" class=\"modal-overlay\" @click=\"closeExtendModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>延期轮次</h4>\n          <button @click=\"closeExtendModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ extendRound && extendRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>延期时间 (分钟):</label>\n            <input\n              type=\"number\"\n              v-model=\"extendMinutes\"\n              class=\"form-control\"\n              min=\"1\"\n              max=\"1440\"\n              placeholder=\"请输入延期分钟数\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmExtend\" class=\"btn btn-primary\" :disabled=\"!extendMinutes\">确认延期</button>\n          <button @click=\"closeExtendModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重新启动轮次模态框 -->\n    <div v-if=\"showRestartModalFlag\" class=\"modal-overlay\" @click=\"closeRestartModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>重新启动轮次</h4>\n          <button @click=\"closeRestartModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>轮次: {{ restartRound && restartRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>新结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"newEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmRestart\" class=\"btn btn-primary\">确认重启</button>\n          <button @click=\"closeRestartModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 复制轮次模态框 -->\n    <div v-if=\"showCopyModalFlag\" class=\"modal-overlay\" @click=\"closeCopyModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h4>复制轮次</h4>\n          <button @click=\"closeCopyModal\" class=\"close-btn\">&times;</button>\n        </div>\n        <div class=\"modal-body\">\n          <p>源轮次: {{ copyRound && copyRound.round_name }}</p>\n          <div class=\"form-group\">\n            <label>新轮次名称:</label>\n            <input\n              type=\"text\"\n              v-model=\"newRoundName\"\n              class=\"form-control\"\n              :placeholder=\"copyRound && copyRound.round_name ? copyRound.round_name + ' (复制)' : ''\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>开始时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyStartTime\"\n              class=\"form-control\"\n            >\n          </div>\n          <div class=\"form-group\">\n            <label>结束时间:</label>\n            <input\n              type=\"datetime-local\"\n              v-model=\"copyEndTime\"\n              class=\"form-control\"\n            >\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button @click=\"confirmCopy\" class=\"btn btn-primary\">确认复制</button>\n          <button @click=\"closeCopyModal\" class=\"btn btn-secondary\">取消</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>创建新轮次</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <!-- 整点秒杀时间设置 -->\n          <div class=\"hourly-flash-settings\">\n            <div class=\"setting-header\">\n              <h4>整点秒杀设置</h4>\n              <p class=\"setting-description\">每小时整点开始，持续55分钟，24小时不间断</p>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label>活动开始日期 *</label>\n                <input\n                  v-model=\"newRound.start_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  required\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>活动结束日期 *</label>\n                <input\n                  v-model=\"newRound.end_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  :min=\"newRound.start_date\"\n                  required\n                />\n              </div>\n            </div>\n\n            <!-- 自动生成的轮次名称预览 -->\n            <div v-if=\"generatedRoundName\" class=\"round-name-preview\">\n              <h5>轮次名称：</h5>\n              <div class=\"name-display\">{{ generatedRoundName }}</div>\n            </div>\n\n            <!-- 时段预览 -->\n            <div v-if=\"hourlySlotPreview.length > 0\" class=\"slot-preview\">\n              <h5>将生成以下秒杀时段：</h5>\n              <div class=\"slot-list-container\">\n                <div class=\"slot-list\">\n                  <div v-for=\"(slot, index) in hourlySlotPreview\" :key=\"index\" class=\"slot-item\">\n                    <span class=\"slot-number\">第{{ index + 1 }}场</span>\n                    <span class=\"slot-time\">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>\n                    <span class=\"slot-duration\">55分钟</span>\n                    <span v-if=\"isSlotInPast(slot.start)\" class=\"slot-status past\">已过期</span>\n                    <span v-else-if=\"isSlotActive(slot.start, slot.end)\" class=\"slot-status active\">进行中</span>\n                    <span v-else class=\"slot-status upcoming\">待开始</span>\n                  </div>\n                  <div v-if=\"hourlySlotPreview.length > 10\" class=\"more-slots\">\n                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段\n                  </div>\n                </div>\n              </div>\n              <div class=\"slot-summary\">\n                <span>共 {{ hourlySlotPreview.length }} 个时段</span>\n                <span class=\"valid-slots\">（有效时段：{{ validSlotsCount }} 个）</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 商品选择 -->\n          <div class=\"form-group\">\n            <label>选择商品 * (点击商品卡片进行选择)</label>\n\n            <!-- 加载状态 -->\n            <div v-if=\"loadingGoods\" class=\"loading-state\">\n              <i class=\"loading-icon\">⏳</i>\n              <span>正在加载商品列表...</span>\n            </div>\n\n            <!-- 商品列表 -->\n            <div v-else-if=\"goodsList && goodsList.length > 0\" class=\"goods-selection-grid\">\n              <div\n                v-for=\"goods in goodsList\"\n                :key=\"goods.id\"\n                class=\"goods-card\"\n                :class=\"{\n                  'selected': isGoodsSelected(goods.id),\n                  'disabled': !goods.can_select\n                }\"\n                @click=\"toggleGoods(goods)\"\n              >\n                <!-- 商品基本信息 -->\n                <div class=\"goods-card-header\">\n                  <div class=\"goods-image-container\">\n                    <img :src=\"goods.list_pic_url\" :alt=\"goods.name\" class=\"goods-card-image\" />\n                    <div v-if=\"isGoodsSelected(goods.id)\" class=\"selected-badge\">\n                      <i class=\"checkmark\">✓</i>\n                    </div>\n                    <div v-if=\"!goods.can_select\" class=\"disabled-overlay\">\n                      <span>已参与其他秒杀</span>\n                    </div>\n                  </div>\n                  <div class=\"goods-card-info\">\n                    <h4 class=\"goods-name\">{{ goods.name }}</h4>\n                    <p class=\"original-price\">原价: ¥{{ goods.retail_price }}</p>\n                    <div v-if=\"!goods.can_select\" class=\"warning-text\">\n                      <i class=\"warning-icon\">⚠</i>\n                      <span>已参与其他秒杀活动</span>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 秒杀设置面板 -->\n                <div v-if=\"isGoodsSelected(goods.id)\" class=\"goods-settings-panel\">\n                  <div class=\"settings-title\">秒杀设置</div>\n                  <div class=\"settings-grid\">\n                    <div class=\"setting-item full-width\">\n                      <label>折扣设置</label>\n                      <div class=\"discount-setting\">\n                        <div class=\"discount-input-group\">\n                          <input\n                            v-model.number=\"getSelectedGoods(goods.id).discount_rate\"\n                            type=\"number\"\n                            step=\"1\"\n                            min=\"10\"\n                            max=\"90\"\n                            class=\"discount-input\"\n                            @input=\"updateFlashPriceByDiscount(goods.id)\"\n                            @click.stop\n                          />\n                          <span class=\"discount-unit\">% OFF</span>\n                        </div>\n                        <div class=\"price-preview\">\n                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →\n                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}\n                        </div>\n                        <div class=\"price-range-hint\" v-if=\"goods.price_range\">\n                          商品价格区间: {{ goods.price_range }}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>秒杀库存</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).stock\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"9999\"\n                        class=\"stock-input\"\n                        @click.stop\n                      />\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>限购数量</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).limit_quantity\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"99\"\n                        class=\"limit-input\"\n                        @click.stop\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 空状态显示 -->\n            <div v-else class=\"empty-state\">\n              <div class=\"empty-icon\">📦</div>\n              <p class=\"empty-text\">暂无可选商品</p>\n              <p class=\"empty-hint\">请确保有上架的商品，且未参与其他秒杀活动</p>\n              <button @click=\"loadGoodsList\" class=\"btn btn-secondary btn-sm\">重新加载</button>\n            </div>\n\n            <!-- 选择提示 -->\n            <div class=\"selection-hint\">\n              <p v-if=\"newRound.goods_list.length === 0\" class=\"hint-text\">\n                <i class=\"info-icon\">ℹ</i>\n                请点击商品卡片选择参与秒杀的商品，可以选择多个商品\n              </p>\n              <p v-else class=\"selected-count\">\n                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品\n              </p>\n            </div>\n          </div>\n\n          <!-- 已选商品汇总 -->\n          <div v-if=\"newRound.goods_list.length > 0\" class=\"selected-summary\">\n            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>\n            <div class=\"summary-list\">\n              <div v-for=\"goods in newRound.goods_list\" :key=\"goods.goods_id\" class=\"summary-item\">\n                <span>{{ goods.goods_name }}</span>\n                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>\n                <span>库存: {{ goods.stock }}</span>\n                <button @click=\"removeGoods(goods.goods_id)\" class=\"remove-btn\">移除</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"modal-footer\">\n          <button @click=\"closeModal\" class=\"btn btn-secondary\">取消</button>\n          <button\n            @click=\"createRound\"\n            class=\"btn btn-primary\"\n            :disabled=\"!canCreateRound || isCreating\"\n          >\n            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次详情模态框 -->\n    <div v-if=\"showDetailModal\" class=\"modal-overlay\" @click=\"showDetailModal = false\">\n      <div class=\"modal-content large\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>\n          <button @click=\"showDetailModal = false\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"round-details\">\n            <div class=\"detail-section\">\n              <h4>基本信息</h4>\n              <p>轮次编号: #{{ selectedRound.round_number }}</p>\n              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>\n              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>\n              <p>状态: {{ getStatusText(selectedRound.status) }}</p>\n            </div>\n            \n            <div class=\"detail-section\">\n              <h4>商品列表</h4>\n              <table class=\"table\">\n                <thead>\n                  <tr>\n                    <th>商品</th>\n                    <th>原价</th>\n                    <th>秒杀价</th>\n                    <th>折扣</th>\n                    <th>库存</th>\n                    <th>已售</th>\n                    <th>限购</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"goods in selectedRound.goods_list\" :key=\"goods.id\">\n                    <td>\n                      <div class=\"goods-cell\">\n                        <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n                        <span>{{ goods.goods_name }}</span>\n                      </div>\n                    </td>\n                    <td>¥{{ goods.original_price }}</td>\n                    <td>¥{{ goods.flash_price }}</td>\n                    <td>{{ goods.discount_rate }}%</td>\n                    <td>{{ goods.stock }}</td>\n                    <td>{{ goods.sold_count }}</td>\n                    <td>{{ goods.limit_quantity }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleMultiPage',\n  data() {\n    return {\n      statistics: {},\n      currentRounds: { current: [], upcoming: [] },\n      roundsList: { data: [], count: 0 },\n      goodsList: [],\n      loadingGoods: false,\n      showAddModal: false,\n      showDetailModal: false,\n      selectedRound: {},\n      isCreating: false,\n      newRound: {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      },\n      refreshTimer: null,\n      creationProgress: {\n        current: 0,\n        total: 0\n      },\n\n      // 批量操作相关\n      selectedRounds: [],\n\n      // 自动刷新相关\n      autoRefresh: false,\n      refreshInterval: 30000, // 30秒\n      refreshCountdown: 0,\n      countdownTimer: null,\n      loading: false,\n\n      // 延期模态框\n      showExtendModalFlag: false,\n      extendRound: null,\n      extendMinutes: null,\n\n      // 重启模态框\n      showRestartModalFlag: false,\n      restartRound: null,\n      newStartTime: '',\n      newEndTime: '',\n\n      // 复制模态框\n      showCopyModalFlag: false,\n      copyRound: null,\n      newRoundName: '',\n      copyStartTime: '',\n      copyEndTime: ''\n    };\n  },\n  \n  computed: {\n    canCreateRound() {\n      const hasStartDate = this.newRound.start_date;\n      const hasEndDate = this.newRound.end_date;\n      const hasGoods = this.newRound.goods_list.length > 0;\n      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);\n      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);\n\n      console.log('canCreateRound检查:', {\n        hasStartDate,\n        hasEndDate,\n        hasGoods,\n        goodsValid,\n        dateValid,\n        goodsList: this.newRound.goods_list\n      });\n\n      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;\n    },\n\n    // 批量选择相关计算属性\n    isAllSelected() {\n      return this.roundsList.data.length > 0 && this.selectedRounds.length === this.roundsList.data.length;\n    },\n\n    isIndeterminate() {\n      return this.selectedRounds.length > 0 && this.selectedRounds.length < this.roundsList.data.length;\n    },\n\n    // 自动生成轮次名称\n    generatedRoundName() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return '';\n      }\n\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      if (startDate.getTime() === endDate.getTime()) {\n        // 单日活动\n        const dateStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${dateStr}整点秒杀`;\n      } else {\n        // 多日活动\n        const startStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        const endStr = endDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${startStr}至${endStr}整点秒杀`;\n      }\n    },\n\n    // 整点秒杀时段预览（24小时全天候）\n    hourlySlotPreview() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return [];\n      }\n\n      const slots = [];\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      // 设置结束日期为当天的23:59:59\n      endDate.setHours(23, 59, 59, 999);\n\n      let currentDate = new Date(startDate);\n      currentDate.setHours(0, 0, 0, 0); // 从00:00开始\n\n      while (currentDate <= endDate) {\n        for (let hour = 0; hour < 24; hour++) {\n          const slotStart = new Date(currentDate);\n          slotStart.setHours(hour, 0, 0, 0);\n\n          const slotEnd = new Date(currentDate);\n          slotEnd.setHours(hour, 55, 0, 0);\n\n          // 检查轮次开始时间是否超出结束日期\n          if (slotStart > endDate) {\n            break;\n          }\n\n          slots.push({\n            start: this.formatLocalDateTime(slotStart),\n            end: this.formatLocalDateTime(slotEnd),\n            startTime: slotStart,\n            endTime: slotEnd\n          });\n        }\n\n        // 移动到下一天\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n      return slots;\n    },\n\n    // 有效时段数量（未过期的时段）\n    validSlotsCount() {\n      const now = new Date();\n      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;\n    }\n  },\n\n  mounted() {\n    console.log('FlashSaleMultiPage组件已挂载');\n    console.log('初始showAddModal值:', this.showAddModal);\n    this.loadData();\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n    if (this.countdownTimer) {\n      clearInterval(this.countdownTimer);\n    }\n  },\n\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList()\n      ]);\n    },\n\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalemulti/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalemulti/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    async loadRoundsList() {\n      try {\n        const response = await this.axios.get('flashsalemulti/list');\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    async loadGoodsList() {\n      try {\n        this.loadingGoods = true;\n        console.log('开始加载商品列表...');\n\n        const response = await this.axios.get('flashsalemulti/goods');\n        console.log('商品列表API响应:', response.data);\n\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data || [];\n          console.log('商品列表加载成功，数量:', this.goodsList.length);\n\n          if (this.goodsList.length === 0) {\n            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');\n          }\n        } else {\n          console.error('API返回错误:', response.data.errmsg);\n          this.$message.error(response.data.errmsg || '加载商品列表失败');\n          this.goodsList = [];\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n        this.$message.error('网络错误，请检查服务器连接');\n        this.goodsList = [];\n      } finally {\n        this.loadingGoods = false;\n      }\n    },\n\n    startAutoRefresh() {\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 30000); // 30秒刷新一次\n    },\n\n    isGoodsSelected(goodsId) {\n      return this.newRound.goods_list.some(g => g.goods_id === goodsId);\n    },\n\n    getSelectedGoods(goodsId) {\n      return this.newRound.goods_list.find(g => g.goods_id === goodsId);\n    },\n\n    toggleGoods(goods) {\n      if (!goods.can_select) return;\n\n      if (this.isGoodsSelected(goods.id)) {\n        this.removeGoods(goods.id);\n      } else {\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const defaultDiscount = 20; // 默认20%折扣\n        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;\n\n        this.newRound.goods_list.push({\n          goods_id: goods.id,\n          goods_name: goods.name,\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: defaultDiscount,\n          stock: 100,\n          limit_quantity: 1\n        });\n      }\n    },\n\n    removeGoods(goodsId) {\n      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);\n      if (index > -1) {\n        this.newRound.goods_list.splice(index, 1);\n      }\n    },\n\n    calculateDiscountRate(originalPrice, flashPrice) {\n      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;\n      const rate = Math.round((1 - flashPrice / originalPrice) * 100);\n      return isNaN(rate) ? 0 : rate;\n    },\n\n    updateFlashPriceByDiscount(goodsId) {\n      const selectedGoods = this.getSelectedGoods(goodsId);\n      if (selectedGoods && selectedGoods.original_price > 0) {\n        const discountRate = selectedGoods.discount_rate || 0;\n        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;\n        selectedGoods.flash_price = flashPrice;\n      }\n    },\n\n    // 格式化本地日期时间为字符串（避免时区问题）\n    formatLocalDateTime(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n      const seconds = String(d.getSeconds()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n\n    // 格式化本地日期为字符串（避免时区问题）\n    formatLocalDate(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    },\n\n    getCurrentDateTime() {\n      const now = new Date();\n      return this.formatLocalDateTime(now).slice(0, 16).replace(' ', 'T');\n    },\n\n    getCurrentDate() {\n      const now = new Date();\n      return this.formatLocalDate(now);\n    },\n\n    openCreateModal() {\n      console.log('点击创建新轮次按钮');\n      // 设置默认日期为今天\n      const today = this.getCurrentDate();\n      this.newRound.start_date = today;\n      this.newRound.end_date = today;\n      this.showAddModal = true;\n      console.log('showAddModal设置为:', this.showAddModal);\n    },\n\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatSlotTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    isSlotInPast(startTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      return slotStart < now;\n    },\n\n    isSlotActive(startTime, endTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      const slotEnd = new Date(endTime);\n      return now >= slotStart && now <= slotEnd;\n    },\n\n    async createRound() {\n      if (!this.canCreateRound) {\n        this.$message.error('请完善轮次信息');\n        return;\n      }\n\n      // 生成整点秒杀时段数据\n      const hourlySlots = this.hourlySlotPreview;\n      const now = new Date();\n\n      // 过滤掉已完全结束的时段，保留当前正在进行的和未来的时段\n      const validSlots = hourlySlots.filter(slot => new Date(slot.end) > now);\n\n      if (validSlots.length === 0) {\n        this.$message.error('所选时间段内没有有效的秒杀时段');\n        return;\n      }\n\n      // 如果轮次数量过多，询问用户确认\n      if (validSlots.length > 50) {\n        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);\n        if (!confirmed) {\n          return;\n        }\n      }\n\n      try {\n        this.isCreating = true;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = validSlots.length;\n\n        let createdCount = 0;\n        let failedCount = 0;\n        const failedReasons = [];\n\n        // 批量处理，每次处理10个轮次\n        const batchSize = 10;\n        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {\n          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);\n          const batch = validSlots.slice(batchStart, batchEnd);\n\n          // 并行创建当前批次的轮次\n          const batchPromises = batch.map(async (slot, batchIndex) => {\n            const globalIndex = batchStart + batchIndex;\n\n            // 重试逻辑\n            const maxRetries = 3;\n            let lastError = null;\n\n            for (let retry = 0; retry < maxRetries; retry++) {\n              try {\n                const roundData = {\n                  round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,\n                  start_time: slot.start,\n                  end_time: slot.end,\n                  is_hourly_flash: true,\n                  slot_index: globalIndex + 1,\n                  total_slots: validSlots.length,\n                  goods_list: this.newRound.goods_list.map(goods => ({\n                    goods_id: goods.goods_id,\n                    goods_name: goods.goods_name,\n                    goods_image: goods.goods_image,\n                    original_price: goods.original_price,\n                    flash_price: goods.flash_price,\n                    stock: goods.stock,\n                    discount_rate: goods.discount_rate\n                  }))\n                };\n\n                const response = await this.axios.post('flashsalemulti/create', roundData);\n\n                if (response.data.errno === 0) {\n                  return { success: true, index: globalIndex + 1 };\n                } else {\n                  lastError = response.data.errmsg;\n                  // 如果是重复键错误，等待一段时间后重试\n                  if (lastError.includes('Duplicate entry') && retry < maxRetries - 1) {\n                    await new Promise(resolve => setTimeout(resolve, 500 * (retry + 1)));\n                    continue;\n                  }\n                  return { success: false, index: globalIndex + 1, error: lastError };\n                }\n              } catch (error) {\n                lastError = error.message;\n                // 如果是网络错误或服务器错误，等待后重试\n                if (retry < maxRetries - 1) {\n                  await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));\n                  continue;\n                }\n              }\n            }\n\n            return { success: false, index: globalIndex + 1, error: `创建失败 (已重试${maxRetries}次): ${lastError}` };\n          });\n\n          // 等待当前批次完成\n          const batchResults = await Promise.all(batchPromises);\n\n          // 统计结果\n          batchResults.forEach(result => {\n            this.creationProgress.current++;\n            if (result.success) {\n              createdCount++;\n            } else {\n              failedCount++;\n              if (failedReasons.length < 5) { // 只记录前5个错误\n                failedReasons.push(`第${result.index}场: ${result.error}`);\n              }\n            }\n          });\n\n          // 短暂延迟，避免服务器压力过大\n          if (batchEnd < validSlots.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n          }\n        }\n\n        // 显示结果\n        if (createdCount > 0) {\n          let message = `成功创建${createdCount}个整点秒杀轮次`;\n          if (failedCount > 0) {\n            message += `，${failedCount}个失败`;\n            if (failedReasons.length > 0) {\n              console.warn('创建失败的轮次:', failedReasons);\n              message += `\\n主要错误: ${failedReasons[0]}`;\n            }\n          }\n          this.$message.success(message);\n          this.closeModal();\n          this.loadData();\n        } else {\n          let errorMessage = '所有轮次创建失败';\n          if (failedReasons.length > 0) {\n            errorMessage += `\\n错误信息: ${failedReasons[0]}`;\n          }\n          this.$message.error(errorMessage);\n        }\n\n      } catch (error) {\n        console.error('创建整点秒杀轮次失败:', error);\n        this.$message.error('创建过程中发生错误: ' + error.message);\n      } finally {\n        this.isCreating = false;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = 0;\n      }\n    },\n\n    closeModal() {\n      this.showAddModal = false;\n      this.newRound = {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      };\n    },\n\n    viewRoundDetails(round) {\n      this.selectedRound = round;\n      this.showDetailModal = true;\n    },\n\n    async closeRound(round) {\n      if (!confirm(`确定要关闭轮次\"${round.round_name}\"吗？关闭后轮次将立即结束。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在关闭轮次...');\n        const response = await this.axios.post('flashsalemulti/close', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已关闭');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '关闭失败');\n        }\n      } catch (error) {\n        console.error('关闭轮次失败:', error);\n        this.$message.error('关闭失败');\n      }\n    },\n\n\n\n    formatTime(seconds) {\n      if (!seconds || seconds <= 0) return '00:00:00';\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor((seconds % 3600) / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return new Date(dateTime).toLocaleString('zh-CN');\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束'\n      };\n      return statusMap[status] || status;\n    },\n\n    // 获取状态样式类\n    getStatusClass(status) {\n      const classMap = {\n        'upcoming': 'badge badge-warning',\n        'active': 'badge badge-success',\n        'ended': 'badge badge-secondary'\n      };\n      return classMap[status] || 'badge badge-light';\n    },\n\n    // 获取倒计时\n    getCountdown(timeStr) {\n      if (!timeStr) return '';\n      const targetTime = new Date(timeStr);\n      const now = new Date();\n      const diff = targetTime - now;\n\n      if (diff <= 0) return '';\n\n      const hours = Math.floor(diff / (1000 * 60 * 60));\n      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n      const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n\n      if (hours > 0) {\n        return `${hours}小时${minutes}分钟`;\n      } else if (minutes > 0) {\n        return `${minutes}分钟${seconds}秒`;\n      } else {\n        return `${seconds}秒`;\n      }\n    },\n\n    // 自动刷新相关方法\n    toggleAutoRefresh() {\n      if (this.autoRefresh) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    startAutoRefresh() {\n      this.stopAutoRefresh(); // 先停止之前的定时器\n\n      this.refreshTimer = setInterval(() => {\n        this.refreshData();\n      }, this.refreshInterval);\n\n      // 启动倒计时\n      this.refreshCountdown = this.refreshInterval / 1000;\n      this.countdownTimer = setInterval(() => {\n        this.refreshCountdown--;\n        if (this.refreshCountdown <= 0) {\n          this.refreshCountdown = this.refreshInterval / 1000;\n        }\n      }, 1000);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n      this.refreshCountdown = 0;\n    },\n\n    async refreshData() {\n      this.loading = true;\n      try {\n        await this.loadData();\n      } catch (error) {\n        console.error('刷新数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 批量操作相关方法\n    toggleSelectAll() {\n      if (this.isAllSelected) {\n        this.selectedRounds = [];\n      } else {\n        this.selectedRounds = this.roundsList.data.map(round => round.id);\n      }\n    },\n\n    updateSelection() {\n      // 这个方法会在复选框状态改变时自动调用\n    },\n\n    clearSelection() {\n      this.selectedRounds = [];\n    },\n\n    async batchCloseRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要关闭的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要关闭选中的 ${this.selectedRounds.length} 个轮次吗？`,\n          '批量关闭确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          let successCount = 0;\n          let failCount = 0;\n\n          for (const roundId of this.selectedRounds) {\n            try {\n              const response = await this.$http.post('/admin/flashsalemulti/close', {\n                round_id: roundId\n              });\n\n              if (response.data.errno === 0) {\n                successCount++;\n              } else {\n                failCount++;\n              }\n            } catch (error) {\n              failCount++;\n            }\n          }\n\n          this.$message.success(`批量关闭完成：成功 ${successCount} 个，失败 ${failCount} 个`);\n          this.clearSelection();\n          this.loadData();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量关闭失败:', error);\n          this.$message.error('批量关闭失败');\n        }\n      }\n    },\n\n    async batchDeleteRounds() {\n      if (this.selectedRounds.length === 0) {\n        this.$message.warning('请先选择要删除的轮次');\n        return;\n      }\n\n      try {\n        const confirmResult = await this.$confirm(\n          `确定要删除选中的 ${this.selectedRounds.length} 个轮次吗？此操作不可恢复！\\n\\n注意：进行中或即将开始的轮次将先被关闭，然后删除。`,\n          '批量删除确认',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在批量删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/batchDelete', {\n            round_ids: this.selectedRounds\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success(response.data.errmsg || '批量删除完成');\n            this.clearSelection();\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '批量删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error);\n          this.$message.error('批量删除失败');\n        }\n      }\n    },\n\n    // 延期轮次相关方法\n    showExtendModal(round) {\n      this.extendRound = round;\n      this.extendMinutes = null;\n      this.showExtendModalFlag = true;\n    },\n\n    closeExtendModal() {\n      this.showExtendModalFlag = false;\n      this.extendRound = null;\n      this.extendMinutes = null;\n    },\n\n    async confirmExtend() {\n      if (!this.extendMinutes || this.extendMinutes <= 0) {\n        this.$message.warning('请输入有效的延期时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/extend', {\n          round_id: this.extendRound.id,\n          extend_minutes: this.extendMinutes\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success(`轮次已延期${this.extendMinutes}分钟`);\n          this.closeExtendModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '延期失败');\n        }\n      } catch (error) {\n        console.error('延期轮次失败:', error);\n        this.$message.error('延期失败');\n      }\n    },\n\n    // 重启轮次相关方法\n    showRestartModal(round) {\n      this.restartRound = round;\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.newStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.newEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showRestartModalFlag = true;\n    },\n\n    closeRestartModal() {\n      this.showRestartModalFlag = false;\n      this.restartRound = null;\n      this.newStartTime = '';\n      this.newEndTime = '';\n    },\n\n    async confirmRestart() {\n      if (!this.newStartTime || !this.newEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.newStartTime) >= new Date(this.newEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/restart', {\n          round_id: this.restartRound.id,\n          new_start_time: this.newStartTime,\n          new_end_time: this.newEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已重新启动');\n          this.closeRestartModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '重启失败');\n        }\n      } catch (error) {\n        console.error('重启轮次失败:', error);\n        this.$message.error('重启失败');\n      }\n    },\n\n    // 复制轮次相关方法\n    showCopyModal(round) {\n      this.copyRound = round;\n      this.newRoundName = round.round_name + ' (复制)';\n\n      // 设置默认时间为当前时间后1小时和2小时\n      const now = new Date();\n      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);\n      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);\n\n      this.copyStartTime = this.formatDateTimeLocal(oneHourLater);\n      this.copyEndTime = this.formatDateTimeLocal(twoHoursLater);\n      this.showCopyModalFlag = true;\n    },\n\n    closeCopyModal() {\n      this.showCopyModalFlag = false;\n      this.copyRound = null;\n      this.newRoundName = '';\n      this.copyStartTime = '';\n      this.copyEndTime = '';\n    },\n\n    async confirmCopy() {\n      if (!this.copyStartTime || !this.copyEndTime) {\n        this.$message.warning('请设置开始和结束时间');\n        return;\n      }\n\n      if (new Date(this.copyStartTime) >= new Date(this.copyEndTime)) {\n        this.$message.warning('开始时间必须早于结束时间');\n        return;\n      }\n\n      try {\n        const response = await this.$http.post('/admin/flashsalemulti/copy', {\n          round_id: this.copyRound.id,\n          new_round_name: this.newRoundName,\n          new_start_time: this.copyStartTime,\n          new_end_time: this.copyEndTime\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次复制成功');\n          this.closeCopyModal();\n          this.loadData();\n        } else {\n          this.$message.error(response.data.errmsg || '复制失败');\n        }\n      } catch (error) {\n        console.error('复制轮次失败:', error);\n        this.$message.error('复制失败');\n      }\n    },\n\n    // 删除轮次\n    async deleteRound(round) {\n      try {\n        const statusText = {\n          'upcoming': '即将开始',\n          'active': '进行中',\n          'ended': '已结束'\n        };\n\n        let confirmMessage = `确定要删除轮次\"${round.round_name}\"吗？此操作不可恢复！`;\n\n        if (round.status === 'active' || round.status === 'upcoming') {\n          confirmMessage += `\\n\\n注意：该轮次当前状态为\"${statusText[round.status]}\"，删除前将先自动关闭轮次。`;\n        }\n\n        const confirmResult = await this.$confirm(\n          confirmMessage,\n          '删除轮次确认',\n          {\n            confirmButtonText: '确定删除',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        );\n\n        if (confirmResult) {\n          this.$message.info('正在删除轮次...');\n\n          const response = await this.$http.post('flashsalemulti/delete', {\n            round_id: round.id\n          });\n\n          if (response.data.errno === 0) {\n            this.$message.success('轮次删除成功');\n            this.loadData();\n          } else {\n            this.$message.error(response.data.errmsg || '删除失败');\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除轮次失败:', error);\n          this.$message.error('删除失败');\n        }\n      }\n    },\n\n    // 编辑轮次\n    editRound(round) {\n      // 这里可以跳转到编辑页面或打开编辑模态框\n      this.$message.info('编辑功能待实现');\n    },\n\n    // 格式化日期时间为本地格式（用于datetime-local输入框）\n    formatDateTimeLocal(date) {\n      const d = new Date(date);\n      const year = d.getFullYear();\n      const month = String(d.getMonth() + 1).padStart(2, '0');\n      const day = String(d.getDate()).padStart(2, '0');\n      const hours = String(d.getHours()).padStart(2, '0');\n      const minutes = String(d.getMinutes()).padStart(2, '0');\n\n      return `${year}-${month}-${day}T${hours}:${minutes}`;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.flash-sale-multi-page {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n/* 表格控制区域 */\n.table-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.auto-refresh-control {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-size: 14px;\n}\n\n.refresh-countdown {\n  color: #666;\n  font-size: 12px;\n}\n\n/* 批量操作区域 */\n.batch-operations {\n  margin-bottom: 15px;\n}\n\n.batch-actions {\n  margin-top: 10px;\n  display: flex;\n  gap: 10px;\n}\n\n/* 状态信息显示 */\n.status-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.time-info {\n  margin-top: 5px;\n  font-size: 12px;\n  color: #666;\n}\n\n.countdown {\n  font-weight: bold;\n  color: #f56c6c;\n}\n\n/* 操作按钮组 */\n.round-actions {\n  display: flex;\n  gap: 5px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin-bottom: 2px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.round-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin: 0;\n}\n\n.btn-info {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: white;\n}\n\n.btn-info:hover {\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #212529;\n}\n\n.btn-warning:hover {\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n/* 整点秒杀设置样式 */\n.hourly-flash-settings {\n  margin-bottom: 20px;\n}\n\n.setting-header {\n  margin-bottom: 15px;\n}\n\n.setting-header h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.setting-description {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.time-range-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  color: #666;\n  font-weight: bold;\n}\n\n.slot-preview {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 5px;\n  border: 1px solid #e9ecef;\n}\n\n.slot-preview h5 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.slot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.slot-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 8px 12px;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.slot-number {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 60px;\n}\n\n.slot-time {\n  flex: 1;\n  color: #333;\n}\n\n.slot-duration {\n  color: #28a745;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.slot-summary {\n  margin-top: 10px;\n  padding-top: 10px;\n  border-top: 1px solid #dee2e6;\n  text-align: center;\n  color: #666;\n  font-weight: bold;\n}\n\n.valid-slots {\n  color: #28a745;\n  margin-left: 10px;\n}\n\n.round-name-preview {\n  margin-bottom: 20px;\n}\n\n.round-name-preview h5 {\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: bold;\n}\n\n.name-display {\n  padding: 10px 15px;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n.slot-list-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.slot-status {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n}\n\n.slot-status.past {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.slot-status.active {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.slot-status.upcoming {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.more-slots {\n  padding: 10px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.stat-card h3 {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.stat-number.active {\n  color: #28a745;\n}\n\n.stat-number.upcoming {\n  color: #ffc107;\n}\n\n/* 当前轮次 */\n.current-rounds {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.round-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.round-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n}\n\n.round-item.active {\n  border-color: #28a745;\n  background-color: #f8fff9;\n}\n\n.round-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.round-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.goods-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 80px;\n}\n\n.goods-item img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n  margin-bottom: 4px;\n}\n\n.goods-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n.goods-item .price {\n  color: #e74c3c;\n  font-weight: bold;\n}\n\n.more-goods {\n  color: #007bff;\n  font-size: 12px;\n}\n\n/* 表格 */\n.rounds-table {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.status-upcoming {\n  color: #ffc107;\n  font-weight: bold;\n}\n\n.status-active {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.status-ended {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n/* 模态框 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content.large {\n  max-width: 1000px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n}\n\n.modal-body {\n  padding: 20px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 表单 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: flex;\n  gap: 15px;\n}\n\n.form-row .form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control.small {\n  width: 80px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 商品选择网格 */\n.goods-selection-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n}\n\n/* 商品卡片 */\n.goods-card {\n  background: white;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.goods-card:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.goods-card.selected {\n  border-color: #28a745;\n  background-color: #f8fff9;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);\n}\n\n.goods-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background-color: #f5f5f5;\n  border-color: #dee2e6;\n}\n\n.goods-card.disabled:hover {\n  transform: none;\n  box-shadow: none;\n  border-color: #dee2e6;\n}\n\n/* 商品卡片头部 */\n.goods-card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.goods-image-container {\n  position: relative;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.goods-card-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.selected-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background-color: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.2);\n}\n\n.checkmark {\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.disabled-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 商品信息 */\n.goods-card-info {\n  flex: 1;\n}\n\n.goods-name {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.original-price {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.warning-text {\n  display: flex;\n  align-items: center;\n  color: #dc3545;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.warning-icon {\n  margin-right: 4px;\n  font-size: 14px;\n}\n\n/* 商品设置面板 */\n.goods-settings-panel {\n  border-top: 1px solid #e9ecef;\n  padding-top: 15px;\n  margin-top: 15px;\n}\n\n.settings-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.settings-title::before {\n  content: \"⚙\";\n  margin-right: 6px;\n  color: #007bff;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n}\n\n.setting-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-item.full-width {\n  grid-column: 1 / -1;\n}\n\n.setting-item label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n/* 价格输入组 */\n.price-input-group {\n  display: flex;\n  align-items: center;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n  background: white;\n}\n\n.currency {\n  background-color: #f8f9fa;\n  padding: 6px 8px;\n  border-right: 1px solid #ddd;\n  font-size: 14px;\n  color: #666;\n}\n\n.price-input {\n  border: none;\n  padding: 6px 8px;\n  font-size: 14px;\n  flex: 1;\n  outline: none;\n}\n\n.price-input:focus {\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.discount-display {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #e74c3c;\n  font-weight: bold;\n  text-align: center;\n  background-color: #fff5f5;\n  padding: 2px 6px;\n  border-radius: 12px;\n  border: 1px solid #fecaca;\n}\n\n/* 库存和限购输入 */\n.stock-input,\n.limit-input {\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 6px 8px;\n  font-size: 14px;\n  outline: none;\n  background: white;\n}\n\n.stock-input:focus,\n.limit-input:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 折扣设置样式 */\n.discount-setting {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.discount-input-group {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.discount-input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.discount-unit {\n  margin-left: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.price-preview {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.price-range-hint {\n  font-size: 12px;\n  color: #28a745;\n  font-style: italic;\n}\n\n/* 选择提示 */\n.selection-hint {\n  margin-top: 15px;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #007bff;\n}\n\n.hint-text {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-right: 8px;\n  color: #007bff;\n  font-size: 16px;\n}\n\n.selected-count {\n  margin: 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.selected-count strong {\n  color: #28a745;\n  font-size: 16px;\n}\n\n/* 已选商品汇总 */\n.selected-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.selected-summary h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n}\n\n.summary-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.remove-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n/* 轮次详情 */\n.round-details {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.detail-section h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.detail-section p {\n  margin: 5px 0;\n  color: #666;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  color: #6c757d;\n  font-size: 16px;\n}\n\n.loading-state .loading-icon {\n  margin-right: 10px;\n  font-size: 20px;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n.empty-state .empty-text {\n  font-size: 18px;\n  font-weight: 500;\n  color: #495057;\n  margin: 0 0 8px 0;\n}\n\n.empty-state .empty-hint {\n  font-size: 14px;\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.empty-state .btn-sm {\n  padding: 6px 16px;\n  font-size: 14px;\n}\n\n.goods-cell {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-cell img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.goods-cell span {\n  font-size: 14px;\n}\n\n/* 状态徽章样式 */\n.badge {\n  display: inline-block;\n  padding: 4px 8px;\n  font-size: 12px;\n  font-weight: bold;\n  border-radius: 4px;\n  text-align: center;\n  white-space: nowrap;\n}\n\n.badge-warning {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.badge-success {\n  background-color: #28a745;\n  color: white;\n}\n\n.badge-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.badge-light {\n  background-color: #f8f9fa;\n  color: #6c757d;\n  border: 1px solid #dee2e6;\n}\n\n/* 模态框样式增强 */\n.modal-content.small {\n  max-width: 500px;\n}\n\n.modal-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 18px;\n}\n\n/* 表单控件增强 */\n.form-control[type=\"number\"] {\n  text-align: center;\n}\n\n.form-control[type=\"datetime-local\"] {\n  font-family: monospace;\n}\n\n/* 按钮样式增强 */\n.btn:hover {\n  opacity: 0.9;\n  transform: translateY(-1px);\n  transition: all 0.2s ease;\n}\n\n.btn-success {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: white;\n}\n\n.btn-success:hover {\n  background-color: #218838;\n  border-color: #1e7e34;\n}\n</style>\n</style>\n"]}]}