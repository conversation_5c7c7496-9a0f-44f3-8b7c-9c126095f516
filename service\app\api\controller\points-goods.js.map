{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\points-goods.js"], "names": ["Base", "require", "module", "exports", "homeAction", "console", "log", "configModel", "model", "configs", "select", "configMap", "for<PERSON>ach", "config", "config_key", "config_value", "points_mall_enabled", "fail", "pointsGoodsModel", "hotGoods", "alias", "join", "where", "field", "order", "limit", "item", "goods_specification_ids", "specInfo", "getSpecificationInfo", "specification_info", "stock_limit", "available_stock", "Math", "min", "stock", "sold_count", "categories", "status", "is_delete", "success", "enabled", "banner", "JSON", "parse", "points_mall_banner", "notice", "points_mall_notice", "max_exchange_per_day", "parseInt", "min_points_required", "hot_goods", "error", "listAction", "page", "get", "category_id", "keyword", "sort", "orderBy", "list", "total", "count", "now", "Date", "start_time", "is_available", "unavailable_reason", "end_time", "length", "detailAction", "id", "detail", "find", "think", "isEmpty", "galleryModel", "gallery", "goods_id", "goods_name", "specificationIds", "specIds", "split", "map", "specModel", "specs", "spec", "value"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,YAAN,GAAmB;AAAA;;AAAA;AACjBC,cAAQC,GAAR,CAAY,oBAAZ;;AAEA,UAAI;AACF;AACA,cAAMC,cAAc,MAAKC,KAAL,CAAW,eAAX,CAApB;AACA,cAAMC,UAAU,MAAMF,YAAYG,MAAZ,EAAtB;AACA,cAAMC,YAAY,EAAlB;AACAF,gBAAQG,OAAR,CAAgB,kBAAU;AACxBD,oBAAUE,OAAOC,UAAjB,IAA+BD,OAAOE,YAAtC;AACD,SAFD;;AAIA;AACA,YAAIJ,UAAUK,mBAAV,KAAkC,GAAtC,EAA2C;AACzC,iBAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;;AAED;AACA,cAAMC,mBAAmB,MAAKV,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMW,WAAW,MAAMD,iBAAiBE,KAAjB,CAAuB,IAAvB,EACpBC,IADoB,CACf,uCADe,EAEpBA,IAFoB,CAEf,2CAFe,EAGpBC,KAHoB,CAGd;AACL,uBAAa,CADR;AAEL,0BAAgB,CAFX;AAGL,yBAAe,CAHV;AAIL,0BAAgB,CAJX;AAKL,yBAAe,CALV;AAML,0BAAgB;AANX,SAHc,EAWpBC,KAXoB,CAWd,gKAXc,EAYpBC,KAZoB,CAYd,0CAZc,EAapBC,KAboB,CAad,CAbc,EAcpBf,MAdoB,EAAvB;;AAgBA;AACA,aAAK,IAAIgB,IAAT,IAAiBP,QAAjB,EAA2B;AACzB,cAAIO,KAAKC,uBAAT,EAAkC;AAChC,kBAAMC,WAAW,MAAM,MAAKC,oBAAL,CAA0BH,KAAKC,uBAA/B,CAAvB;AACAD,iBAAKI,kBAAL,GAA0BF,QAA1B;AACD;;AAED;AACA,cAAIF,KAAKK,WAAL,GAAmB,CAAvB,EAA0B;AACxBL,iBAAKM,eAAL,GAAuBC,KAAKC,GAAL,CAASR,KAAKS,KAAd,EAAqBT,KAAKK,WAAL,GAAmBL,KAAKU,UAA7C,CAAvB;AACD,WAFD,MAEO;AACLV,iBAAKM,eAAL,GAAuBN,KAAKS,KAA5B;AACD;AACF;;AAED;AACA,cAAME,aAAa,MAAM,MAAK7B,KAAL,CAAW,mBAAX,EACtBc,KADsB,CAChB;AACLgB,kBAAQ,CADH;AAELC,qBAAW;AAFN,SADgB,EAKtBf,KALsB,CAKhB,WALgB,EAMtBd,MANsB,EAAzB;;AAQA,eAAO,MAAK8B,OAAL,CAAa;AAClB3B,kBAAQ;AACN4B,qBAAS9B,UAAUK,mBAAV,KAAkC,GADrC;AAEN0B,oBAAQC,KAAKC,KAAL,CAAWjC,UAAUkC,kBAAV,IAAgC,IAA3C,CAFF;AAGNC,oBAAQnC,UAAUoC,kBAAV,IAAgC,qBAHlC;AAINC,kCAAsBC,SAAStC,UAAUqC,oBAAV,IAAkC,IAA3C,CAJhB;AAKNE,iCAAqBD,SAAStC,UAAUuC,mBAAV,IAAiC,KAA1C;AALf,WADU;AAQlBC,qBAAWhC,QARO;AASlBkB,sBAAYA;AATM,SAAb,CAAP;AAYD,OApED,CAoEE,OAAOe,KAAP,EAAc;AACd/C,gBAAQ+C,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,eAAO,MAAKnC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AA1EgB;AA2ElB;;AAED;;;;AAIMoC,YAAN,GAAmB;AAAA;;AAAA;AACjBhD,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAMgD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAM9B,QAAQ,OAAK8B,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAMC,cAAc,OAAKD,GAAL,CAAS,aAAT,KAA2B,EAA/C;AACA,cAAME,UAAU,OAAKF,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,cAAMG,OAAO,OAAKH,GAAL,CAAS,MAAT,KAAoB,SAAjC,CALE,CAK0C;;AAE5C;AACA,cAAMjC,QAAQ;AACZ,uBAAa,CADD;AAEZ,0BAAgB,CAFJ;AAGZ,yBAAe,CAHH;AAIZ,0BAAgB,CAJJ;AAKZ,yBAAe,CALH;AAMZ,0BAAgB;AANJ,SAAd;;AASA,YAAIkC,gBAAgB,EAApB,EAAwB;AACtBlC,gBAAM,eAAN,IAAyB2B,SAASO,WAAT,CAAzB;AACD;;AAED,YAAIC,YAAY,EAAhB,EAAoB;AAClBnC,gBAAM,QAAN,IAAkB,CAAC,MAAD,EAAU,IAAGmC,OAAQ,GAArB,CAAlB;AACD;;AAED;AACA,YAAIE,UAAU,0BAAd;AACA,gBAAQD,IAAR;AACE,eAAK,WAAL;AACEC,sBAAU,oBAAV;AACA;AACF,eAAK,YAAL;AACEA,sBAAU,qBAAV;AACA;AACF,eAAK,YAAL;AACEA,sBAAU,qBAAV;AACA;AACF,eAAK,aAAL;AACEA,sBAAU,sBAAV;AACA;AACF,eAAK,OAAL;AACEA,sBAAU,oBAAV;AACA;AACF;AACEA,sBAAU,0BAAV;AAjBJ;;AAoBA;AACA,cAAMzC,mBAAmB,OAAKV,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMoD,OAAO,MAAM1C,iBAAiBE,KAAjB,CAAuB,IAAvB,EAChBC,IADgB,CACX,uCADW,EAEhBA,IAFgB,CAEX,2CAFW,EAGhBC,KAHgB,CAGVA,KAHU,EAIhBC,KAJgB,CAIV,gKAJU,EAKhBC,KALgB,CAKVmC,OALU,EAMhBL,IANgB,CAMXA,IANW,EAML7B,KANK,EAOhBf,MAPgB,EAAnB;;AASA;AACA,cAAMmD,QAAQ,MAAM3C,iBAAiBE,KAAjB,CAAuB,IAAvB,EACjBC,IADiB,CACZ,uCADY,EAEjBA,IAFiB,CAEZ,2CAFY,EAGjBC,KAHiB,CAGXA,KAHW,EAIjBwC,KAJiB,EAApB;;AAMA;AACA,aAAK,IAAIpC,IAAT,IAAiBkC,IAAjB,EAAuB;AACrB,cAAIlC,KAAKC,uBAAT,EAAkC;AAChC,kBAAMC,WAAW,MAAM,OAAKC,oBAAL,CAA0BH,KAAKC,uBAA/B,CAAvB;AACAD,iBAAKI,kBAAL,GAA0BF,QAA1B;AACD;;AAED;AACA,cAAIF,KAAKK,WAAL,GAAmB,CAAvB,EAA0B;AACxBL,iBAAKM,eAAL,GAAuBC,KAAKC,GAAL,CAASR,KAAKS,KAAd,EAAqBT,KAAKK,WAAL,GAAmBL,KAAKU,UAA7C,CAAvB;AACD,WAFD,MAEO;AACLV,iBAAKM,eAAL,GAAuBN,KAAKS,KAA5B;AACD;;AAED;AACA,gBAAM4B,MAAM,IAAIC,IAAJ,EAAZ;AACA,cAAItC,KAAKuC,UAAL,IAAmB,IAAID,IAAJ,CAAStC,KAAKuC,UAAd,IAA4BF,GAAnD,EAAwD;AACtDrC,iBAAKwC,YAAL,GAAoB,KAApB;AACAxC,iBAAKyC,kBAAL,GAA0B,OAA1B;AACD,WAHD,MAGO,IAAIzC,KAAK0C,QAAL,IAAiB,IAAIJ,IAAJ,CAAStC,KAAK0C,QAAd,IAA0BL,GAA/C,EAAoD;AACzDrC,iBAAKwC,YAAL,GAAoB,KAApB;AACAxC,iBAAKyC,kBAAL,GAA0B,OAA1B;AACD,WAHM,MAGA,IAAIzC,KAAKM,eAAL,IAAwB,CAA5B,EAA+B;AACpCN,iBAAKwC,YAAL,GAAoB,KAApB;AACAxC,iBAAKyC,kBAAL,GAA0B,MAA1B;AACD,WAHM,MAGA;AACLzC,iBAAKwC,YAAL,GAAoB,IAApB;AACD;AACF;;AAED7D,gBAAQC,GAAR,CAAa,OAAMsD,KAAKS,MAAO,aAAYR,KAAM,IAAjD;;AAEA,eAAO,OAAKrB,OAAL,CAAa;AAClBoB,gBAAMA,IADY;AAElBC,iBAAOA,KAFW;AAGlBP,gBAAML,SAASK,IAAT,CAHY;AAIlB7B,iBAAOwB,SAASxB,KAAT;AAJW,SAAb,CAAP;AAOD,OAxGD,CAwGE,OAAO2B,KAAP,EAAc;AACd/C,gBAAQ+C,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,OAAKnC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AA9GgB;AA+GlB;;AAED;;;;AAIMqD,cAAN,GAAqB;AAAA;;AAAA;AACnBjE,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAMiE,KAAK,OAAKhB,GAAL,CAAS,IAAT,CAAX;;AAEA,YAAI,CAACgB,EAAL,EAAS;AACP,iBAAO,OAAKtD,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;;AAED;AACA,cAAMC,mBAAmB,OAAKV,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMgE,SAAS,MAAMtD,iBAAiBE,KAAjB,CAAuB,IAAvB,EAClBC,IADkB,CACb,uCADa,EAElBA,IAFkB,CAEb,2CAFa,EAGlBC,KAHkB,CAGZ;AACL,mBAASiD,EADJ;AAEL,uBAAa,CAFR;AAGL,0BAAgB,CAHX;AAIL,yBAAe,CAJV;AAKL,0BAAgB,CALX;AAML,yBAAe,CANV;AAOL,0BAAgB;AAPX,SAHY,EAYlBhD,KAZkB,CAYZ,8KAZY,EAalBkD,IAbkB,EAArB;;AAeA,YAAIC,MAAMC,OAAN,CAAcH,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKvD,IAAL,CAAU,GAAV,EAAe,WAAf,CAAP;AACD;;AAED;AACA,YAAIuD,OAAO7C,uBAAX,EAAoC;AAClC,gBAAMC,WAAW,MAAM,OAAKC,oBAAL,CAA0B2C,OAAO7C,uBAAjC,CAAvB;AACA6C,iBAAO1C,kBAAP,GAA4BF,QAA5B;AACD;;AAED;AACA,YAAI4C,OAAOzC,WAAP,GAAqB,CAAzB,EAA4B;AAC1ByC,iBAAOxC,eAAP,GAAyBC,KAAKC,GAAL,CAASsC,OAAOrC,KAAhB,EAAuBqC,OAAOzC,WAAP,GAAqByC,OAAOpC,UAAnD,CAAzB;AACD,SAFD,MAEO;AACLoC,iBAAOxC,eAAP,GAAyBwC,OAAOrC,KAAhC;AACD;;AAED;AACA,cAAM4B,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAIQ,OAAOP,UAAP,IAAqB,IAAID,IAAJ,CAASQ,OAAOP,UAAhB,IAA8BF,GAAvD,EAA4D;AAC1DS,iBAAON,YAAP,GAAsB,KAAtB;AACAM,iBAAOL,kBAAP,GAA4B,OAA5B;AACD,SAHD,MAGO,IAAIK,OAAOJ,QAAP,IAAmB,IAAIJ,IAAJ,CAASQ,OAAOJ,QAAhB,IAA4BL,GAAnD,EAAwD;AAC7DS,iBAAON,YAAP,GAAsB,KAAtB;AACAM,iBAAOL,kBAAP,GAA4B,OAA5B;AACD,SAHM,MAGA,IAAIK,OAAOxC,eAAP,IAA0B,CAA9B,EAAiC;AACtCwC,iBAAON,YAAP,GAAsB,KAAtB;AACAM,iBAAOL,kBAAP,GAA4B,MAA5B;AACD,SAHM,MAGA;AACLK,iBAAON,YAAP,GAAsB,IAAtB;AACD;;AAED;AACA,cAAMU,eAAe,OAAKpE,KAAL,CAAW,eAAX,CAArB;AACA,cAAMqE,UAAU,MAAMD,aAAatD,KAAb,CAAmB;AACvCwD,oBAAUN,OAAOM,QADsB;AAEvCvC,qBAAW;AAF4B,SAAnB,EAGnBf,KAHmB,CAGb,gBAHa,EAGKd,MAHL,EAAtB;;AAKA8D,eAAOK,OAAP,GAAiBA,OAAjB;;AAEAxE,gBAAQC,GAAR,CAAY,SAAZ,EAAuBkE,OAAOO,UAA9B;;AAEA,eAAO,OAAKvC,OAAL,CAAagC,MAAb,CAAP;AAED,OArED,CAqEE,OAAOpB,KAAP,EAAc;AACd/C,gBAAQ+C,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,OAAKnC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA3EkB;AA4EpB;;AAED;;;AAGMY,sBAAN,CAA2BmD,gBAA3B,EAA6C;AAAA;;AAAA;AAC3C,UAAI,CAACA,gBAAL,EAAuB,OAAO,EAAP;;AAEvB,UAAI;AACF,cAAMC,UAAUD,iBAAiBE,KAAjB,CAAuB,GAAvB,EAA4BC,GAA5B,CAAgC;AAAA,iBAAMlC,SAASsB,EAAT,CAAN;AAAA,SAAhC,CAAhB;AACA,cAAMa,YAAY,OAAK5E,KAAL,CAAW,qBAAX,CAAlB;AACA,cAAM6E,QAAQ,MAAMD,UAAU9D,KAAV,CAAgB;AAClCiD,cAAI,CAAC,IAAD,EAAOU,OAAP,CAD8B;AAElC1C,qBAAW;AAFuB,SAAhB,EAGjB7B,MAHiB,EAApB;;AAKA,eAAO2E,MAAMF,GAAN,CAAU;AAAA,iBAAQG,KAAKC,KAAb;AAAA,SAAV,EAA8BlE,IAA9B,CAAmC,KAAnC,CAAP;AACD,OATD,CASE,OAAO+B,KAAP,EAAc;AACd/C,gBAAQ+C,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,EAAP;AACD;AAf0C;AAgB5C;AA7SiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\points-goods.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n  \n  /**\n   * 获取积分商城首页数据\n   * GET /api/points-goods/home\n   */\n  async homeAction() {\n    console.log('=== 获取积分商城首页数据 ===');\n    \n    try {\n      // 获取积分商城配置\n      const configModel = this.model('points_config');\n      const configs = await configModel.select();\n      const configMap = {};\n      configs.forEach(config => {\n        configMap[config.config_key] = config.config_value;\n      });\n      \n      // 检查积分商城是否启用\n      if (configMap.points_mall_enabled !== '1') {\n        return this.fail(400, '积分商城暂未开放');\n      }\n      \n      // 获取热门商品（前6个）- 联表查询真实商品信息\n      const pointsGoodsModel = this.model('points_goods');\n      const hotGoods = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where({\n          'pg.status': 1,\n          'pg.is_delete': 0,\n          'g.is_delete': 0,\n          'g.is_on_sale': 1,\n          'p.is_delete': 0,\n          'p.is_on_sale': 1\n        })\n        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')\n        .order('pg.is_hot DESC, pg.sort DESC, pg.id DESC')\n        .limit(6)\n        .select();\n      \n      // 处理规格信息\n      for (let item of hotGoods) {\n        if (item.goods_specification_ids) {\n          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);\n          item.specification_info = specInfo;\n        }\n        \n        // 检查库存限制\n        if (item.stock_limit > 0) {\n          item.available_stock = Math.min(item.stock, item.stock_limit - item.sold_count);\n        } else {\n          item.available_stock = item.stock;\n        }\n      }\n      \n      // 获取分类列表\n      const categories = await this.model('points_categories')\n        .where({\n          status: 1,\n          is_delete: 0\n        })\n        .order('sort DESC')\n        .select();\n      \n      return this.success({\n        config: {\n          enabled: configMap.points_mall_enabled === '1',\n          banner: JSON.parse(configMap.points_mall_banner || '[]'),\n          notice: configMap.points_mall_notice || '欢迎来到积分商城，用积分兑换心仪商品！',\n          max_exchange_per_day: parseInt(configMap.max_exchange_per_day || '10'),\n          min_points_required: parseInt(configMap.min_points_required || '100')\n        },\n        hot_goods: hotGoods,\n        categories: categories\n      });\n      \n    } catch (error) {\n      console.error('获取积分商城首页数据失败:', error);\n      return this.fail(500, '获取数据失败');\n    }\n  }\n  \n  /**\n   * 获取积分商品列表\n   * GET /api/points-goods/list\n   */\n  async listAction() {\n    console.log('=== 获取积分商品列表 ===');\n    \n    try {\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const category_id = this.get('category_id') || '';\n      const keyword = this.get('keyword') || '';\n      const sort = this.get('sort') || 'default'; // default, price_asc, price_desc, points_asc, points_desc\n      \n      // 构建查询条件\n      const where = {\n        'pg.status': 1,\n        'pg.is_delete': 0,\n        'g.is_delete': 0,\n        'g.is_on_sale': 1,\n        'p.is_delete': 0,\n        'p.is_on_sale': 1\n      };\n      \n      if (category_id !== '') {\n        where['g.category_id'] = parseInt(category_id);\n      }\n      \n      if (keyword !== '') {\n        where['g.name'] = ['like', `%${keyword}%`];\n      }\n      \n      // 构建排序条件\n      let orderBy = 'pg.sort DESC, pg.id DESC';\n      switch (sort) {\n        case 'price_asc':\n          orderBy = 'p.retail_price ASC';\n          break;\n        case 'price_desc':\n          orderBy = 'p.retail_price DESC';\n          break;\n        case 'points_asc':\n          orderBy = 'pg.points_price ASC';\n          break;\n        case 'points_desc':\n          orderBy = 'pg.points_price DESC';\n          break;\n        case 'sales':\n          orderBy = 'pg.sold_count DESC';\n          break;\n        default:\n          orderBy = 'pg.sort DESC, pg.id DESC';\n      }\n      \n      // 联表查询积分商品列表\n      const pointsGoodsModel = this.model('points_goods');\n      const list = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where(where)\n        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')\n        .order(orderBy)\n        .page(page, limit)\n        .select();\n      \n      // 获取总数\n      const total = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where(where)\n        .count();\n      \n      // 处理规格信息和库存\n      for (let item of list) {\n        if (item.goods_specification_ids) {\n          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);\n          item.specification_info = specInfo;\n        }\n        \n        // 检查库存限制\n        if (item.stock_limit > 0) {\n          item.available_stock = Math.min(item.stock, item.stock_limit - item.sold_count);\n        } else {\n          item.available_stock = item.stock;\n        }\n        \n        // 检查时间限制\n        const now = new Date();\n        if (item.start_time && new Date(item.start_time) > now) {\n          item.is_available = false;\n          item.unavailable_reason = '活动未开始';\n        } else if (item.end_time && new Date(item.end_time) < now) {\n          item.is_available = false;\n          item.unavailable_reason = '活动已结束';\n        } else if (item.available_stock <= 0) {\n          item.is_available = false;\n          item.unavailable_reason = '库存不足';\n        } else {\n          item.is_available = true;\n        }\n      }\n      \n      console.log(`查询到 ${list.length} 条积分商品，总计 ${total} 条`);\n      \n      return this.success({\n        list: list,\n        total: total,\n        page: parseInt(page),\n        limit: parseInt(limit)\n      });\n      \n    } catch (error) {\n      console.error('获取积分商品列表失败:', error);\n      return this.fail(500, '获取数据失败');\n    }\n  }\n\n  /**\n   * 获取积分商品详情\n   * GET /api/points-goods/detail/:id\n   */\n  async detailAction() {\n    console.log('=== 获取积分商品详情 ===');\n    \n    try {\n      const id = this.get('id');\n      \n      if (!id) {\n        return this.fail(400, '商品ID不能为空');\n      }\n      \n      // 联表查询积分商品详情\n      const pointsGoodsModel = this.model('points_goods');\n      const detail = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where({\n          'pg.id': id,\n          'pg.status': 1,\n          'pg.is_delete': 0,\n          'g.is_delete': 0,\n          'g.is_on_sale': 1,\n          'p.is_delete': 0,\n          'p.is_on_sale': 1\n        })\n        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.goods_desc, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')\n        .find();\n      \n      if (think.isEmpty(detail)) {\n        return this.fail(404, '商品不存在或已下架');\n      }\n      \n      // 获取规格信息\n      if (detail.goods_specification_ids) {\n        const specInfo = await this.getSpecificationInfo(detail.goods_specification_ids);\n        detail.specification_info = specInfo;\n      }\n      \n      // 检查库存限制\n      if (detail.stock_limit > 0) {\n        detail.available_stock = Math.min(detail.stock, detail.stock_limit - detail.sold_count);\n      } else {\n        detail.available_stock = detail.stock;\n      }\n      \n      // 检查时间限制\n      const now = new Date();\n      if (detail.start_time && new Date(detail.start_time) > now) {\n        detail.is_available = false;\n        detail.unavailable_reason = '活动未开始';\n      } else if (detail.end_time && new Date(detail.end_time) < now) {\n        detail.is_available = false;\n        detail.unavailable_reason = '活动已结束';\n      } else if (detail.available_stock <= 0) {\n        detail.is_available = false;\n        detail.unavailable_reason = '库存不足';\n      } else {\n        detail.is_available = true;\n      }\n      \n      // 获取商品图片\n      const galleryModel = this.model('goods_gallery');\n      const gallery = await galleryModel.where({\n        goods_id: detail.goods_id,\n        is_delete: 0\n      }).order('sort_order ASC').select();\n      \n      detail.gallery = gallery;\n      \n      console.log('积分商品详情:', detail.goods_name);\n      \n      return this.success(detail);\n      \n    } catch (error) {\n      console.error('获取积分商品详情失败:', error);\n      return this.fail(500, '获取商品详情失败');\n    }\n  }\n\n  /**\n   * 获取规格信息\n   */\n  async getSpecificationInfo(specificationIds) {\n    if (!specificationIds) return '';\n    \n    try {\n      const specIds = specificationIds.split(',').map(id => parseInt(id));\n      const specModel = this.model('goods_specification');\n      const specs = await specModel.where({\n        id: ['IN', specIds],\n        is_delete: 0\n      }).select();\n      \n      return specs.map(spec => spec.value).join(' / ');\n    } catch (error) {\n      console.error('获取规格信息失败:', error);\n      return '';\n    }\n  }\n};\n"]}