{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\seckill.js"], "names": ["Base", "require", "moment", "module", "exports", "indexAction", "console", "log", "roundModel", "model", "now", "Date", "toISOString", "slice", "replace", "activeRound", "where", "status", "find", "upcomingRound", "order", "seckillTimeIndex", "currentRoundInfo", "startTime", "start_time", "endTime", "end_time", "isNaN", "getTime", "id", "time", "toTimeString", "substring", "continued", "Math", "floor", "state", "stop", "round_id", "round_name", "seckillTime", "success", "lovely", "error", "fail", "listAction", "roundId", "get", "post", "timeId", "page", "parseInt", "limit", "url", "ctx", "method", "roundGoodsModel", "goodsModel", "rounds", "round", "think", "isEmpty", "activeRounds", "select", "length", "upcomingRounds", "seckillList", "roundGoods", "roundGood", "goods", "goods_id", "is_delete", "is_on_sale", "totalStock", "stock", "sold_count", "percent", "push", "round_goods_id", "title", "goods_name", "name", "image", "goods_image", "list_pic_url", "price", "parseFloat", "flash_price", "ot_price", "original_price", "retail_price", "quota", "quota_show", "sales", "limit_quantity", "startIndex", "endIndex", "paginatedList", "stack", "message", "detailAction", "result", "images", "goods_brief", "goods_desc", "seckill_info", "round_number", "time_left", "calculateTimeLeft", "is_active", "can_buy", "goodsDetailAction", "goodsId", "timeLeft", "max", "total_stock", "soldCount", "homeAction", "list", "initAction", "timeSlotModel", "categoryModel", "categories", "parent_id", "sort_order", "is_show", "category", "existingCategory", "add", "category_id", "goods_number", "sell_volume", "min_retail_price", "cost_price", "min_cost_price", "goods_unit", "add_time", "goodsItem", "existingGoods", "timeSlots", "timeSlot", "existingTimeSlot", "existingRound", "data", "end", "diff", "hours", "minutes", "seconds", "duration", "asHours", "updateRoundAction", "startTimeStr", "endTimeStr", "update"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;;AAElC;;;;AAIMK,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACFC,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMC,aAAa,MAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMC,MAAM,IAAIC,IAAJ,GAAWC,WAAX,GAAyBC,KAAzB,CAA+B,CAA/B,EAAkC,EAAlC,EAAsCC,OAAtC,CAA8C,GAA9C,EAAmD,GAAnD,CAAZ;;AAEA;AACA,cAAMC,cAAc,MAAMP,WAAWQ,KAAX,CAAiB;AACzCC,kBAAQ;AADiC,SAAjB,EAEvBC,IAFuB,EAA1B;;AAIA;AACA,cAAMC,gBAAgB,MAAMX,WAAWQ,KAAX,CAAiB;AAC3CC,kBAAQ;AADmC,SAAjB,EAEzBG,KAFyB,CAEnB,gBAFmB,EAEDF,IAFC,EAA5B;;AAIA,YAAIG,mBAAmB,CAAC,CAAxB;AACA,YAAIC,mBAAmB,IAAvB;;AAEA,YAAIP,WAAJ,EAAiB;AACf;AACA,gBAAMQ,YAAY,IAAIZ,IAAJ,CAASI,YAAYS,UAArB,CAAlB;AACA,gBAAMC,UAAU,IAAId,IAAJ,CAASI,YAAYW,QAArB,CAAhB;;AAEA;AACA,cAAI,CAACC,MAAMJ,UAAUK,OAAV,EAAN,CAAD,IAA+B,CAACD,MAAMF,QAAQG,OAAR,EAAN,CAApC,EAA8D;AAC5DN,+BAAmB;AACjBO,kBAAId,YAAYc,EADC;AAEjBC,oBAAMP,UAAUQ,YAAV,GAAyBC,SAAzB,CAAmC,CAAnC,EAAsC,CAAtC,CAFW;AAGjBC,yBAAWC,KAAKC,KAAL,CAAW,CAACV,UAAUF,SAAX,KAAyB,OAAO,EAAP,GAAY,EAArC,CAAX,CAHM;AAIjBN,sBAAQ,CAJS;AAKjBmB,qBAAO,KALU;AAMjBC,oBAAMH,KAAKC,KAAL,CAAWV,QAAQG,OAAR,KAAoB,IAA/B,CANW;AAOjBU,wBAAUvB,YAAYc,EAPL;AAQjBU,0BAAYxB,YAAYwB;AARP,aAAnB;AAUAlB,+BAAmB,CAAnB;AACD;AACF,SAnBD,MAmBO,IAAIF,aAAJ,EAAmB;AACxB;AACA,gBAAMI,YAAY,IAAIZ,IAAJ,CAASQ,cAAcK,UAAvB,CAAlB;AACA,gBAAMC,UAAU,IAAId,IAAJ,CAASQ,cAAcO,QAAvB,CAAhB;;AAEA;AACA,cAAI,CAACC,MAAMJ,UAAUK,OAAV,EAAN,CAAD,IAA+B,CAACD,MAAMF,QAAQG,OAAR,EAAN,CAApC,EAA8D;AAC5DN,+BAAmB;AACjBO,kBAAIV,cAAcU,EADD;AAEjBC,oBAAMP,UAAUQ,YAAV,GAAyBC,SAAzB,CAAmC,CAAnC,EAAsC,CAAtC,CAFW;AAGjBC,yBAAWC,KAAKC,KAAL,CAAW,CAACV,UAAUF,SAAX,KAAyB,OAAO,EAAP,GAAY,EAArC,CAAX,CAHM;AAIjBN,sBAAQ,CAJS;AAKjBmB,qBAAO,MALU;AAMjBC,oBAAMH,KAAKC,KAAL,CAAWV,QAAQG,OAAR,KAAoB,IAA/B,CANW;AAOjBU,wBAAUnB,cAAcU,EAPP;AAQjBU,0BAAYpB,cAAcoB;AART,aAAnB;AAUAlB,+BAAmB,CAAnB;AACD;AACF;;AAED,cAAMmB,cAAclB,mBAAmB,CAACA,gBAAD,CAAnB,GAAwC,EAA5D;;AAEAhB,gBAAQC,GAAR,CAAY,SAAZ,EAAuB,EAAEiC,WAAF,EAAenB,gBAAf,EAAiCN,WAAjC,EAA8CI,aAA9C,EAAvB;;AAEA,eAAO,MAAKsB,OAAL,CAAa;AAClBD,uBAAaA,WADK;AAElBnB,4BAAkBA,gBAFA;AAGlBqB,kBAAQ;AAHU,SAAb,CAAP;AAMD,OArED,CAqEE,OAAOC,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,eAAO,MAAKC,IAAL,CAAU,SAAV,CAAP;AACD;AAzEiB;AA0EnB;;AAED;;;;AAIMC,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACF;AACA,cAAMC,UAAU,OAAKC,GAAL,CAAS,UAAT,KAAwB,OAAKC,IAAL,CAAU,UAAV,CAAxC;AACA,cAAMC,SAAS,OAAKF,GAAL,CAAS,QAAT,KAAsB,OAAKC,IAAL,CAAU,QAAV,CAArC;AACA,cAAME,OAAOC,SAAS,OAAKJ,GAAL,CAAS,MAAT,KAAoB,OAAKC,IAAL,CAAU,MAAV,CAA7B,KAAmD,CAAhE;AACA,cAAMI,QAAQD,SAAS,OAAKJ,GAAL,CAAS,OAAT,KAAqB,OAAKC,IAAL,CAAU,OAAV,CAA9B,KAAqD,EAAnE;;AAEA1C,gBAAQC,GAAR,CAAY,kBAAZ,EAAgC;AAC9B8C,eAAK,OAAKC,GAAL,CAASD,GADgB;AAE9BE,kBAAQ,OAAKA,MAFiB;AAG9BT,iBAH8B;AAI9BG,gBAJ8B;AAK9BC,cAL8B;AAM9BE;AAN8B,SAAhC;;AASA,cAAM5C,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM+C,kBAAkB,OAAK/C,KAAL,CAAW,wBAAX,CAAxB;AACA,cAAMgD,aAAa,OAAKhD,KAAL,CAAW,OAAX,CAAnB;;AAEA,YAAIiD,SAAS,EAAb;;AAEA,YAAIZ,OAAJ,EAAa;AACX;AACA,gBAAMa,QAAQ,MAAMnD,WAAWQ,KAAX,CAAiB,EAAEa,IAAIiB,OAAN,EAAjB,EAAkC5B,IAAlC,EAApB;AACA,cAAI,CAAC0C,MAAMC,OAAN,CAAcF,KAAd,CAAL,EAA2B;AACzBD,qBAAS,CAACC,KAAD,CAAT;AACD;AACF,SAND,MAMO;AACL;AACA,gBAAMG,eAAe,MAAMtD,WAAWQ,KAAX,CAAiB;AAC1CC,oBAAQ;AADkC,WAAjB,EAExBG,KAFwB,CAElB,gBAFkB,EAEA2C,MAFA,EAA3B;;AAIA,cAAID,gBAAgBA,aAAaE,MAAb,GAAsB,CAA1C,EAA6C;AAC3CN,qBAASI,YAAT;AACD,WAFD,MAEO;AACL;AACA,kBAAMG,iBAAiB,MAAMzD,WAAWQ,KAAX,CAAiB;AAC5CC,sBAAQ;AADoC,aAAjB,EAE1BG,KAF0B,CAEpB,gBAFoB,EAEFgC,KAFE,CAEI,CAFJ,EAEOW,MAFP,EAA7B;;AAIA,gBAAIE,kBAAkBA,eAAeD,MAAf,GAAwB,CAA9C,EAAiD;AAC/CN,uBAASO,cAAT;AACD;AACF;AACF;;AAED,YAAI,CAACP,MAAD,IAAWA,OAAOM,MAAP,KAAkB,CAAjC,EAAoC;AAClC1D,kBAAQC,GAAR,CAAY,gBAAZ;AACA,iBAAO,OAAKkC,OAAL,CAAa,EAAb,CAAP;AACD;;AAED,cAAMyB,cAAc,EAApB;;AAEA,aAAK,MAAMP,KAAX,IAAoBD,MAApB,EAA4B;AAC1BpD,kBAAQC,GAAR,CAAY,OAAZ,EAAqBoD,KAArB;;AAEA;AACA,gBAAMQ,aAAa,MAAMX,gBAAgBxC,KAAhB,CAAsB;AAC7CsB,sBAAUqB,MAAM9B;AAD6B,WAAtB,EAEtBkC,MAFsB,EAAzB;;AAIAzD,kBAAQC,GAAR,CAAa,MAAKoD,MAAM9B,EAAG,OAA3B,EAAmCsC,UAAnC;;AAEA,cAAI,CAACA,UAAD,IAAeA,WAAWH,MAAX,KAAsB,CAAzC,EAA4C;AAC1C1D,oBAAQC,GAAR,CAAY,YAAZ,EAA0BoD,MAAM9B,EAAhC;AACA;AACD;;AAED;AACA,eAAK,MAAMuC,SAAX,IAAwBD,UAAxB,EAAoC;AAClC;AACA,kBAAME,QAAQ,MAAMZ,WAAWzC,KAAX,CAAiB;AACnCa,kBAAIuC,UAAUE,QADqB;AAEnCC,yBAAW,CAFwB;AAGnCC,0BAAY;AAHuB,aAAjB,EAIjBtD,IAJiB,EAApB;;AAMA,gBAAI,CAAC0C,MAAMC,OAAN,CAAcQ,KAAd,CAAL,EAA2B;AACzB;AACA,oBAAMI,aAAaL,UAAUM,KAAV,GAAkBN,UAAUO,UAA/C;AACA,oBAAMC,UAAUH,aAAa,CAAb,GAAiBvC,KAAKC,KAAL,CAAYiC,UAAUO,UAAV,GAAuBF,UAAxB,GAAsC,GAAjD,CAAjB,GAAyE,CAAzF;;AAEAP,0BAAYW,IAAZ,CAAiB;AACfhD,oBAAI8B,MAAM9B,EADK;AAEfiD,gCAAgBV,UAAUvC,EAFX;AAGfyC,0BAAUF,UAAUE,QAHL;AAIfS,uBAAOX,UAAUY,UAAV,IAAwBX,MAAMY,IAJtB;AAKfC,uBAAOd,UAAUe,WAAV,IAAyBd,MAAMe,YALvB;AAMfC,uBAAOC,WAAWlB,UAAUmB,WAArB,CANQ;AAOfC,0BAAUF,WAAWlB,UAAUqB,cAAV,IAA4BpB,MAAMqB,YAA7C,CAPK;AAQfC,uBAAOvB,UAAUM,KARF,EAQS;AACxBkB,4BAAYnB,UATG,EASS;AACxBG,yBAASA,OAVM,EAUG;AAClBF,uBAAON,UAAUM,KAXF;AAYfmB,uBAAOzB,UAAUO,UAZF;AAafnD,4BAAYmC,MAAMnC,UAbH;AAcfE,0BAAUiC,MAAMjC,QAdD;AAefT,wBAAQ0C,MAAM1C,MAfC;AAgBf6E,gCAAgB1B,UAAU0B;AAhBX,eAAjB;AAkBD;AACF;AACF;;AAED;AACA,cAAMC,aAAa,CAAC7C,OAAO,CAAR,IAAaE,KAAhC;AACA,cAAM4C,WAAWD,aAAa3C,KAA9B;AACA,cAAM6C,gBAAgB/B,YAAYrD,KAAZ,CAAkBkF,UAAlB,EAA8BC,QAA9B,CAAtB;;AAEA1F,gBAAQC,GAAR,CAAa,MAAK0F,cAAcjC,MAAO,QAAvC;AACA,eAAO,OAAKvB,OAAL,CAAawD,aAAb,CAAP;AAED,OAlHD,CAkHE,OAAOtD,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACArC,gBAAQqC,KAAR,CAAc,OAAd,EAAuBA,MAAMuD,KAA7B;AACA,eAAO,OAAKtD,IAAL,CAAU,eAAeD,MAAMwD,OAA/B,CAAP;AACD;AAvHgB;AAwHlB;;AAED;;;;AAIMC,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF;AACA,cAAMtD,UAAU,OAAKC,GAAL,CAAS,IAAT,CAAhB;AACA,cAAME,SAAS,OAAKF,GAAL,CAAS,SAAT,CAAf;;AAEAzC,gBAAQC,GAAR,CAAY,kBAAZ,EAAgC;AAC9B8C,eAAK,OAAKC,GAAL,CAASD,GADgB;AAE9BP,iBAF8B;AAG9BG;AAH8B,SAAhC;;AAMA,YAAI,CAACH,OAAL,EAAc;AACZ,iBAAO,OAAKF,IAAL,CAAU,WAAV,CAAP;AACD;;AAED,cAAMpC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMgD,aAAa,OAAKhD,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,cAAMkD,QAAQ,MAAMnD,WAAWQ,KAAX,CAAiB,EAAEa,IAAIiB,OAAN,EAAjB,EAAkC5B,IAAlC,EAApB;AACA,YAAI0C,MAAMC,OAAN,CAAcF,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKf,IAAL,CAAU,SAAV,CAAP;AACD;;AAED;AACA,cAAMyB,QAAQ,MAAMZ,WAAWzC,KAAX,CAAiB;AACnCa,cAAI8B,MAAMW,QADyB;AAEnCC,qBAAW;AAFwB,SAAjB,EAGjBrD,IAHiB,EAApB;;AAKA,YAAI0C,MAAMC,OAAN,CAAcQ,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKzB,IAAL,CAAU,SAAV,CAAP;AACD;;AAED;AACA,cAAM6B,aAAad,MAAMe,KAAN,GAAcf,MAAMgB,UAAvC;AACA,cAAMC,UAAUH,aAAa,CAAb,GAAiBvC,KAAKC,KAAL,CAAYwB,MAAMgB,UAAN,GAAmBF,UAApB,GAAkC,GAA7C,CAAjB,GAAqE,CAArF;;AAEA,cAAM4B,SAAS;AACbxE,cAAI8B,MAAM9B,EADG;AAEbyC,oBAAUX,MAAMW,QAFH;AAGbS,iBAAOpB,MAAMqB,UAAN,IAAoBX,MAAMY,IAHpB;AAIbC,iBAAOvB,MAAMwB,WAAN,IAAqBd,MAAMe,YAJrB;AAKbkB,kBAAQjC,MAAMe,YAAN,GAAqB,CAACf,MAAMe,YAAP,CAArB,GAA4C,EALvC;AAMbC,iBAAOC,WAAW3B,MAAM4B,WAAjB,CANM;AAObC,oBAAUF,WAAW3B,MAAM8B,cAAjB,CAPG;AAQbE,iBAAOhC,MAAMe,KARA;AASbkB,sBAAYnB,UATC;AAUbG,mBAASA,OAVI;AAWbF,iBAAOf,MAAMe,KAXA;AAYbmB,iBAAOlC,MAAMgB,UAZA;AAabnD,sBAAYmC,MAAMnC,UAbL;AAcbE,oBAAUiC,MAAMjC,QAdH;AAebT,kBAAQ0C,MAAM1C,MAfD;AAgBb6E,0BAAgBnC,MAAMmC,cAhBT;AAiBbS,uBAAalC,MAAMkC,WAAN,IAAqB,EAjBrB;AAkBbC,sBAAYnC,MAAMmC,UAAN,IAAoB,EAlBnB;AAmBb;AACAC,wBAAc;AACZnE,sBAAUqB,MAAM9B,EADJ;AAEZ6E,0BAAc/C,MAAM+C,YAFR;AAGZC,uBAAW,OAAKC,iBAAL,CAAuBjD,MAAMjC,QAA7B,CAHC;AAIZmF,uBAAWlD,MAAM1C,MAAN,KAAiB,QAJhB;AAKZ6F,qBAASnD,MAAM1C,MAAN,KAAiB,QAAjB,IAA6B0C,MAAMe,KAAN,GAAc;AALxC;AApBD,SAAf;;AA6BApE,gBAAQC,GAAR,CAAY,SAAZ,EAAuB8F,MAAvB;AACA,eAAO,OAAK5D,OAAL,CAAa4D,MAAb,CAAP;AAED,OAtED,CAsEE,OAAO1D,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AA1EkB;AA2EpB;;AAED;;;;AAIMmE,mBAAN,GAA0B;AAAA;;AAAA;AACxB,UAAI;AACF,cAAMC,UAAU,OAAKjE,GAAL,CAAS,UAAT,CAAhB;AACA,cAAMD,UAAU,OAAKC,GAAL,CAAS,UAAT,CAAhB;;AAEAzC,gBAAQC,GAAR,CAAY,qBAAZ,EAAmC,EAAEyG,OAAF,EAAWlE,OAAX,EAAnC;;AAEA,YAAI,CAACkE,OAAD,IAAY,CAAClE,OAAjB,EAA0B;AACxB,iBAAO,OAAKF,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,cAAMpC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM+C,kBAAkB,OAAK/C,KAAL,CAAW,wBAAX,CAAxB;AACA,cAAMgD,aAAa,OAAKhD,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,cAAMkD,QAAQ,MAAMnD,WAAWQ,KAAX,CAAiB,EAAEa,IAAIiB,OAAN,EAAjB,EAAkC5B,IAAlC,EAApB;AACA,YAAI0C,MAAMC,OAAN,CAAcF,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKf,IAAL,CAAU,SAAV,CAAP;AACD;;AAED;AACA,cAAMuB,aAAa,MAAMX,gBAAgBxC,KAAhB,CAAsB;AAC7CsB,oBAAUQ,OADmC;AAE7CwB,oBAAU0C;AAFmC,SAAtB,EAGtB9F,IAHsB,EAAzB;;AAKA,YAAI0C,MAAMC,OAAN,CAAcM,UAAd,CAAJ,EAA+B;AAC7B,iBAAO,OAAKvB,IAAL,CAAU,aAAV,CAAP;AACD;;AAED;AACA,cAAMyB,QAAQ,MAAMZ,WAAWzC,KAAX,CAAiB;AACnCa,cAAImF,OAD+B;AAEnCzC,qBAAW;AAFwB,SAAjB,EAGjBrD,IAHiB,EAApB;;AAKA,YAAI0C,MAAMC,OAAN,CAAcQ,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKzB,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,cAAMlC,MAAM,IAAIC,IAAJ,EAAZ;AACA,cAAMc,UAAU,IAAId,IAAJ,CAASgD,MAAMjC,QAAf,CAAhB;AACA,cAAMuF,WAAW/E,KAAKgF,GAAL,CAAS,CAAT,EAAYhF,KAAKC,KAAL,CAAW,CAACV,UAAUf,GAAX,IAAkB,IAA7B,CAAZ,CAAjB;;AAEA;AACA,cAAM+D,aAAaN,WAAWgD,WAAX,IAA0BhD,WAAWO,KAAxD;AACA,cAAM0C,YAAYjD,WAAWQ,UAAX,IAAyB,CAA3C;AACA,cAAMC,UAAUH,aAAa,CAAb,GAAiBvC,KAAKC,KAAL,CAAYiF,YAAY3C,UAAb,GAA2B,GAAtC,CAAjB,GAA8D,CAA9E;;AAEA,cAAM4B,SAAS;AACb;AACA/B,oBAAUD,MAAMxC,EAFH;AAGboD,gBAAMZ,MAAMY,IAHC;AAIbG,wBAAcf,MAAMe,YAJP;AAKbmB,uBAAalC,MAAMkC,WALN;AAMbC,sBAAYnC,MAAMmC,UANL;AAObd,wBAAcrB,MAAMqB,YAPP;;AASb;AACAe,wBAAc;AACZnE,sBAAUqB,MAAM9B,EADJ;AAEZU,wBAAYoB,MAAMpB,UAFN;AAGZgD,yBAAapB,WAAWoB,WAHZ;AAIZE,4BAAgBtB,WAAWsB,cAJf;AAKZf,mBAAOP,WAAWO,KALN;AAMZC,wBAAYyC,SANA;AAOZD,yBAAa1C,UAPD;AAQZG,qBAASA,OARG;AASZkB,4BAAgB3B,WAAW2B,cATf;AAUZtE,wBAAYmC,MAAMnC,UAVN;AAWZE,sBAAUiC,MAAMjC,QAXJ;AAYZT,oBAAQ0C,MAAM1C,MAZF;AAaZ0F,uBAAWM,QAbC;AAcZJ,uBAAWlD,MAAM1C,MAAN,KAAiB,QAAjB,IAA6BgG,WAAW,CAdvC;AAeZH,qBAASnD,MAAM1C,MAAN,KAAiB,QAAjB,IAA6BkD,WAAWO,KAAX,GAAmB,CAAhD,IAAqDuC,WAAW;AAf7D;AAVD,SAAf;;AA6BA3G,gBAAQC,GAAR,CAAY,YAAZ,EAA0B8F,MAA1B;AACA,eAAO,OAAK5D,OAAL,CAAa4D,MAAb,CAAP;AAED,OAlFD,CAkFE,OAAO1D,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,gBAAd,EAAgCA,KAAhC;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAtFuB;AAuFzB;;AAED;;;;AAIMyE,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACF/G,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMgD,aAAa,OAAKhD,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,cAAMiD,SAAS,MAAMlD,WAAWQ,KAAX,CAAiB;AACpCC,kBAAQ;AAD4B,SAAjB,EAElBG,KAFkB,CAEZ,gBAFY,EAEMgC,KAFN,CAEY,CAFZ,EAEeW,MAFf,EAArB;;AAIA,YAAI,CAACL,MAAD,IAAWA,OAAOM,MAAP,KAAkB,CAAjC,EAAoC;AAClC,iBAAO,OAAKvB,OAAL,CAAa;AAClBD,yBAAa,EADK;AAElBnB,8BAAkB,CAAC,CAFD;AAGlBiG,kBAAM;AAHY,WAAb,CAAP;AAKD;;AAED,cAAMpD,cAAc,EAApB;;AAEA,aAAK,MAAMP,KAAX,IAAoBD,MAApB,EAA4B;AAC1B;AACA,gBAAMW,QAAQ,MAAMZ,WAAWzC,KAAX,CAAiB;AACnCa,gBAAI8B,MAAMW,QADyB;AAEnCC,uBAAW,CAFwB;AAGnCC,wBAAY;AAHuB,WAAjB,EAIjBtD,IAJiB,EAApB;;AAMA,cAAI,CAAC0C,MAAMC,OAAN,CAAcQ,KAAd,CAAL,EAA2B;AACzB;AACA,kBAAMI,aAAad,MAAMe,KAAN,GAAcf,MAAMgB,UAAvC;AACA,kBAAMC,UAAUH,aAAa,CAAb,GAAiBvC,KAAKC,KAAL,CAAYwB,MAAMgB,UAAN,GAAmBF,UAApB,GAAkC,GAA7C,CAAjB,GAAqE,CAArF;;AAEAP,wBAAYW,IAAZ,CAAiB;AACfhD,kBAAI8B,MAAM9B,EADK;AAEfyC,wBAAUX,MAAMW,QAFD;AAGfS,qBAAOpB,MAAMqB,UAAN,IAAoBX,MAAMY,IAHlB;AAIfC,qBAAOvB,MAAMwB,WAAN,IAAqBd,MAAMe,YAJnB;AAKfC,qBAAOC,WAAW3B,MAAM4B,WAAjB,CALQ;AAMfC,wBAAUF,WAAW3B,MAAM8B,cAAjB,CANK;AAOfE,qBAAOhC,MAAMe,KAPE;AAQfkB,0BAAYnB,UARG;AASfG,uBAASA,OATM;AAUfF,qBAAOf,MAAMe,KAVE;AAWfmB,qBAAOlC,MAAMgB,UAXE;AAYfnD,0BAAYmC,MAAMnC,UAZH;AAafE,wBAAUiC,MAAMjC,QAbD;AAcfT,sBAAQ0C,MAAM1C,MAdC;AAef6E,8BAAgBnC,MAAMmC;AAfP,aAAjB;AAiBD;AACF;;AAED;AACA,cAAMO,SAAS;AACbvE,gBAAM,OADO;AAEbO,gBAAMH,KAAKC,KAAL,CAAWxB,KAAKD,GAAL,KAAa,IAAxB,IAAgC,IAFzB,EAE+B;AAC5C4G,gBAAMpD;AAHO,SAAf;;AAMA5D,gBAAQC,GAAR,CAAa,MAAK2D,YAAYF,MAAO,UAArC;AACA,eAAO,OAAKvB,OAAL,CAAa4D,MAAb,CAAP;AAED,OAhED,CAgEE,OAAO1D,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,OAAKC,IAAL,CAAU,QAAV,CAAP;AACD;AApEgB;AAqElB;;AAED;;;;AAIM2E,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACFjH,gBAAQC,GAAR,CAAY,mBAAZ;;AAEA,cAAMiH,gBAAgB,OAAK/G,KAAL,CAAW,uBAAX,CAAtB;AACA,cAAMD,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMgD,aAAa,OAAKhD,KAAL,CAAW,OAAX,CAAnB;AACA,cAAMgH,gBAAgB,OAAKhH,KAAL,CAAW,UAAX,CAAtB;;AAEA;AACA,cAAMiH,aAAa,CACjB,EAAE7F,IAAI,CAAN,EAASoD,MAAM,MAAf,EAAuB0C,WAAW,CAAlC,EAAqCC,YAAY,CAAjD,EAAoDC,SAAS,CAA7D,EADiB,EAEjB,EAAEhG,IAAI,CAAN,EAASoD,MAAM,MAAf,EAAuB0C,WAAW,CAAlC,EAAqCC,YAAY,CAAjD,EAAoDC,SAAS,CAA7D,EAFiB,CAAnB;;AAKA,aAAK,MAAMC,QAAX,IAAuBJ,UAAvB,EAAmC;AACjC,gBAAMK,mBAAmB,MAAMN,cAAczG,KAAd,CAAoB,EAAEa,IAAIiG,SAASjG,EAAf,EAApB,EAAyCX,IAAzC,EAA/B;AACA,cAAI0C,MAAMC,OAAN,CAAckE,gBAAd,CAAJ,EAAqC;AACnC,kBAAMN,cAAcO,GAAd,CAAkBF,QAAlB,CAAN;AACD;AACF;;AAED;AACA,cAAMzD,QAAQ,CACZ;AACExC,cAAI,CADN;AAEEoG,uBAAa,CAFf;AAGEzD,sBAAY,CAHd;AAIES,gBAAM,yBAJR;AAKEiD,wBAAc,EALhB;AAMEC,uBAAa,GANf;AAOEzC,wBAAc,OAPhB;AAQE0C,4BAAkB,OARpB;AASEC,sBAAY,OATd;AAUEC,0BAAgB,OAVlB;AAWE/B,uBAAa,sBAXf;AAYEgC,sBAAY,GAZd;AAaEnD,wBAAc,+BAbhB;AAcEoD,oBAAUtG,KAAKC,KAAL,CAAWxB,KAAKD,GAAL,KAAa,IAAxB,CAdZ;AAeE6D,qBAAW;AAfb,SADY,EAkBZ;AACE1C,cAAI,CADN;AAEEoG,uBAAa,CAFf;AAGEzD,sBAAY,CAHd;AAIES,gBAAM,kBAJR;AAKEiD,wBAAc,EALhB;AAMEC,uBAAa,EANf;AAOEzC,wBAAc,OAPhB;AAQE0C,4BAAkB,OARpB;AASEC,sBAAY,OATd;AAUEC,0BAAgB,OAVlB;AAWE/B,uBAAa,eAXf;AAYEgC,sBAAY,GAZd;AAaEnD,wBAAc,6BAbhB;AAcEoD,oBAAUtG,KAAKC,KAAL,CAAWxB,KAAKD,GAAL,KAAa,IAAxB,CAdZ;AAeE6D,qBAAW;AAfb,SAlBY,EAmCZ;AACE1C,cAAI,CADN;AAEEoG,uBAAa,CAFf;AAGEzD,sBAAY,CAHd;AAIES,gBAAM,oBAJR;AAKEiD,wBAAc,EALhB;AAMEC,uBAAa,EANf;AAOEzC,wBAAc,QAPhB;AAQE0C,4BAAkB,QARpB;AASEC,sBAAY,QATd;AAUEC,0BAAgB,QAVlB;AAWE/B,uBAAa,iBAXf;AAYEgC,sBAAY,GAZd;AAaEnD,wBAAc,8BAbhB;AAcEoD,oBAAUtG,KAAKC,KAAL,CAAWxB,KAAKD,GAAL,KAAa,IAAxB,CAdZ;AAeE6D,qBAAW;AAfb,SAnCY,CAAd;;AAsDA,aAAK,MAAMkE,SAAX,IAAwBpE,KAAxB,EAA+B;AAC7B,gBAAMqE,gBAAgB,MAAMjF,WAAWzC,KAAX,CAAiB,EAAEa,IAAI4G,UAAU5G,EAAhB,EAAjB,EAAuCX,IAAvC,EAA5B;AACA,cAAI0C,MAAMC,OAAN,CAAc6E,aAAd,CAAJ,EAAkC;AAChC,kBAAMjF,WAAWuE,GAAX,CAAeS,SAAf,CAAN;AACD;AACF;;AAED;AACA,cAAME,YAAY,CAChB,EAAE9G,IAAI,CAAN,EAASoD,MAAM,KAAf,EAAsBzD,YAAY,UAAlC,EAA8CE,UAAU,UAAxD,EAAoEkG,YAAY,CAAhF,EAAmFf,WAAW,CAA9F,EADgB,EAEhB,EAAEhF,IAAI,CAAN,EAASoD,MAAM,KAAf,EAAsBzD,YAAY,UAAlC,EAA8CE,UAAU,UAAxD,EAAoEkG,YAAY,CAAhF,EAAmFf,WAAW,CAA9F,EAFgB,EAGhB,EAAEhF,IAAI,CAAN,EAASoD,MAAM,KAAf,EAAsBzD,YAAY,UAAlC,EAA8CE,UAAU,UAAxD,EAAoEkG,YAAY,CAAhF,EAAmFf,WAAW,CAA9F,EAHgB,CAAlB;;AAMA,aAAK,MAAM+B,QAAX,IAAuBD,SAAvB,EAAkC;AAChC,gBAAME,mBAAmB,MAAMrB,cAAcxG,KAAd,CAAoB,EAAEa,IAAI+G,SAAS/G,EAAf,EAApB,EAAyCX,IAAzC,EAA/B;AACA,cAAI0C,MAAMC,OAAN,CAAcgF,gBAAd,CAAJ,EAAqC;AACnC,kBAAMrB,cAAcQ,GAAd,CAAkBY,QAAlB,CAAN;AACD;AACF;;AAED;AACA,cAAMlI,MAAM,IAAIC,IAAJ,EAAZ;AACA,cAAM+C,SAAS,CACb;AACE7B,cAAI,CADN;AAEE6E,wBAAc,CAFhB;AAGEpC,oBAAU,CAHZ;AAIEU,sBAAY,yBAJd;AAKEG,uBAAa,+BALf;AAMEM,0BAAgB,OANlB;AAOEF,uBAAa,OAPf;AAQEb,iBAAO,EART;AASEC,sBAAY,CATd;AAUEmB,0BAAgB,CAVlB;AAWEtE,sBAAYd,GAXd;AAYEgB,oBAAU,IAAIf,IAAJ,CAASD,IAAIkB,OAAJ,KAAgB,IAAI,EAAJ,GAAS,EAAT,GAAc,IAAvC,CAZZ,EAY0D;AACxDX,kBAAQ;AAbV,SADa,EAgBb;AACEY,cAAI,CADN;AAEE6E,wBAAc,CAFhB;AAGEpC,oBAAU,CAHZ;AAIEU,sBAAY,kBAJd;AAKEG,uBAAa,6BALf;AAMEM,0BAAgB,OANlB;AAOEF,uBAAa,OAPf;AAQEb,iBAAO,EART;AASEC,sBAAY,CATd;AAUEmB,0BAAgB,CAVlB;AAWEtE,sBAAYd,GAXd;AAYEgB,oBAAU,IAAIf,IAAJ,CAASD,IAAIkB,OAAJ,KAAgB,IAAI,EAAJ,GAAS,EAAT,GAAc,IAAvC,CAZZ,EAY0D;AACxDX,kBAAQ;AAbV,SAhBa,EA+Bb;AACEY,cAAI,CADN;AAEE6E,wBAAc,CAFhB;AAGEpC,oBAAU,CAHZ;AAIEU,sBAAY,oBAJd;AAKEG,uBAAa,8BALf;AAMEM,0BAAgB,QANlB;AAOEF,uBAAa,QAPf;AAQEb,iBAAO,CART;AASEC,sBAAY,CATd;AAUEmB,0BAAgB,CAVlB;AAWEtE,sBAAYd,GAXd;AAYEgB,oBAAU,IAAIf,IAAJ,CAASD,IAAIkB,OAAJ,KAAgB,IAAI,EAAJ,GAAS,EAAT,GAAc,IAAvC,CAZZ,EAY0D;AACxDX,kBAAQ;AAbV,SA/Ba,CAAf;;AAgDA,aAAK,MAAM0C,KAAX,IAAoBD,MAApB,EAA4B;AAC1B,gBAAMoF,gBAAgB,MAAMtI,WAAWQ,KAAX,CAAiB,EAAEa,IAAI8B,MAAM9B,EAAZ,EAAjB,EAAmCX,IAAnC,EAA5B;AACA,cAAI0C,MAAMC,OAAN,CAAciF,aAAd,CAAJ,EAAkC;AAChC,kBAAMtI,WAAWwH,GAAX,CAAerE,KAAf,CAAN;AACD;AACF;;AAED,eAAO,OAAKlB,OAAL,CAAa;AAClB0D,mBAAS,WADS;AAElB4C,gBAAM;AACJrB,wBAAYA,WAAW1D,MADnB;AAEJK,mBAAOA,MAAML,MAFT;AAGJ2E,uBAAWA,UAAU3E,MAHjB;AAIJN,oBAAQA,OAAOM;AAJX;AAFY,SAAb,CAAP;AAUD,OApKD,CAoKE,OAAOrB,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,eAAO,OAAKC,IAAL,CAAU,gBAAgBD,MAAMwD,OAAhC,CAAP;AACD;AAxKgB;AAyKlB;;AAED;;;AAGAS,oBAAkBnF,OAAlB,EAA2B;AACzB,UAAMf,MAAMR,QAAZ;AACA,UAAM8I,MAAM9I,OAAOuB,OAAP,CAAZ;AACA,UAAMwH,OAAOD,IAAIC,IAAJ,CAASvI,GAAT,CAAb;;AAEA,QAAIuI,QAAQ,CAAZ,EAAe;AACb,aAAO,EAAEC,OAAO,CAAT,EAAYC,SAAS,CAArB,EAAwBC,SAAS,CAAjC,EAAP;AACD;;AAED,UAAMC,WAAWnJ,OAAOmJ,QAAP,CAAgBJ,IAAhB,CAAjB;AACA,WAAO;AACLC,aAAOhH,KAAKC,KAAL,CAAWkH,SAASC,OAAT,EAAX,CADF;AAELH,eAASE,SAASF,OAAT,EAFJ;AAGLC,eAASC,SAASD,OAAT;AAHJ,KAAP;AAKD;;AAED;;;;AAIMG,mBAAN,GAA0B;AAAA;;AAAA;AACxB,UAAI;AACF,cAAMzG,UAAU,OAAKC,GAAL,CAAS,UAAT,KAAwB,EAAxC;AACA,cAAMrC,MAAM,IAAIC,IAAJ,EAAZ;AACA,cAAMY,YAAY,IAAIZ,IAAJ,CAASD,IAAIkB,OAAJ,KAAgB,KAAK,EAAL,GAAU,IAAnC,CAAlB,CAHE,CAG0D;AAC5D,cAAMH,UAAU,IAAId,IAAJ,CAASD,IAAIkB,OAAJ,KAAgB,MAAM,EAAN,GAAW,IAApC,CAAhB,CAJE,CAI2D;;AAE7D,cAAM4H,eAAejI,UAAUX,WAAV,GAAwBC,KAAxB,CAA8B,CAA9B,EAAiC,EAAjC,EAAqCC,OAArC,CAA6C,GAA7C,EAAkD,GAAlD,CAArB;AACA,cAAM2I,aAAahI,QAAQb,WAAR,GAAsBC,KAAtB,CAA4B,CAA5B,EAA+B,EAA/B,EAAmCC,OAAnC,CAA2C,GAA3C,EAAgD,GAAhD,CAAnB;;AAEAR,gBAAQC,GAAR,CAAY,SAAZ,EAAuB,EAAEuC,OAAF,EAAW0G,YAAX,EAAyBC,UAAzB,EAAvB;;AAEA,cAAM,OAAKhJ,KAAL,CAAW,mBAAX,EAAgCO,KAAhC,CAAsC,EAAEa,IAAIiB,OAAN,EAAtC,EAAuD4G,MAAvD,CAA8D;AAClElI,sBAAYgI,YADsD;AAElE9H,oBAAU+H,UAFwD;AAGlExI,kBAAQ;AAH0D,SAA9D,CAAN;;AAMA,eAAO,OAAKwB,OAAL,CAAa;AAClB0D,mBAAS,UADS;AAElB7D,oBAAUQ,OAFQ;AAGlBtB,sBAAYgI,YAHM;AAIlB9H,oBAAU+H,UAJQ;AAKlBxI,kBAAQ;AALU,SAAb,CAAP;AAOD,OAxBD,CAwBE,OAAO0B,KAAP,EAAc;AACdrC,gBAAQqC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,WAAWD,MAAMwD,OAAhC,CAAP;AACD;AA5BuB;AA6BzB;AA7qBiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\seckill.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 获取当前秒杀状态\n   * GET /api/seckill/index\n   */\n  async indexAction() {\n    try {\n      console.log('=== 获取当前秒杀状态 ===');\n\n      const roundModel = this.model('flash_sale_rounds');\n      const now = new Date().toISOString().slice(0, 19).replace('T', ' ');\n\n      // 查找当前正在进行的轮次\n      const activeRound = await roundModel.where({\n        status: 'active'\n      }).find();\n\n      // 查找即将开始的轮次\n      const upcomingRound = await roundModel.where({\n        status: 'upcoming'\n      }).order('start_time ASC').find();\n\n      let seckillTimeIndex = -1;\n      let currentRoundInfo = null;\n\n      if (activeRound) {\n        // 有正在进行的轮次\n        const startTime = new Date(activeRound.start_time);\n        const endTime = new Date(activeRound.end_time);\n\n        // 确保时间有效\n        if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {\n          currentRoundInfo = {\n            id: activeRound.id,\n            time: startTime.toTimeString().substring(0, 5),\n            continued: Math.floor((endTime - startTime) / (1000 * 60 * 60)),\n            status: 1,\n            state: '抢购中',\n            stop: Math.floor(endTime.getTime() / 1000),\n            round_id: activeRound.id,\n            round_name: activeRound.round_name\n          };\n          seckillTimeIndex = 0;\n        }\n      } else if (upcomingRound) {\n        // 有即将开始的轮次\n        const startTime = new Date(upcomingRound.start_time);\n        const endTime = new Date(upcomingRound.end_time);\n\n        // 确保时间有效\n        if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {\n          currentRoundInfo = {\n            id: upcomingRound.id,\n            time: startTime.toTimeString().substring(0, 5),\n            continued: Math.floor((endTime - startTime) / (1000 * 60 * 60)),\n            status: 2,\n            state: '即将开始',\n            stop: Math.floor(endTime.getTime() / 1000),\n            round_id: upcomingRound.id,\n            round_name: upcomingRound.round_name\n          };\n          seckillTimeIndex = 0;\n        }\n      }\n\n      const seckillTime = currentRoundInfo ? [currentRoundInfo] : [];\n\n      console.log('当前秒杀状态:', { seckillTime, seckillTimeIndex, activeRound, upcomingRound });\n\n      return this.success({\n        seckillTime: seckillTime,\n        seckillTimeIndex: seckillTimeIndex,\n        lovely: ''\n      });\n\n    } catch (error) {\n      console.error('获取秒杀时间段失败:', error);\n      return this.fail('获取时间段失败');\n    }\n  }\n\n  /**\n   * 获取秒杀商品列表\n   * GET /api/seckill/list\n   */\n  async listAction() {\n    try {\n      // 获取参数：支持round_id或timeId（兼容旧版本）\n      const roundId = this.get('round_id') || this.post('round_id');\n      const timeId = this.get('timeId') || this.post('timeId');\n      const page = parseInt(this.get('page') || this.post('page')) || 1;\n      const limit = parseInt(this.get('limit') || this.post('limit')) || 10;\n\n      console.log('=== 获取秒杀商品列表 ===', {\n        url: this.ctx.url,\n        method: this.method,\n        roundId,\n        timeId,\n        page,\n        limit\n      });\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n      const goodsModel = this.model('goods');\n\n      let rounds = [];\n\n      if (roundId) {\n        // 如果提供了round_id，直接查询指定轮次\n        const round = await roundModel.where({ id: roundId }).find();\n        if (!think.isEmpty(round)) {\n          rounds = [round];\n        }\n      } else {\n        // 否则查询当前active状态的轮次\n        const activeRounds = await roundModel.where({\n          status: 'active'\n        }).order('start_time ASC').select();\n\n        if (activeRounds && activeRounds.length > 0) {\n          rounds = activeRounds;\n        } else {\n          // 如果没有active轮次，查找即将开始的轮次\n          const upcomingRounds = await roundModel.where({\n            status: 'upcoming'\n          }).order('start_time ASC').limit(1).select();\n\n          if (upcomingRounds && upcomingRounds.length > 0) {\n            rounds = upcomingRounds;\n          }\n        }\n      }\n\n      if (!rounds || rounds.length === 0) {\n        console.log('没有找到任何轮次，返回空数组');\n        return this.success([]);\n      }\n\n      const seckillList = [];\n      \n      for (const round of rounds) {\n        console.log('处理轮次:', round);\n\n        // 获取该轮次的所有商品\n        const roundGoods = await roundGoodsModel.where({\n          round_id: round.id\n        }).select();\n\n        console.log(`轮次 ${round.id} 的商品:`, roundGoods);\n\n        if (!roundGoods || roundGoods.length === 0) {\n          console.log('轮次没有商品，跳过:', round.id);\n          continue;\n        }\n\n        // 处理该轮次的每个商品\n        for (const roundGood of roundGoods) {\n          // 获取商品详细信息\n          const goods = await goodsModel.where({\n            id: roundGood.goods_id,\n            is_delete: 0,\n            is_on_sale: 1\n          }).find();\n\n          if (!think.isEmpty(goods)) {\n            // 计算进度百分比\n            const totalStock = roundGood.stock + roundGood.sold_count;\n            const percent = totalStock > 0 ? Math.floor((roundGood.sold_count / totalStock) * 100) : 0;\n\n            seckillList.push({\n              id: round.id,\n              round_goods_id: roundGood.id,\n              goods_id: roundGood.goods_id,\n              title: roundGood.goods_name || goods.name,\n              image: roundGood.goods_image || goods.list_pic_url,\n              price: parseFloat(roundGood.flash_price),\n              ot_price: parseFloat(roundGood.original_price || goods.retail_price),\n              quota: roundGood.stock, // 剩余库存\n              quota_show: totalStock, // 总库存\n              percent: percent, // 进度百分比\n              stock: roundGood.stock,\n              sales: roundGood.sold_count,\n              start_time: round.start_time,\n              end_time: round.end_time,\n              status: round.status,\n              limit_quantity: roundGood.limit_quantity\n            });\n          }\n        }\n      }\n\n      // 分页处理\n      const startIndex = (page - 1) * limit;\n      const endIndex = startIndex + limit;\n      const paginatedList = seckillList.slice(startIndex, endIndex);\n\n      console.log(`返回 ${paginatedList.length} 个秒杀商品`);\n      return this.success(paginatedList);\n\n    } catch (error) {\n      console.error('获取秒杀商品列表失败:', error);\n      console.error('错误堆栈:', error.stack);\n      return this.fail('获取商品列表失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取秒杀商品详情\n   * GET /api/seckill/detail/:id\n   */\n  async detailAction() {\n    try {\n      // 使用查询参数获取id\n      const roundId = this.get('id');\n      const timeId = this.get('time_id');\n\n      console.log('=== 获取秒杀商品详情 ===', {\n        url: this.ctx.url,\n        roundId,\n        timeId\n      });\n\n      if (!roundId) {\n        return this.fail('请提供秒杀商品ID');\n      }\n\n      const roundModel = this.model('flash_sale_rounds');\n      const goodsModel = this.model('goods');\n\n      // 获取秒杀轮次信息\n      const round = await roundModel.where({ id: roundId }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('秒杀商品不存在');\n      }\n\n      // 获取商品详细信息\n      const goods = await goodsModel.where({\n        id: round.goods_id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(goods)) {\n        return this.fail('关联商品不存在');\n      }\n\n      // 计算库存和销量信息\n      const totalStock = round.stock + round.sold_count;\n      const percent = totalStock > 0 ? Math.floor((round.sold_count / totalStock) * 100) : 0;\n\n      const result = {\n        id: round.id,\n        goods_id: round.goods_id,\n        title: round.goods_name || goods.name,\n        image: round.goods_image || goods.list_pic_url,\n        images: goods.list_pic_url ? [goods.list_pic_url] : [],\n        price: parseFloat(round.flash_price),\n        ot_price: parseFloat(round.original_price),\n        quota: round.stock,\n        quota_show: totalStock,\n        percent: percent,\n        stock: round.stock,\n        sales: round.sold_count,\n        start_time: round.start_time,\n        end_time: round.end_time,\n        status: round.status,\n        limit_quantity: round.limit_quantity,\n        goods_brief: goods.goods_brief || '',\n        goods_desc: goods.goods_desc || '',\n        // 秒杀特有字段\n        seckill_info: {\n          round_id: round.id,\n          round_number: round.round_number,\n          time_left: this.calculateTimeLeft(round.end_time),\n          is_active: round.status === 'active',\n          can_buy: round.status === 'active' && round.stock > 0\n        }\n      };\n\n      console.log('秒杀商品详情:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取秒杀商品详情失败:', error);\n      return this.fail('获取商品详情失败');\n    }\n  }\n\n  /**\n   * 获取秒杀商品详情（用于商品详情页）\n   * GET /api/seckill/goodsDetail?goods_id=xxx&round_id=xxx\n   */\n  async goodsDetailAction() {\n    try {\n      const goodsId = this.get('goods_id');\n      const roundId = this.get('round_id');\n\n      console.log('=== 获取秒杀商品详情页数据 ===', { goodsId, roundId });\n\n      if (!goodsId || !roundId) {\n        return this.fail('请提供商品ID和轮次ID');\n      }\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n      const goodsModel = this.model('goods');\n\n      // 获取轮次信息\n      const round = await roundModel.where({ id: roundId }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('秒杀轮次不存在');\n      }\n\n      // 获取轮次商品信息\n      const roundGoods = await roundGoodsModel.where({\n        round_id: roundId,\n        goods_id: goodsId\n      }).find();\n\n      if (think.isEmpty(roundGoods)) {\n        return this.fail('该商品不在此秒杀轮次中');\n      }\n\n      // 获取商品详细信息\n      const goods = await goodsModel.where({\n        id: goodsId,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(goods)) {\n        return this.fail('商品不存在');\n      }\n\n      // 计算剩余时间\n      const now = new Date();\n      const endTime = new Date(round.end_time);\n      const timeLeft = Math.max(0, Math.floor((endTime - now) / 1000));\n\n      // 计算进度百分比\n      const totalStock = roundGoods.total_stock || roundGoods.stock;\n      const soldCount = roundGoods.sold_count || 0;\n      const percent = totalStock > 0 ? Math.floor((soldCount / totalStock) * 100) : 0;\n\n      const result = {\n        // 商品基本信息\n        goods_id: goods.id,\n        name: goods.name,\n        list_pic_url: goods.list_pic_url,\n        goods_brief: goods.goods_brief,\n        goods_desc: goods.goods_desc,\n        retail_price: goods.retail_price,\n\n        // 秒杀信息\n        seckill_info: {\n          round_id: round.id,\n          round_name: round.round_name,\n          flash_price: roundGoods.flash_price,\n          original_price: roundGoods.original_price,\n          stock: roundGoods.stock,\n          sold_count: soldCount,\n          total_stock: totalStock,\n          percent: percent,\n          limit_quantity: roundGoods.limit_quantity,\n          start_time: round.start_time,\n          end_time: round.end_time,\n          status: round.status,\n          time_left: timeLeft,\n          is_active: round.status === 'active' && timeLeft > 0,\n          can_buy: round.status === 'active' && roundGoods.stock > 0 && timeLeft > 0\n        }\n      };\n\n      console.log('秒杀商品详情页数据:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取秒杀商品详情页数据失败:', error);\n      return this.fail('获取商品详情失败');\n    }\n  }\n\n  /**\n   * 获取首页秒杀数据（简化版本）\n   * GET /api/seckill/home\n   */\n  async homeAction() {\n    try {\n      console.log('=== 获取首页秒杀数据 ===');\n\n      const roundModel = this.model('flash_sale_rounds');\n      const goodsModel = this.model('goods');\n\n      // 获取当前进行中的轮次（限制数量）\n      const rounds = await roundModel.where({\n        status: 'active'\n      }).order('start_time ASC').limit(3).select();\n\n      if (!rounds || rounds.length === 0) {\n        return this.success({\n          seckillTime: [],\n          seckillTimeIndex: -1,\n          list: []\n        });\n      }\n\n      const seckillList = [];\n\n      for (const round of rounds) {\n        // 获取商品详细信息\n        const goods = await goodsModel.where({\n          id: round.goods_id,\n          is_delete: 0,\n          is_on_sale: 1\n        }).find();\n\n        if (!think.isEmpty(goods)) {\n          // 计算进度百分比\n          const totalStock = round.stock + round.sold_count;\n          const percent = totalStock > 0 ? Math.floor((round.sold_count / totalStock) * 100) : 0;\n\n          seckillList.push({\n            id: round.id,\n            goods_id: round.goods_id,\n            title: round.goods_name || goods.name,\n            image: round.goods_image || goods.list_pic_url,\n            price: parseFloat(round.flash_price),\n            ot_price: parseFloat(round.original_price),\n            quota: round.stock,\n            quota_show: totalStock,\n            percent: percent,\n            stock: round.stock,\n            sales: round.sold_count,\n            start_time: round.start_time,\n            end_time: round.end_time,\n            status: round.status,\n            limit_quantity: round.limit_quantity\n          });\n        }\n      }\n\n      // 模拟时间段数据\n      const result = {\n        time: '10:00',\n        stop: Math.floor(Date.now() / 1000) + 3600, // 1小时后结束\n        list: seckillList\n      };\n\n      console.log(`返回 ${seckillList.length} 个首页秒杀商品`);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取首页秒杀数据失败:', error);\n      return this.fail('获取数据失败');\n    }\n  }\n\n  /**\n   * 初始化测试数据\n   * GET /api/seckill/init\n   */\n  async initAction() {\n    try {\n      console.log('=== 初始化秒杀测试数据 ===');\n\n      const timeSlotModel = this.model('flash_sale_time_slots');\n      const roundModel = this.model('flash_sale_rounds');\n      const goodsModel = this.model('goods');\n      const categoryModel = this.model('category');\n\n      // 1. 插入商品分类\n      const categories = [\n        { id: 1, name: '手机数码', parent_id: 0, sort_order: 1, is_show: 1 },\n        { id: 2, name: '电脑办公', parent_id: 0, sort_order: 2, is_show: 1 }\n      ];\n\n      for (const category of categories) {\n        const existingCategory = await categoryModel.where({ id: category.id }).find();\n        if (think.isEmpty(existingCategory)) {\n          await categoryModel.add(category);\n        }\n      }\n\n      // 2. 插入测试商品\n      const goods = [\n        {\n          id: 1,\n          category_id: 1,\n          is_on_sale: 1,\n          name: 'iPhone 15 Pro Max 256GB',\n          goods_number: 50,\n          sell_volume: 128,\n          retail_price: 8999.00,\n          min_retail_price: 8999.00,\n          cost_price: 7500.00,\n          min_cost_price: 7500.00,\n          goods_brief: '苹果最新旗舰手机，搭载A17 Pro芯片',\n          goods_unit: '台',\n          list_pic_url: '/images/goods/iphone15pro.jpg',\n          add_time: Math.floor(Date.now() / 1000),\n          is_delete: 0\n        },\n        {\n          id: 2,\n          category_id: 1,\n          is_on_sale: 1,\n          name: '小米14 Ultra 512GB',\n          goods_number: 30,\n          sell_volume: 89,\n          retail_price: 5999.00,\n          min_retail_price: 5999.00,\n          cost_price: 4500.00,\n          min_cost_price: 4500.00,\n          goods_brief: '小米年度旗舰，徕卡影像系统',\n          goods_unit: '台',\n          list_pic_url: '/images/goods/mi14ultra.jpg',\n          add_time: Math.floor(Date.now() / 1000),\n          is_delete: 0\n        },\n        {\n          id: 3,\n          category_id: 2,\n          is_on_sale: 1,\n          name: 'MacBook Pro 14寸 M3',\n          goods_number: 20,\n          sell_volume: 45,\n          retail_price: 15999.00,\n          min_retail_price: 15999.00,\n          cost_price: 12000.00,\n          min_cost_price: 12000.00,\n          goods_brief: '苹果专业级笔记本电脑，M3芯片',\n          goods_unit: '台',\n          list_pic_url: '/images/goods/macbookpro.jpg',\n          add_time: Math.floor(Date.now() / 1000),\n          is_delete: 0\n        }\n      ];\n\n      for (const goodsItem of goods) {\n        const existingGoods = await goodsModel.where({ id: goodsItem.id }).find();\n        if (think.isEmpty(existingGoods)) {\n          await goodsModel.add(goodsItem);\n        }\n      }\n\n      // 3. 插入秒杀时间段\n      const timeSlots = [\n        { id: 1, name: '上午场', start_time: '10:00:00', end_time: '12:00:00', sort_order: 1, is_active: 1 },\n        { id: 2, name: '下午场', start_time: '14:00:00', end_time: '16:00:00', sort_order: 2, is_active: 1 },\n        { id: 3, name: '晚上场', start_time: '20:00:00', end_time: '22:00:00', sort_order: 3, is_active: 1 }\n      ];\n\n      for (const timeSlot of timeSlots) {\n        const existingTimeSlot = await timeSlotModel.where({ id: timeSlot.id }).find();\n        if (think.isEmpty(existingTimeSlot)) {\n          await timeSlotModel.add(timeSlot);\n        }\n      }\n\n      // 4. 插入秒杀轮次\n      const now = new Date();\n      const rounds = [\n        {\n          id: 1,\n          round_number: 1,\n          goods_id: 1,\n          goods_name: 'iPhone 15 Pro Max 256GB',\n          goods_image: '/images/goods/iphone15pro.jpg',\n          original_price: 8999.00,\n          flash_price: 6999.00,\n          stock: 10,\n          sold_count: 3,\n          limit_quantity: 1,\n          start_time: now,\n          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后\n          status: 'active'\n        },\n        {\n          id: 2,\n          round_number: 2,\n          goods_id: 2,\n          goods_name: '小米14 Ultra 512GB',\n          goods_image: '/images/goods/mi14ultra.jpg',\n          original_price: 5999.00,\n          flash_price: 4999.00,\n          stock: 15,\n          sold_count: 8,\n          limit_quantity: 2,\n          start_time: now,\n          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后\n          status: 'active'\n        },\n        {\n          id: 3,\n          round_number: 3,\n          goods_id: 3,\n          goods_name: 'MacBook Pro 14寸 M3',\n          goods_image: '/images/goods/macbookpro.jpg',\n          original_price: 15999.00,\n          flash_price: 13999.00,\n          stock: 5,\n          sold_count: 2,\n          limit_quantity: 1,\n          start_time: now,\n          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后\n          status: 'active'\n        }\n      ];\n\n      for (const round of rounds) {\n        const existingRound = await roundModel.where({ id: round.id }).find();\n        if (think.isEmpty(existingRound)) {\n          await roundModel.add(round);\n        }\n      }\n\n      return this.success({\n        message: '测试数据初始化成功',\n        data: {\n          categories: categories.length,\n          goods: goods.length,\n          timeSlots: timeSlots.length,\n          rounds: rounds.length\n        }\n      });\n\n    } catch (error) {\n      console.error('初始化测试数据失败:', error);\n      return this.fail('初始化测试数据失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 计算剩余时间\n   */\n  calculateTimeLeft(endTime) {\n    const now = moment();\n    const end = moment(endTime);\n    const diff = end.diff(now);\n\n    if (diff <= 0) {\n      return { hours: 0, minutes: 0, seconds: 0 };\n    }\n\n    const duration = moment.duration(diff);\n    return {\n      hours: Math.floor(duration.asHours()),\n      minutes: duration.minutes(),\n      seconds: duration.seconds()\n    };\n  }\n\n  /**\n   * 临时测试API - 更新轮次状态\n   * GET /api/seckill/updateRound?round_id=32\n   */\n  async updateRoundAction() {\n    try {\n      const roundId = this.get('round_id') || 32;\n      const now = new Date();\n      const startTime = new Date(now.getTime() - 10 * 60 * 1000); // 10分钟前开始\n      const endTime = new Date(now.getTime() + 120 * 60 * 1000);   // 2小时后结束\n\n      const startTimeStr = startTime.toISOString().slice(0, 19).replace('T', ' ');\n      const endTimeStr = endTime.toISOString().slice(0, 19).replace('T', ' ');\n\n      console.log('更新轮次状态:', { roundId, startTimeStr, endTimeStr });\n\n      await this.model('flash_sale_rounds').where({ id: roundId }).update({\n        start_time: startTimeStr,\n        end_time: endTimeStr,\n        status: 'active'\n      });\n\n      return this.success({\n        message: '轮次状态更新成功',\n        round_id: roundId,\n        start_time: startTimeStr,\n        end_time: endTimeStr,\n        status: 'active'\n      });\n    } catch (error) {\n      console.error('更新轮次状态失败:', error);\n      return this.fail(500, '更新失败: ' + error.message);\n    }\n  }\n};\n"]}