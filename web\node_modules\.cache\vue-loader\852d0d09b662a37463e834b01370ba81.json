{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue", "mtime": 1754305692512}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFBhZ2VQYXRoOiAiL2Rhc2hib2FyZCIsCiAgICAgIGxvZ2luSW5mbzogbnVsbCwKICAgICAgaXNDb2xsYXBzZWQ6IGZhbHNlLAogICAgICBvcGVuU3VibWVudXM6IHsKICAgICAgICBnb29kczogZmFsc2UsCiAgICAgICAgc2V0dGluZ3M6IGZhbHNlCiAgICAgIH0sCiAgICAgIGlzUHJvbW90aW9uTWVudU9wZW46IGZhbHNlLAogICAgICBpc1BlcnNvbmFsUHJvbW90aW9uTWVudU9wZW46IGZhbHNlLAogICAgICBpc01hcmtldGluZ01lbnVPcGVuOiBmYWxzZQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHRvZ2dsZVNpZGViYXIoKSB7CiAgICAgIHRoaXMuaXNDb2xsYXBzZWQgPSAhdGhpcy5pc0NvbGxhcHNlZDsKICAgICAgLy8g6Kem5Y+R54i257uE5Lu25pu05paw5biD5bGACiAgICAgIHRoaXMuJGVtaXQoJ3NpZGViYXItdG9nZ2xlJywgdGhpcy5pc0NvbGxhcHNlZCk7CiAgICB9LAogICAgdG9nZ2xlU3VibWVudShzdWJtZW51TmFtZSkgewogICAgICBpZiAoIXRoaXMuaXNDb2xsYXBzZWQpIHsKICAgICAgICB0aGlzLm9wZW5TdWJtZW51c1tzdWJtZW51TmFtZV0gPSAhdGhpcy5vcGVuU3VibWVudXNbc3VibWVudU5hbWVdOwogICAgICB9CiAgICB9LAogICAgdG9nZ2xlUHJvbW90aW9uTWVudSgpIHsKICAgICAgaWYgKCF0aGlzLmlzQ29sbGFwc2VkKSB7CiAgICAgICAgdGhpcy5pc1Byb21vdGlvbk1lbnVPcGVuID0gIXRoaXMuaXNQcm9tb3Rpb25NZW51T3BlbjsKICAgICAgfQogICAgfSwKICAgIHRvZ2dsZVBlcnNvbmFsUHJvbW90aW9uTWVudSgpIHsKICAgICAgaWYgKCF0aGlzLmlzQ29sbGFwc2VkKSB7CiAgICAgICAgdGhpcy5pc1BlcnNvbmFsUHJvbW90aW9uTWVudU9wZW4gPSAhdGhpcy5pc1BlcnNvbmFsUHJvbW90aW9uTWVudU9wZW47CiAgICAgIH0KICAgIH0sCiAgICB0b2dnbGVNYXJrZXRpbmdNZW51KCkgewogICAgICBpZiAoIXRoaXMuaXNDb2xsYXBzZWQpIHsKICAgICAgICB0aGlzLmlzTWFya2V0aW5nTWVudU9wZW4gPSAhdGhpcy5pc01hcmtldGluZ01lbnVPcGVuOwogICAgICB9CiAgICB9LAogICAgdXBkYXRlTWVudVN0YXRlKCkgewogICAgICAvLyDlpoLmnpzlvZPliY3ot6/nlLHmmK/lm6LpmJ/liIbplIDnm7jlhbPpobXpnaLvvIjkvYbkuI3mmK/llYblk4HliIbplIDmsaDvvInvvIzoh6rliqjlsZXlvIDlm6LpmJ/liIbplIDoj5zljZUKICAgICAgaWYgKHRoaXMuJHJvdXRlLnBhdGguaW5jbHVkZXMoJy9kYXNoYm9hcmQvcHJvbW90aW9uLycpICYmIHRoaXMuJHJvdXRlLnBhdGggIT09ICcvZGFzaGJvYXJkL3Byb21vdGlvbi9wcm9kdWN0LXBvb2wnKSB7CiAgICAgICAgdGhpcy5pc1Byb21vdGlvbk1lbnVPcGVuID0gdHJ1ZTsKICAgICAgfQogICAgICAvLyDlpoLmnpzlvZPliY3ot6/nlLHmmK/kuKrkurrmjqjlub/nm7jlhbPpobXpnaLvvIzoh6rliqjlsZXlvIDkuKrkurrmjqjlub/oj5zljZUKICAgICAgaWYgKHRoaXMuJHJvdXRlLnBhdGguaW5jbHVkZXMoJy9kYXNoYm9hcmQvcGVyc29uYWwtcHJvbW90aW9uLycpKSB7CiAgICAgICAgdGhpcy5pc1BlcnNvbmFsUHJvbW90aW9uTWVudU9wZW4gPSB0cnVlOwogICAgICB9CiAgICAgIC8vIOWmguaenOW9k+WJjei3r+eUseaYr+iQpemUgOW3peWFt+ebuOWFs+mhtemdou+8jOiHquWKqOWxleW8gOiQpemUgOW3peWFt+iPnOWNlQogICAgICBpZiAodGhpcy4kcm91dGUucGF0aC5pbmNsdWRlcygnL2Rhc2hib2FyZC9tYXJrZXRpbmcvJykpIHsKICAgICAgICB0aGlzLmlzTWFya2V0aW5nTWVudU9wZW4gPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlT3BlbihrZXksIGtleVBhdGgpIHsKICAgICAgY29uc29sZS5sb2coa2V5LCBrZXlQYXRoKTsKICAgIH0sCiAgICBoYW5kbGVDbG9zZShrZXksIGtleVBhdGgpIHsKICAgICAgY29uc29sZS5sb2coa2V5LCBrZXlQYXRoKTsKICAgIH0sCiAgICBsb2dvdXQoKSB7CiAgICAgIC8vIOebtOaOpemAgOWHuu+8jOS4jeaYvuekuuehruiupOW8ueeqlwogICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTsKICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2UoeyBuYW1lOiAibG9naW4iIH0pOwogICAgfSwKICAgIGNoZWNrTG9naW4oKSB7CiAgICAgIHRoaXMuYXhpb3MuZ2V0KCJpbmRleC9jaGVja0xvZ2luIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhKTsKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gNDAxKSB7CiAgICAgICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKTsKICAgICAgICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsgbmFtZTogImxvZ2luIiB9KTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgIC8vIGF4aW9z5oum5oiq5Zmo5Lya6Ieq5Yqo5aSE55CGNDAx6ZSZ6K+vCiAgICAgICAgY29uc29sZS5sb2coJ2NoZWNrTG9naW4gZXJyb3I6JywgZXJyb3IpOwogICAgICB9KTsKICAgIH0sCiAgfSwKICB3YXRjaDogewogICAgaXNDb2xsYXBzZWQobmV3VmFsKSB7CiAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICAvLyDmipjlj6Dml7blhbPpl63miYDmnInlrZDoj5zljZUKICAgICAgICB0aGlzLm9wZW5TdWJtZW51cy5nb29kcyA9IGZhbHNlOwogICAgICAgIHRoaXMub3BlblN1Ym1lbnVzLnNldHRpbmdzID0gZmFsc2U7CiAgICAgICAgdGhpcy5pc1Byb21vdGlvbk1lbnVPcGVuID0gZmFsc2U7CiAgICAgICAgdGhpcy5pc1BlcnNvbmFsUHJvbW90aW9uTWVudU9wZW4gPSBmYWxzZTsKICAgICAgICB0aGlzLmlzTWFya2V0aW5nTWVudU9wZW4gPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgICckcm91dGUnKCkgewogICAgICB0aGlzLnVwZGF0ZU1lbnVTdGF0ZSgpOwogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIGNvbnNvbGUubG9nKHRoaXMuJHJvdXRlLnBhdGgpOwogICAgdGhpcy5jaGVja0xvZ2luKCk7CiAgICBpZiAoIXRoaXMubG9naW5JbmZvKSB7CiAgICAgIHRoaXMubG9naW5JbmZvID0gSlNPTi5wYXJzZSgKICAgICAgICB3aW5kb3cubG9jYWxTdG9yYWdlLmdldEl0ZW0oInVzZXJJbmZvIikgfHwgbnVsbAogICAgICApOwogICAgfQoKICAgIC8vIOagueaNruW9k+WJjei3r+eUseiHquWKqOWxleW8gOWvueW6lOeahOiPnOWNlQogICAgdGhpcy51cGRhdGVNZW51U3RhdGUoKTsKICB9LAp9Owo="}, {"version": 3, "sources": ["Sidebar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAibA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Sidebar.vue", "sourceRoot": "src/components/Common", "sourcesContent": ["<template>\n  <div\n    id=\"sidebar\"\n    :class=\"['sidebar', 'bg-white', 'border-r', 'h-full', 'fixed', 'left-0', 'top-0', 'overflow-y-auto', 'z-10', 'shadow-sm', { 'collapsed': isCollapsed }]\"\n  >\n    <div class=\"p-4 flex items-center justify-between border-b\">\n      <div class=\"font-['Pacifico'] text-xl text-gray-800\">logo</div>\n      <button\n        @click=\"toggleSidebar\"\n        class=\"w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100\"\n      >\n        <i :class=\"isCollapsed ? 'ri-menu-unfold-line ri-lg' : 'ri-menu-fold-line ri-lg'\"></i>\n      </button>\n    </div>\n    <div class=\"mt-2\">\n      <ul class=\"space-y-1\">\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/welcome\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/welcome' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/welcome' }\"\n            >\n              <i class=\"ri-home-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/welcome' }\">首页</span>\n          </router-link>\n        </li>\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/data-overview\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/data-overview' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/data-overview' }\"\n            >\n              <i class=\"ri-bar-chart-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/data-overview' }\">数据概览</span>\n          </router-link>\n        </li>\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/order\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/order' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/order' }\"\n            >\n              <i class=\"ri-file-list-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/order' }\">订单列表</span>\n          </router-link>\n        </li>\n        <li class=\"mx-2\">\n          <div\n            @click=\"toggleSubmenu('goods')\"\n            class=\"px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex items-center\">\n                <div\n                  class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n                >\n                  <i class=\"ri-shopping-bag-line\"></i>\n                </div>\n                <span class=\"ml-3 menu-text text-sm text-gray-600\">商品管理</span>\n              </div>\n              <i\n                :class=\"['ri-arrow-down-s-line', 'submenu-arrow', 'text-gray-400', { 'transform rotate-180': openSubmenus.goods }]\"\n              ></i>\n            </div>\n          </div>\n          <ul :class=\"['submenu', 'pl-9', 'mt-1', { 'hidden': !openSubmenus.goods }]\">\n            <li>\n              <router-link\n                to=\"/dashboard/goods\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/goods' }\"\n              >商品列表</router-link>\n            </li>\n            <li>\n              <router-link\n                to=\"/dashboard/nature\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/nature' }\"\n              >商品设置</router-link>\n            </li>\n          </ul>\n        </li>\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/shopcart\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/shopcart' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/shopcart' }\"\n            >\n              <i class=\"ri-shopping-cart-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/shopcart' }\">购物车</span>\n          </router-link>\n        </li>\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/user\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/user' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/user' }\"\n            >\n              <i class=\"ri-user-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/user' }\">用户列表</span>\n          </router-link>\n        </li>\n\n        <!-- 商品分销池 - 一级菜单 -->\n        <li class=\"mx-2\">\n          <router-link\n            to=\"/dashboard/promotion/product-pool\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n            :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/product-pool' }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': $route.path === '/dashboard/promotion/product-pool' }\"\n            >\n              <i class=\"ri-store-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\" :class=\"{ 'font-medium': $route.path === '/dashboard/promotion/product-pool' }\">商品分销池</span>\n          </router-link>\n        </li>\n\n        <!-- 团队分销子菜单 -->\n        <li class=\"mx-2\">\n          <div\n            @click=\"togglePromotionMenu\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer\"\n            :class=\"{ 'bg-primary/5 text-primary': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }\"\n            >\n              <i class=\"ri-share-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm flex-1\" :class=\"{ 'font-medium': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }\">团队分销</span>\n            <div class=\"menu-text\">\n              <i class=\"ri-arrow-down-s-line transition-transform duration-200\" :class=\"{ 'rotate-180': isPromotionMenuOpen }\"></i>\n            </div>\n          </div>\n\n          <!-- 团队分销子菜单项 -->\n          <div v-show=\"isPromotionMenuOpen\" class=\"ml-6 mt-1 space-y-1\">\n            <router-link\n              to=\"/dashboard/promotion/member-management\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/member-management' }\"\n            >\n              <span class=\"menu-text\">分销员管理</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/data-overview\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/data-overview' }\"\n            >\n              <span class=\"menu-text\">数据概览</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/member-list\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/member-list' }\"\n            >\n              <span class=\"menu-text\">分销员列表</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/mode-settings\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/mode-settings' }\"\n            >\n              <span class=\"menu-text\">分销模式</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/review-status\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/review-status' }\"\n            >\n              <span class=\"menu-text\">审核状态</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/commission-settings\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/commission-settings' }\"\n            >\n              <span class=\"menu-text\">分销佣金</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/promotion/recruitment-rules\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/recruitment-rules' }\"\n            >\n              <span class=\"menu-text\">招募设置</span>\n            </router-link>\n\n\n\n\n          </div>\n        </li>\n\n        <!-- 个人推广子菜单 -->\n        <li class=\"mx-2\">\n          <div\n            @click=\"togglePersonalPromotionMenu\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer\"\n            :class=\"{ 'bg-primary/5 text-primary': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }\"\n            >\n              <i class=\"ri-user-star-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm flex-1\" :class=\"{ 'font-medium': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }\">个人推广</span>\n            <div class=\"menu-text\">\n              <i class=\"ri-arrow-down-s-line transition-transform duration-200\" :class=\"{ 'rotate-180': isPersonalPromotionMenuOpen }\"></i>\n            </div>\n          </div>\n\n          <!-- 个人推广子菜单项 -->\n          <div v-show=\"isPersonalPromotionMenuOpen\" class=\"ml-6 mt-1 space-y-1\">\n            <router-link\n              to=\"/dashboard/personal-promotion/promoter-list\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/promoter-list' }\"\n            >\n              <span class=\"menu-text\">推广员列表</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/personal-promotion/data-overview\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/data-overview' }\"\n            >\n              <span class=\"menu-text\">数据概览</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/personal-promotion/commission\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/commission' }\"\n            >\n              <span class=\"menu-text\">推广佣金</span>\n            </router-link>\n\n\n\n            <router-link\n              to=\"/dashboard/personal-promotion/share-records\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/share-records' }\"\n            >\n              <span class=\"menu-text\">分享记录</span>\n            </router-link>\n          </div>\n        </li>\n\n        <!-- 营销工具子菜单 -->\n        <li class=\"mx-2\">\n          <div\n            @click=\"toggleMarketingMenu\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer\"\n            :class=\"{ 'bg-primary/5 text-primary': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n              :class=\"{ 'text-primary': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }\"\n            >\n              <i class=\"ri-megaphone-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm flex-1\" :class=\"{ 'font-medium': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }\">营销工具</span>\n            <div class=\"menu-text\">\n              <i class=\"ri-arrow-down-s-line transition-transform duration-200\" :class=\"{ 'rotate-180': isMarketingMenuOpen }\"></i>\n            </div>\n          </div>\n\n          <!-- 营销工具子菜单项 -->\n          <div v-show=\"isMarketingMenuOpen\" class=\"ml-6 mt-1 space-y-1\">\n            <router-link\n              to=\"/dashboard/marketing/order-gifts\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/order-gifts' }\"\n            >\n              <span class=\"menu-text\">订单有礼</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/marketing/coupons\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/coupons' }\"\n            >\n              <span class=\"menu-text\">优惠券</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/marketing/points-goods\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/points-goods' }\"\n            >\n              <span class=\"menu-text\">积分兑好礼</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/marketing/flash-sale\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/flash-sale' }\"\n            >\n              <span class=\"menu-text\">限时秒杀</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/marketing/group-buy\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/group-buy' }\"\n            >\n              <span class=\"menu-text\">团购活动</span>\n            </router-link>\n\n            <router-link\n              to=\"/dashboard/marketing/points-goods\"\n              class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm\"\n              :class=\"{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/points-goods' }\"\n            >\n              <span class=\"menu-text\">积分兑好礼</span>\n            </router-link>\n          </div>\n        </li>\n        <li class=\"mx-2\">\n          <div\n            @click=\"toggleSubmenu('settings')\"\n            class=\"px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group\"\n          >\n            <div class=\"flex items-center justify-between\">\n              <div class=\"flex items-center\">\n                <div\n                  class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n                >\n                  <i class=\"ri-store-line\"></i>\n                </div>\n                <span class=\"ml-3 menu-text text-sm text-gray-600\">店铺设置</span>\n              </div>\n              <i\n                :class=\"['ri-arrow-down-s-line', 'submenu-arrow', 'text-gray-400', { 'transform rotate-180': openSubmenus.settings }]\"\n              ></i>\n            </div>\n          </div>\n          <ul :class=\"['submenu', 'pl-9', 'mt-1', { 'hidden': !openSubmenus.settings }]\">\n            <li>\n              <router-link\n                to=\"/dashboard/settings/showset\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/settings/showset' }\"\n              >显示设置</router-link>\n            </li>\n            <li>\n              <router-link\n                to=\"/dashboard/ad\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/ad' }\"\n              >广告列表</router-link>\n            </li>\n            <li>\n              <router-link\n                to=\"/dashboard/freight\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/freight' }\"\n              >运费模板</router-link>\n            </li>\n            <li>\n              <router-link\n                to=\"/dashboard/shipper\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/shipper' }\"\n              >快递设置</router-link>\n            </li>\n            <li>\n              <router-link\n                to=\"/dashboard/admin\"\n                class=\"block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg\"\n                :class=\"{ 'text-primary bg-gray-100': $route.path === '/dashboard/admin' }\"\n              >管理员</router-link>\n            </li>\n          </ul>\n        </li>\n\n        <li class=\"mx-2 mt-8\">\n          <a\n            href=\"#\"\n            @click=\"logout\"\n            class=\"flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group\"\n          >\n            <div\n              class=\"w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600\"\n            >\n              <i class=\"ri-logout-box-line\"></i>\n            </div>\n            <span class=\"ml-3 menu-text text-sm\">退出</span>\n          </a>\n        </li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      currentPagePath: \"/dashboard\",\n      loginInfo: null,\n      isCollapsed: false,\n      openSubmenus: {\n        goods: false,\n        settings: false\n      },\n      isPromotionMenuOpen: false,\n      isPersonalPromotionMenuOpen: false,\n      isMarketingMenuOpen: false\n    };\n  },\n  methods: {\n    toggleSidebar() {\n      this.isCollapsed = !this.isCollapsed;\n      // 触发父组件更新布局\n      this.$emit('sidebar-toggle', this.isCollapsed);\n    },\n    toggleSubmenu(submenuName) {\n      if (!this.isCollapsed) {\n        this.openSubmenus[submenuName] = !this.openSubmenus[submenuName];\n      }\n    },\n    togglePromotionMenu() {\n      if (!this.isCollapsed) {\n        this.isPromotionMenuOpen = !this.isPromotionMenuOpen;\n      }\n    },\n    togglePersonalPromotionMenu() {\n      if (!this.isCollapsed) {\n        this.isPersonalPromotionMenuOpen = !this.isPersonalPromotionMenuOpen;\n      }\n    },\n    toggleMarketingMenu() {\n      if (!this.isCollapsed) {\n        this.isMarketingMenuOpen = !this.isMarketingMenuOpen;\n      }\n    },\n    updateMenuState() {\n      // 如果当前路由是团队分销相关页面（但不是商品分销池），自动展开团队分销菜单\n      if (this.$route.path.includes('/dashboard/promotion/') && this.$route.path !== '/dashboard/promotion/product-pool') {\n        this.isPromotionMenuOpen = true;\n      }\n      // 如果当前路由是个人推广相关页面，自动展开个人推广菜单\n      if (this.$route.path.includes('/dashboard/personal-promotion/')) {\n        this.isPersonalPromotionMenuOpen = true;\n      }\n      // 如果当前路由是营销工具相关页面，自动展开营销工具菜单\n      if (this.$route.path.includes('/dashboard/marketing/')) {\n        this.isMarketingMenuOpen = true;\n      }\n    },\n    handleOpen(key, keyPath) {\n      console.log(key, keyPath);\n    },\n    handleClose(key, keyPath) {\n      console.log(key, keyPath);\n    },\n    logout() {\n      // 直接退出，不显示确认弹窗\n      localStorage.clear();\n      this.$router.replace({ name: \"login\" });\n    },\n    checkLogin() {\n      this.axios.get(\"index/checkLogin\").then((response) => {\n        console.log(response.data);\n        if (response.data.errno === 401) {\n          localStorage.clear();\n          this.$router.replace({ name: \"login\" });\n        }\n      }).catch((error) => {\n        // axios拦截器会自动处理401错误\n        console.log('checkLogin error:', error);\n      });\n    },\n  },\n  watch: {\n    isCollapsed(newVal) {\n      if (newVal) {\n        // 折叠时关闭所有子菜单\n        this.openSubmenus.goods = false;\n        this.openSubmenus.settings = false;\n        this.isPromotionMenuOpen = false;\n        this.isPersonalPromotionMenuOpen = false;\n        this.isMarketingMenuOpen = false;\n      }\n    },\n    '$route'() {\n      this.updateMenuState();\n    }\n  },\n  mounted() {\n    console.log(this.$route.path);\n    this.checkLogin();\n    if (!this.loginInfo) {\n      this.loginInfo = JSON.parse(\n        window.localStorage.getItem(\"userInfo\") || null\n      );\n    }\n\n    // 根据当前路由自动展开对应的菜单\n    this.updateMenuState();\n  },\n};\n</script>\n<style>\n/* 配置Tailwind CSS主题色 */\n:root {\n  --primary: #4f46e5;\n  --secondary: #6366f1;\n}\n\n.sidebar {\n  width: 240px;\n  min-width: 240px;\n  transition: all 0.3s;\n}\n\n.sidebar.collapsed {\n  width: 80px;\n  min-width: 80px;\n}\n\n.sidebar.collapsed .menu-text {\n  display: none;\n}\n\n.sidebar.collapsed .submenu-arrow {\n  display: none;\n}\n\n.sidebar.collapsed .submenu {\n  display: none !important;\n}\n\n/* Tailwind CSS 自定义类 */\n.text-primary {\n  color: var(--primary);\n}\n\n.bg-primary\\/5 {\n  background-color: rgba(79, 70, 229, 0.05);\n}\n\n.hover\\:text-primary:hover {\n  color: var(--primary);\n}\n\n.group-hover\\:text-primary {\n  color: #9ca3af;\n}\n\n.group:hover .group-hover\\:text-primary {\n  color: var(--primary);\n}\n\n/* Remix Icons 样式修复 */\n:where([class^=\"ri-\"])::before {\n  content: \"\\f3c2\";\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sidebar {\n    width: 100%;\n    transform: translateX(-100%);\n  }\n\n  .sidebar.show {\n    transform: translateX(0);\n  }\n}\n</style>\n"]}]}