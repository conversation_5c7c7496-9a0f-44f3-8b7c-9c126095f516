{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue?vue&type=template&id=da052a4e&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue", "mtime": 1754305493472}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}