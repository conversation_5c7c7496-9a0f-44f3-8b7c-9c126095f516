const Base = require('./base.js');

module.exports = class extends Base {
  
  /**
   * 获取积分商城首页数据
   * GET /api/points-goods/home
   */
  async homeAction() {
    console.log('=== 获取积分商城首页数据 ===');
    
    try {
      // 获取积分商城配置
      const configModel = this.model('points_config');
      const configs = await configModel.select();
      const configMap = {};
      configs.forEach(config => {
        configMap[config.config_key] = config.config_value;
      });
      
      // 检查积分商城是否启用
      if (configMap.points_mall_enabled !== '1') {
        return this.fail(400, '积分商城暂未开放');
      }
      
      // 获取热门商品（前6个）- 联表查询真实商品信息
      const pointsGoodsModel = this.model('points_goods');
      const hotGoods = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where({
          'pg.status': 1,
          'pg.is_delete': 0,
          'g.is_delete': 0,
          'g.is_on_sale': 1,
          'p.is_delete': 0,
          'p.is_on_sale': 1
        })
        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')
        .order('pg.is_hot DESC, pg.sort DESC, pg.id DESC')
        .limit(6)
        .select();
      
      // 处理规格信息
      for (let item of hotGoods) {
        if (item.goods_specification_ids) {
          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);
          item.specification_info = specInfo;
        }
        
        // 检查库存限制
        if (item.stock_limit > 0) {
          item.available_stock = Math.min(item.stock, item.stock_limit - item.sold_count);
        } else {
          item.available_stock = item.stock;
        }
      }
      
      // 获取分类列表
      const categories = await this.model('points_categories')
        .where({
          status: 1,
          is_delete: 0
        })
        .order('sort DESC')
        .select();
      
      return this.success({
        config: {
          enabled: configMap.points_mall_enabled === '1',
          banner: JSON.parse(configMap.points_mall_banner || '[]'),
          notice: configMap.points_mall_notice || '欢迎来到积分商城，用积分兑换心仪商品！',
          max_exchange_per_day: parseInt(configMap.max_exchange_per_day || '10'),
          min_points_required: parseInt(configMap.min_points_required || '100')
        },
        hot_goods: hotGoods,
        categories: categories
      });
      
    } catch (error) {
      console.error('获取积分商城首页数据失败:', error);
      return this.fail(500, '获取数据失败');
    }
  }
  
  /**
   * 获取积分商品列表
   * GET /api/points-goods/list
   */
  async listAction() {
    console.log('=== 获取积分商品列表 ===');
    
    try {
      const page = this.get('page') || 1;
      const limit = this.get('limit') || 20;
      const category_id = this.get('category_id') || '';
      const keyword = this.get('keyword') || '';
      const sort = this.get('sort') || 'default'; // default, price_asc, price_desc, points_asc, points_desc
      
      // 构建查询条件
      const where = {
        'pg.status': 1,
        'pg.is_delete': 0,
        'g.is_delete': 0,
        'g.is_on_sale': 1,
        'p.is_delete': 0,
        'p.is_on_sale': 1
      };
      
      if (category_id !== '') {
        where['g.category_id'] = parseInt(category_id);
      }
      
      if (keyword !== '') {
        where['g.name'] = ['like', `%${keyword}%`];
      }
      
      // 构建排序条件
      let orderBy = 'pg.sort DESC, pg.id DESC';
      switch (sort) {
        case 'price_asc':
          orderBy = 'p.retail_price ASC';
          break;
        case 'price_desc':
          orderBy = 'p.retail_price DESC';
          break;
        case 'points_asc':
          orderBy = 'pg.points_price ASC';
          break;
        case 'points_desc':
          orderBy = 'pg.points_price DESC';
          break;
        case 'sales':
          orderBy = 'pg.sold_count DESC';
          break;
        default:
          orderBy = 'pg.sort DESC, pg.id DESC';
      }
      
      // 联表查询积分商品列表
      const pointsGoodsModel = this.model('points_goods');
      const list = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where(where)
        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')
        .order(orderBy)
        .page(page, limit)
        .select();
      
      // 获取总数
      const total = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where(where)
        .count();
      
      // 处理规格信息和库存
      for (let item of list) {
        if (item.goods_specification_ids) {
          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);
          item.specification_info = specInfo;
        }
        
        // 检查库存限制
        if (item.stock_limit > 0) {
          item.available_stock = Math.min(item.stock, item.stock_limit - item.sold_count);
        } else {
          item.available_stock = item.stock;
        }
        
        // 检查时间限制
        const now = new Date();
        if (item.start_time && new Date(item.start_time) > now) {
          item.is_available = false;
          item.unavailable_reason = '活动未开始';
        } else if (item.end_time && new Date(item.end_time) < now) {
          item.is_available = false;
          item.unavailable_reason = '活动已结束';
        } else if (item.available_stock <= 0) {
          item.is_available = false;
          item.unavailable_reason = '库存不足';
        } else {
          item.is_available = true;
        }
      }
      
      console.log(`查询到 ${list.length} 条积分商品，总计 ${total} 条`);
      
      return this.success({
        list: list,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit)
      });
      
    } catch (error) {
      console.error('获取积分商品列表失败:', error);
      return this.fail(500, '获取数据失败');
    }
  }

  /**
   * 获取积分商品详情
   * GET /api/points-goods/detail/:id
   */
  async detailAction() {
    console.log('=== 获取积分商品详情 ===');
    
    try {
      const id = this.get('id');
      
      if (!id) {
        return this.fail(400, '商品ID不能为空');
      }
      
      // 联表查询积分商品详情
      const pointsGoodsModel = this.model('points_goods');
      const detail = await pointsGoodsModel.alias('pg')
        .join('hiolabs_goods g ON pg.goods_id = g.id')
        .join('hiolabs_product p ON pg.product_id = p.id')
        .where({
          'pg.id': id,
          'pg.status': 1,
          'pg.is_delete': 0,
          'g.is_delete': 0,
          'g.is_on_sale': 1,
          'p.is_delete': 0,
          'p.is_on_sale': 1
        })
        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.goods_desc, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')
        .find();
      
      if (think.isEmpty(detail)) {
        return this.fail(404, '商品不存在或已下架');
      }
      
      // 获取规格信息
      if (detail.goods_specification_ids) {
        const specInfo = await this.getSpecificationInfo(detail.goods_specification_ids);
        detail.specification_info = specInfo;
      }
      
      // 检查库存限制
      if (detail.stock_limit > 0) {
        detail.available_stock = Math.min(detail.stock, detail.stock_limit - detail.sold_count);
      } else {
        detail.available_stock = detail.stock;
      }
      
      // 检查时间限制
      const now = new Date();
      if (detail.start_time && new Date(detail.start_time) > now) {
        detail.is_available = false;
        detail.unavailable_reason = '活动未开始';
      } else if (detail.end_time && new Date(detail.end_time) < now) {
        detail.is_available = false;
        detail.unavailable_reason = '活动已结束';
      } else if (detail.available_stock <= 0) {
        detail.is_available = false;
        detail.unavailable_reason = '库存不足';
      } else {
        detail.is_available = true;
      }
      
      // 获取商品图片
      const galleryModel = this.model('goods_gallery');
      const gallery = await galleryModel.where({
        goods_id: detail.goods_id,
        is_delete: 0
      }).order('sort_order ASC').select();
      
      detail.gallery = gallery;
      
      console.log('积分商品详情:', detail.goods_name);
      
      return this.success(detail);
      
    } catch (error) {
      console.error('获取积分商品详情失败:', error);
      return this.fail(500, '获取商品详情失败');
    }
  }

  /**
   * 获取规格信息
   */
  async getSpecificationInfo(specificationIds) {
    if (!specificationIds) return '';
    
    try {
      const specIds = specificationIds.split(',').map(id => parseInt(id));
      const specModel = this.model('goods_specification');
      const specs = await specModel.where({
        id: ['IN', specIds],
        is_delete: 0
      }).select();
      
      return specs.map(spec => spec.value).join(' / ');
    } catch (error) {
      console.error('获取规格信息失败:', error);
      return '';
    }
  }
};
