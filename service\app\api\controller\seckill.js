function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');

module.exports = class extends Base {

  /**
   * 获取当前秒杀状态
   * GET /api/seckill/index
   */
  indexAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取当前秒杀状态 ===');

        const roundModel = _this.model('flash_sale_rounds');
        const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

        // 查找当前正在进行的轮次
        const activeRound = yield roundModel.where({
          status: 'active'
        }).find();

        // 查找即将开始的轮次
        const upcomingRound = yield roundModel.where({
          status: 'upcoming'
        }).order('start_time ASC').find();

        let seckillTimeIndex = -1;
        let currentRoundInfo = null;

        if (activeRound) {
          // 有正在进行的轮次
          const startTime = new Date(activeRound.start_time);
          const endTime = new Date(activeRound.end_time);

          // 确保时间有效
          if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
            currentRoundInfo = {
              id: activeRound.id,
              time: startTime.toTimeString().substring(0, 5),
              continued: Math.floor((endTime - startTime) / (1000 * 60 * 60)),
              status: 1,
              state: '抢购中',
              stop: Math.floor(endTime.getTime() / 1000),
              round_id: activeRound.id,
              round_name: activeRound.round_name
            };
            seckillTimeIndex = 0;
          }
        } else if (upcomingRound) {
          // 有即将开始的轮次
          const startTime = new Date(upcomingRound.start_time);
          const endTime = new Date(upcomingRound.end_time);

          // 确保时间有效
          if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime())) {
            currentRoundInfo = {
              id: upcomingRound.id,
              time: startTime.toTimeString().substring(0, 5),
              continued: Math.floor((endTime - startTime) / (1000 * 60 * 60)),
              status: 2,
              state: '即将开始',
              stop: Math.floor(endTime.getTime() / 1000),
              round_id: upcomingRound.id,
              round_name: upcomingRound.round_name
            };
            seckillTimeIndex = 0;
          }
        }

        const seckillTime = currentRoundInfo ? [currentRoundInfo] : [];

        console.log('当前秒杀状态:', { seckillTime, seckillTimeIndex, activeRound, upcomingRound });

        return _this.success({
          seckillTime: seckillTime,
          seckillTimeIndex: seckillTimeIndex,
          lovely: ''
        });
      } catch (error) {
        console.error('获取秒杀时间段失败:', error);
        return _this.fail('获取时间段失败');
      }
    })();
  }

  /**
   * 获取秒杀商品列表
   * GET /api/seckill/list
   */
  listAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        // 获取参数：支持round_id或timeId（兼容旧版本）
        const roundId = _this2.get('round_id') || _this2.post('round_id');
        const timeId = _this2.get('timeId') || _this2.post('timeId');
        const page = parseInt(_this2.get('page') || _this2.post('page')) || 1;
        const limit = parseInt(_this2.get('limit') || _this2.post('limit')) || 10;

        console.log('=== 获取秒杀商品列表 ===', {
          url: _this2.ctx.url,
          method: _this2.method,
          roundId,
          timeId,
          page,
          limit
        });

        const roundModel = _this2.model('flash_sale_rounds');
        const roundGoodsModel = _this2.model('flash_sale_round_goods');
        const goodsModel = _this2.model('goods');

        let rounds = [];

        if (roundId) {
          // 如果提供了round_id，直接查询指定轮次
          const round = yield roundModel.where({ id: roundId }).find();
          if (!think.isEmpty(round)) {
            rounds = [round];
          }
        } else {
          // 否则查询当前active状态的轮次
          const activeRounds = yield roundModel.where({
            status: 'active'
          }).order('start_time ASC').select();

          if (activeRounds && activeRounds.length > 0) {
            rounds = activeRounds;
          } else {
            // 如果没有active轮次，查找即将开始的轮次
            const upcomingRounds = yield roundModel.where({
              status: 'upcoming'
            }).order('start_time ASC').limit(1).select();

            if (upcomingRounds && upcomingRounds.length > 0) {
              rounds = upcomingRounds;
            }
          }
        }

        if (!rounds || rounds.length === 0) {
          console.log('没有找到任何轮次，返回空数组');
          return _this2.success([]);
        }

        const seckillList = [];

        for (const round of rounds) {
          console.log('处理轮次:', round);

          // 获取该轮次的所有商品
          const roundGoods = yield roundGoodsModel.where({
            round_id: round.id
          }).select();

          console.log(`轮次 ${round.id} 的商品:`, roundGoods);

          if (!roundGoods || roundGoods.length === 0) {
            console.log('轮次没有商品，跳过:', round.id);
            continue;
          }

          // 处理该轮次的每个商品
          for (const roundGood of roundGoods) {
            // 获取商品详细信息
            const goods = yield goodsModel.where({
              id: roundGood.goods_id,
              is_delete: 0,
              is_on_sale: 1
            }).find();

            if (!think.isEmpty(goods)) {
              // 计算进度百分比
              const totalStock = roundGood.stock + roundGood.sold_count;
              const percent = totalStock > 0 ? Math.floor(roundGood.sold_count / totalStock * 100) : 0;

              seckillList.push({
                id: round.id,
                round_goods_id: roundGood.id,
                goods_id: roundGood.goods_id,
                title: roundGood.goods_name || goods.name,
                image: roundGood.goods_image || goods.list_pic_url,
                price: parseFloat(roundGood.flash_price),
                ot_price: parseFloat(roundGood.original_price || goods.retail_price),
                quota: roundGood.stock, // 剩余库存
                quota_show: totalStock, // 总库存
                percent: percent, // 进度百分比
                stock: roundGood.stock,
                sales: roundGood.sold_count,
                start_time: round.start_time,
                end_time: round.end_time,
                status: round.status,
                limit_quantity: roundGood.limit_quantity
              });
            }
          }
        }

        // 分页处理
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedList = seckillList.slice(startIndex, endIndex);

        console.log(`返回 ${paginatedList.length} 个秒杀商品`);
        return _this2.success(paginatedList);
      } catch (error) {
        console.error('获取秒杀商品列表失败:', error);
        console.error('错误堆栈:', error.stack);
        return _this2.fail('获取商品列表失败: ' + error.message);
      }
    })();
  }

  /**
   * 获取秒杀商品详情
   * GET /api/seckill/detail/:id
   */
  detailAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        // 使用查询参数获取id
        const roundId = _this3.get('id');
        const timeId = _this3.get('time_id');

        console.log('=== 获取秒杀商品详情 ===', {
          url: _this3.ctx.url,
          roundId,
          timeId
        });

        if (!roundId) {
          return _this3.fail('请提供秒杀商品ID');
        }

        const roundModel = _this3.model('flash_sale_rounds');
        const goodsModel = _this3.model('goods');

        // 获取秒杀轮次信息
        const round = yield roundModel.where({ id: roundId }).find();
        if (think.isEmpty(round)) {
          return _this3.fail('秒杀商品不存在');
        }

        // 获取商品详细信息
        const goods = yield goodsModel.where({
          id: round.goods_id,
          is_delete: 0
        }).find();

        if (think.isEmpty(goods)) {
          return _this3.fail('关联商品不存在');
        }

        // 计算库存和销量信息
        const totalStock = round.stock + round.sold_count;
        const percent = totalStock > 0 ? Math.floor(round.sold_count / totalStock * 100) : 0;

        const result = {
          id: round.id,
          goods_id: round.goods_id,
          title: round.goods_name || goods.name,
          image: round.goods_image || goods.list_pic_url,
          images: goods.list_pic_url ? [goods.list_pic_url] : [],
          price: parseFloat(round.flash_price),
          ot_price: parseFloat(round.original_price),
          quota: round.stock,
          quota_show: totalStock,
          percent: percent,
          stock: round.stock,
          sales: round.sold_count,
          start_time: round.start_time,
          end_time: round.end_time,
          status: round.status,
          limit_quantity: round.limit_quantity,
          goods_brief: goods.goods_brief || '',
          goods_desc: goods.goods_desc || '',
          // 秒杀特有字段
          seckill_info: {
            round_id: round.id,
            round_number: round.round_number,
            time_left: _this3.calculateTimeLeft(round.end_time),
            is_active: round.status === 'active',
            can_buy: round.status === 'active' && round.stock > 0
          }
        };

        console.log('秒杀商品详情:', result);
        return _this3.success(result);
      } catch (error) {
        console.error('获取秒杀商品详情失败:', error);
        return _this3.fail('获取商品详情失败');
      }
    })();
  }

  /**
   * 获取秒杀商品详情（用于商品详情页）
   * GET /api/seckill/goodsDetail?goods_id=xxx&round_id=xxx
   */
  goodsDetailAction() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        const goodsId = _this4.get('goods_id');
        const roundId = _this4.get('round_id');

        console.log('=== 获取秒杀商品详情页数据 ===', { goodsId, roundId });

        if (!goodsId || !roundId) {
          return _this4.fail('请提供商品ID和轮次ID');
        }

        const roundModel = _this4.model('flash_sale_rounds');
        const roundGoodsModel = _this4.model('flash_sale_round_goods');
        const goodsModel = _this4.model('goods');

        // 获取轮次信息
        const round = yield roundModel.where({ id: roundId }).find();
        if (think.isEmpty(round)) {
          return _this4.fail('秒杀轮次不存在');
        }

        // 获取轮次商品信息
        const roundGoods = yield roundGoodsModel.where({
          round_id: roundId,
          goods_id: goodsId
        }).find();

        if (think.isEmpty(roundGoods)) {
          return _this4.fail('该商品不在此秒杀轮次中');
        }

        // 获取商品详细信息
        const goods = yield goodsModel.where({
          id: goodsId,
          is_delete: 0
        }).find();

        if (think.isEmpty(goods)) {
          return _this4.fail('商品不存在');
        }

        // 计算剩余时间
        const now = new Date();
        const endTime = new Date(round.end_time);
        const timeLeft = Math.max(0, Math.floor((endTime - now) / 1000));

        // 计算进度百分比
        const totalStock = roundGoods.total_stock || roundGoods.stock;
        const soldCount = roundGoods.sold_count || 0;
        const percent = totalStock > 0 ? Math.floor(soldCount / totalStock * 100) : 0;

        const result = {
          // 商品基本信息
          goods_id: goods.id,
          name: goods.name,
          list_pic_url: goods.list_pic_url,
          goods_brief: goods.goods_brief,
          goods_desc: goods.goods_desc,
          retail_price: goods.retail_price,

          // 秒杀信息
          seckill_info: {
            round_id: round.id,
            round_name: round.round_name,
            flash_price: roundGoods.flash_price,
            original_price: roundGoods.original_price,
            stock: roundGoods.stock,
            sold_count: soldCount,
            total_stock: totalStock,
            percent: percent,
            limit_quantity: roundGoods.limit_quantity,
            start_time: round.start_time,
            end_time: round.end_time,
            status: round.status,
            time_left: timeLeft,
            is_active: round.status === 'active' && timeLeft > 0,
            can_buy: round.status === 'active' && roundGoods.stock > 0 && timeLeft > 0
          }
        };

        console.log('秒杀商品详情页数据:', result);
        return _this4.success(result);
      } catch (error) {
        console.error('获取秒杀商品详情页数据失败:', error);
        return _this4.fail('获取商品详情失败');
      }
    })();
  }

  /**
   * 获取首页秒杀数据（简化版本）
   * GET /api/seckill/home
   */
  homeAction() {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取首页秒杀数据 ===');

        const roundModel = _this5.model('flash_sale_rounds');
        const goodsModel = _this5.model('goods');

        // 获取当前进行中的轮次（限制数量）
        const rounds = yield roundModel.where({
          status: 'active'
        }).order('start_time ASC').limit(3).select();

        if (!rounds || rounds.length === 0) {
          return _this5.success({
            seckillTime: [],
            seckillTimeIndex: -1,
            list: []
          });
        }

        const seckillList = [];

        for (const round of rounds) {
          // 获取商品详细信息
          const goods = yield goodsModel.where({
            id: round.goods_id,
            is_delete: 0,
            is_on_sale: 1
          }).find();

          if (!think.isEmpty(goods)) {
            // 计算进度百分比
            const totalStock = round.stock + round.sold_count;
            const percent = totalStock > 0 ? Math.floor(round.sold_count / totalStock * 100) : 0;

            seckillList.push({
              id: round.id,
              goods_id: round.goods_id,
              title: round.goods_name || goods.name,
              image: round.goods_image || goods.list_pic_url,
              price: parseFloat(round.flash_price),
              ot_price: parseFloat(round.original_price),
              quota: round.stock,
              quota_show: totalStock,
              percent: percent,
              stock: round.stock,
              sales: round.sold_count,
              start_time: round.start_time,
              end_time: round.end_time,
              status: round.status,
              limit_quantity: round.limit_quantity
            });
          }
        }

        // 模拟时间段数据
        const result = {
          time: '10:00',
          stop: Math.floor(Date.now() / 1000) + 3600, // 1小时后结束
          list: seckillList
        };

        console.log(`返回 ${seckillList.length} 个首页秒杀商品`);
        return _this5.success(result);
      } catch (error) {
        console.error('获取首页秒杀数据失败:', error);
        return _this5.fail('获取数据失败');
      }
    })();
  }

  /**
   * 初始化测试数据
   * GET /api/seckill/init
   */
  initAction() {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 初始化秒杀测试数据 ===');

        const timeSlotModel = _this6.model('flash_sale_time_slots');
        const roundModel = _this6.model('flash_sale_rounds');
        const goodsModel = _this6.model('goods');
        const categoryModel = _this6.model('category');

        // 1. 插入商品分类
        const categories = [{ id: 1, name: '手机数码', parent_id: 0, sort_order: 1, is_show: 1 }, { id: 2, name: '电脑办公', parent_id: 0, sort_order: 2, is_show: 1 }];

        for (const category of categories) {
          const existingCategory = yield categoryModel.where({ id: category.id }).find();
          if (think.isEmpty(existingCategory)) {
            yield categoryModel.add(category);
          }
        }

        // 2. 插入测试商品
        const goods = [{
          id: 1,
          category_id: 1,
          is_on_sale: 1,
          name: 'iPhone 15 Pro Max 256GB',
          goods_number: 50,
          sell_volume: 128,
          retail_price: 8999.00,
          min_retail_price: 8999.00,
          cost_price: 7500.00,
          min_cost_price: 7500.00,
          goods_brief: '苹果最新旗舰手机，搭载A17 Pro芯片',
          goods_unit: '台',
          list_pic_url: '/images/goods/iphone15pro.jpg',
          add_time: Math.floor(Date.now() / 1000),
          is_delete: 0
        }, {
          id: 2,
          category_id: 1,
          is_on_sale: 1,
          name: '小米14 Ultra 512GB',
          goods_number: 30,
          sell_volume: 89,
          retail_price: 5999.00,
          min_retail_price: 5999.00,
          cost_price: 4500.00,
          min_cost_price: 4500.00,
          goods_brief: '小米年度旗舰，徕卡影像系统',
          goods_unit: '台',
          list_pic_url: '/images/goods/mi14ultra.jpg',
          add_time: Math.floor(Date.now() / 1000),
          is_delete: 0
        }, {
          id: 3,
          category_id: 2,
          is_on_sale: 1,
          name: 'MacBook Pro 14寸 M3',
          goods_number: 20,
          sell_volume: 45,
          retail_price: 15999.00,
          min_retail_price: 15999.00,
          cost_price: 12000.00,
          min_cost_price: 12000.00,
          goods_brief: '苹果专业级笔记本电脑，M3芯片',
          goods_unit: '台',
          list_pic_url: '/images/goods/macbookpro.jpg',
          add_time: Math.floor(Date.now() / 1000),
          is_delete: 0
        }];

        for (const goodsItem of goods) {
          const existingGoods = yield goodsModel.where({ id: goodsItem.id }).find();
          if (think.isEmpty(existingGoods)) {
            yield goodsModel.add(goodsItem);
          }
        }

        // 3. 插入秒杀时间段
        const timeSlots = [{ id: 1, name: '上午场', start_time: '10:00:00', end_time: '12:00:00', sort_order: 1, is_active: 1 }, { id: 2, name: '下午场', start_time: '14:00:00', end_time: '16:00:00', sort_order: 2, is_active: 1 }, { id: 3, name: '晚上场', start_time: '20:00:00', end_time: '22:00:00', sort_order: 3, is_active: 1 }];

        for (const timeSlot of timeSlots) {
          const existingTimeSlot = yield timeSlotModel.where({ id: timeSlot.id }).find();
          if (think.isEmpty(existingTimeSlot)) {
            yield timeSlotModel.add(timeSlot);
          }
        }

        // 4. 插入秒杀轮次
        const now = new Date();
        const rounds = [{
          id: 1,
          round_number: 1,
          goods_id: 1,
          goods_name: 'iPhone 15 Pro Max 256GB',
          goods_image: '/images/goods/iphone15pro.jpg',
          original_price: 8999.00,
          flash_price: 6999.00,
          stock: 10,
          sold_count: 3,
          limit_quantity: 1,
          start_time: now,
          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后
          status: 'active'
        }, {
          id: 2,
          round_number: 2,
          goods_id: 2,
          goods_name: '小米14 Ultra 512GB',
          goods_image: '/images/goods/mi14ultra.jpg',
          original_price: 5999.00,
          flash_price: 4999.00,
          stock: 15,
          sold_count: 8,
          limit_quantity: 2,
          start_time: now,
          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后
          status: 'active'
        }, {
          id: 3,
          round_number: 3,
          goods_id: 3,
          goods_name: 'MacBook Pro 14寸 M3',
          goods_image: '/images/goods/macbookpro.jpg',
          original_price: 15999.00,
          flash_price: 13999.00,
          stock: 5,
          sold_count: 2,
          limit_quantity: 1,
          start_time: now,
          end_time: new Date(now.getTime() + 2 * 60 * 60 * 1000), // 2小时后
          status: 'active'
        }];

        for (const round of rounds) {
          const existingRound = yield roundModel.where({ id: round.id }).find();
          if (think.isEmpty(existingRound)) {
            yield roundModel.add(round);
          }
        }

        return _this6.success({
          message: '测试数据初始化成功',
          data: {
            categories: categories.length,
            goods: goods.length,
            timeSlots: timeSlots.length,
            rounds: rounds.length
          }
        });
      } catch (error) {
        console.error('初始化测试数据失败:', error);
        return _this6.fail('初始化测试数据失败: ' + error.message);
      }
    })();
  }

  /**
   * 计算剩余时间
   */
  calculateTimeLeft(endTime) {
    const now = moment();
    const end = moment(endTime);
    const diff = end.diff(now);

    if (diff <= 0) {
      return { hours: 0, minutes: 0, seconds: 0 };
    }

    const duration = moment.duration(diff);
    return {
      hours: Math.floor(duration.asHours()),
      minutes: duration.minutes(),
      seconds: duration.seconds()
    };
  }

  /**
   * 临时测试API - 更新轮次状态
   * GET /api/seckill/updateRound?round_id=32
   */
  updateRoundAction() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        const roundId = _this7.get('round_id') || 32;
        const now = new Date();
        const startTime = new Date(now.getTime() - 10 * 60 * 1000); // 10分钟前开始
        const endTime = new Date(now.getTime() + 120 * 60 * 1000); // 2小时后结束

        const startTimeStr = startTime.toISOString().slice(0, 19).replace('T', ' ');
        const endTimeStr = endTime.toISOString().slice(0, 19).replace('T', ' ');

        console.log('更新轮次状态:', { roundId, startTimeStr, endTimeStr });

        yield _this7.model('flash_sale_rounds').where({ id: roundId }).update({
          start_time: startTimeStr,
          end_time: endTimeStr,
          status: 'active'
        });

        return _this7.success({
          message: '轮次状态更新成功',
          round_id: roundId,
          start_time: startTimeStr,
          end_time: endTimeStr,
          status: 'active'
        });
      } catch (error) {
        console.error('更新轮次状态失败:', error);
        return _this7.fail(500, '更新失败: ' + error.message);
      }
    })();
  }
};
//# sourceMappingURL=seckill.js.map