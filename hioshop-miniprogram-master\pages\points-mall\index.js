const util = require('../../utils/util.js');
const api = require('../../config/api.js');

Page({
  data: {
    // 页面数据
    goodsList: [],
    categories: [],
    config: {},
    
    // 筛选条件
    currentCategory: 0,
    currentSort: 'default',
    keyword: '',
    
    // 分页
    page: 1,
    limit: 20,
    total: 0,
    hasMore: true,
    
    // 状态
    loading: false,
    refreshing: false,
    
    // 排序选项
    sortOptions: [
      { key: 'default', name: '综合排序' },
      { key: 'price_asc', name: '积分升序' },
      { key: 'price_desc', name: '积分降序' },
      { key: 'sales', name: '销量优先' }
    ],
    
    // UI状态
    showSortMenu: false
  },

  onLoad: function (options) {
    console.log('积分商城页面加载');
    this.loadInitialData();
  },

  onShow: function () {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新');
    this.refreshData();
  },

  onReachBottom: function () {
    console.log('上拉加载更多');
    this.loadMoreData();
  },

  /**
   * 加载初始数据
   */
  loadInitialData: function() {
    console.log('加载积分商城初始数据');
    
    // 获取首页配置和分类
    util.request(api.PointsGoodsHome).then((res) => {
      console.log('积分商城首页数据:', res);
      
      if (res.errno === 0 && res.data) {
        this.setData({
          config: res.data.config,
          categories: [{ id: 0, name: '全部' }, ...res.data.categories]
        });
      }
      
      // 加载商品列表
      this.loadGoodsList();
    }).catch((error) => {
      console.error('获取积分商城首页数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    this.setData({
      page: 1,
      goodsList: [],
      hasMore: true,
      refreshing: true
    });
    
    this.loadGoodsList().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 加载更多数据
   */
  loadMoreData: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({
      page: this.data.page + 1
    });
    
    this.loadGoodsList();
  },

  /**
   * 加载商品列表
   */
  loadGoodsList: function() {
    if (this.data.loading) {
      return Promise.resolve();
    }
    
    this.setData({ loading: true });
    
    const params = {
      page: this.data.page,
      limit: this.data.limit,
      sort: this.data.currentSort
    };
    
    // 添加分类筛选
    if (this.data.currentCategory > 0) {
      params.category_id = this.data.currentCategory;
    }
    
    // 添加关键词搜索
    if (this.data.keyword) {
      params.keyword = this.data.keyword;
    }
    
    console.log('请求积分商品列表参数:', params);
    
    return util.request(api.PointsGoodsList, params).then((res) => {
      console.log('积分商品列表响应:', res);
      
      if (res.errno === 0 && res.data) {
        const newList = this.data.page === 1 ? res.data.list : [...this.data.goodsList, ...res.data.list];
        
        this.setData({
          goodsList: newList,
          total: res.data.total,
          hasMore: newList.length < res.data.total
        });
      } else {
        wx.showToast({
          title: res.errmsg || '加载失败',
          icon: 'none'
        });
      }
    }).catch((error) => {
      console.error('获取积分商品列表失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  /**
   * 分类切换
   */
  onCategoryChange: function(e) {
    const categoryId = parseInt(e.currentTarget.dataset.id);
    console.log('切换分类:', categoryId);
    
    if (categoryId === this.data.currentCategory) {
      return;
    }
    
    this.setData({
      currentCategory: categoryId,
      page: 1,
      goodsList: [],
      hasMore: true
    });
    
    this.loadGoodsList();
  },

  /**
   * 显示排序菜单
   */
  showSortMenu: function() {
    this.setData({
      showSortMenu: true
    });
  },

  /**
   * 隐藏排序菜单
   */
  hideSortMenu: function() {
    this.setData({
      showSortMenu: false
    });
  },

  /**
   * 排序切换
   */
  onSortChange: function(e) {
    const sortKey = e.currentTarget.dataset.key;
    console.log('切换排序:', sortKey);
    
    if (sortKey === this.data.currentSort) {
      this.hideSortMenu();
      return;
    }
    
    this.setData({
      currentSort: sortKey,
      showSortMenu: false,
      page: 1,
      goodsList: [],
      hasMore: true
    });
    
    this.loadGoodsList();
  },

  /**
   * 搜索输入
   */
  onSearchInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  onSearch: function() {
    console.log('搜索关键词:', this.data.keyword);
    
    this.setData({
      page: 1,
      goodsList: [],
      hasMore: true
    });
    
    this.loadGoodsList();
  },

  /**
   * 清空搜索
   */
  onClearSearch: function() {
    this.setData({
      keyword: '',
      page: 1,
      goodsList: [],
      hasMore: true
    });
    
    this.loadGoodsList();
  },

  /**
   * 跳转到商品详情
   */
  goGoodsDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('跳转到积分商品详情:', id);
    
    wx.navigateTo({
      url: `/pages/points-goods-detail/index?id=${id}`
    });
  },

  /**
   * 获取当前排序名称
   */
  getCurrentSortName: function() {
    const currentOption = this.data.sortOptions.find(option => option.key === this.data.currentSort);
    return currentOption ? currentOption.name : '综合排序';
  }
});
