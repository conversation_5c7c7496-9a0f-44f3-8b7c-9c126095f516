{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue?vue&type=template&id=52cb9d70&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Common\\Sidebar.vue", "mtime": 1754302177628}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}