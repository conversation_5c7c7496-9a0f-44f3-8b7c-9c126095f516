<template>
  <div class="flash-sale-multi-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>限时秒杀管理（多商品轮次）</h2>
      <button @click="openCreateModal" class="btn btn-primary">
        创建新轮次
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>总轮次</h3>
        <p class="stat-number">{{ statistics.totalRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>进行中</h3>
        <p class="stat-number active">{{ statistics.activeRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>即将开始</h3>
        <p class="stat-number upcoming">{{ statistics.upcomingRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>总销售额</h3>
        <p class="stat-number">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>
      </div>
    </div>

    <!-- 当前轮次 -->
    <div class="current-rounds" v-if="currentRounds.current && currentRounds.current.length > 0">
      <h3>当前进行中的轮次</h3>
      <div class="round-list">
        <div v-for="round in currentRounds.current" :key="round.id" class="round-item active">
          <div class="round-info">
            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>
            <p>商品数量: {{ round.goods_count }}</p>
            <p>剩余时间: {{ formatTime(round.countdown) }}</p>
          </div>
          <div class="goods-preview">
            <div v-for="goods in round.goods_list.slice(0, 3)" :key="goods.id" class="goods-item">
              <img :src="goods.goods_image" :alt="goods.goods_name" />
              <span>{{ goods.goods_name }}</span>
              <span class="price">¥{{ goods.flash_price }}</span>
            </div>
            <span v-if="round.goods_list.length > 3" class="more-goods">
              +{{ round.goods_list.length - 3 }}个商品
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 轮次列表 -->
    <div class="rounds-table">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>轮次列表</h3>
        <div class="table-controls">
          <div class="auto-refresh-control">
            <label class="form-check-label">
              <input
                type="checkbox"
                v-model="autoRefresh"
                @change="toggleAutoRefresh"
                class="form-check-input"
              >
              自动刷新 ({{ refreshInterval / 1000 }}秒)
            </label>
            <span v-if="autoRefresh" class="refresh-countdown">
              下次刷新: {{ refreshCountdown }}秒
            </span>
          </div>
          <button @click="refreshData" class="btn btn-secondary btn-sm">
            <i class="fa fa-refresh" :class="{ 'fa-spin': loading }"></i>
            手动刷新
          </button>
        </div>
      </div>

      <!-- 批量操作区域 -->
      <div v-if="selectedRounds.length > 0" class="batch-operations mb-3">
        <div class="alert alert-info">
          已选择 {{ selectedRounds.length }} 个轮次
          <div class="batch-actions">
            <button @click="batchCloseRounds" class="btn btn-warning btn-sm">批量关闭</button>
            <button @click="batchDeleteRounds" class="btn btn-danger btn-sm">批量删除</button>
            <button @click="clearSelection" class="btn btn-secondary btn-sm">取消选择</button>
          </div>
        </div>
      </div>

      <table class="table">
        <thead>
          <tr>
            <th>
              <input
                type="checkbox"
                @change="toggleSelectAll"
                :checked="isAllSelected"
                :indeterminate.prop="isIndeterminate"
              >
            </th>
            <th>轮次编号</th>
            <th>轮次名称</th>
            <th>商品数量</th>
            <th>总库存</th>
            <th>已售出</th>
            <th>开始时间</th>
            <th>结束时间</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="round in roundsList.data" :key="round.id">
            <td>
              <input
                type="checkbox"
                :value="round.id"
                v-model="selectedRounds"
                @change="updateSelection"
              >
            </td>
            <td>#{{ round.round_number }}</td>
            <td>{{ round.round_name }}</td>
            <td>{{ round.goods_count }}</td>
            <td>{{ round.total_stock }}</td>
            <td>{{ round.total_sold }}</td>
            <td>{{ formatDateTime(round.start_time) }}</td>
            <td>{{ formatDateTime(round.end_time) }}</td>
            <td>
              <div class="status-info">
                <span :class="getStatusClass(round.status)">{{ getStatusText(round.status) }}</span>
                <div class="time-info">
                  <small v-if="round.status === 'upcoming'">
                    开始时间: {{ formatTime(round.start_time) }}
                    <span v-if="getCountdown(round.start_time)" class="countdown">
                      ({{ getCountdown(round.start_time) }})
                    </span>
                  </small>
                  <small v-else-if="round.status === 'active'">
                    结束时间: {{ formatTime(round.end_time) }}
                    <span v-if="getCountdown(round.end_time)" class="countdown text-danger">
                      ({{ getCountdown(round.end_time) }})
                    </span>
                  </small>
                  <small v-else-if="round.status === 'ended'">
                    已于 {{ formatTime(round.end_time) }} 结束
                  </small>
                </div>
              </div>
            </td>
            <td>
              <div class="round-actions">
                <button @click="viewRoundDetails(round)" class="btn btn-info btn-sm">查看详情</button>

                <!-- 即将开始的轮次 -->
                <template v-if="round.status === 'upcoming'">
                  <button @click="editRound(round)" class="btn btn-success btn-sm">编辑</button>
                  <button @click="closeRound(round)" class="btn btn-warning btn-sm">取消轮次</button>
                  <button @click="deleteRound(round)" class="btn btn-danger btn-sm">删除</button>
                </template>

                <!-- 进行中的轮次 -->
                <template v-if="round.status === 'active'">
                  <button @click="showExtendModal(round)" class="btn btn-primary btn-sm">延期</button>
                  <button @click="closeRound(round)" class="btn btn-warning btn-sm">立即结束</button>
                  <button @click="deleteRound(round)" class="btn btn-danger btn-sm">删除</button>
                </template>

                <!-- 已结束的轮次 -->
                <template v-if="round.status === 'ended'">
                  <button @click="showRestartModal(round)" class="btn btn-success btn-sm">重新启动</button>
                  <button @click="showCopyModal(round)" class="btn btn-secondary btn-sm">复制轮次</button>
                  <button @click="deleteRound(round)" class="btn btn-danger btn-sm">删除</button>
                </template>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 延期轮次模态框 -->
    <div v-if="showExtendModalFlag" class="modal-overlay" @click="closeExtendModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>延期轮次</h4>
          <button @click="closeExtendModal" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <p>轮次: {{ extendRound && extendRound.round_name }}</p>
          <div class="form-group">
            <label>延期时间 (分钟):</label>
            <input
              type="number"
              v-model="extendMinutes"
              class="form-control"
              min="1"
              max="1440"
              placeholder="请输入延期分钟数"
            >
          </div>
        </div>
        <div class="modal-footer">
          <button @click="confirmExtend" class="btn btn-primary" :disabled="!extendMinutes">确认延期</button>
          <button @click="closeExtendModal" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>

    <!-- 重新启动轮次模态框 -->
    <div v-if="showRestartModalFlag" class="modal-overlay" @click="closeRestartModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>重新启动轮次</h4>
          <button @click="closeRestartModal" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <p>轮次: {{ restartRound && restartRound.round_name }}</p>
          <div class="form-group">
            <label>新开始时间:</label>
            <input
              type="datetime-local"
              v-model="newStartTime"
              class="form-control"
            >
          </div>
          <div class="form-group">
            <label>新结束时间:</label>
            <input
              type="datetime-local"
              v-model="newEndTime"
              class="form-control"
            >
          </div>
        </div>
        <div class="modal-footer">
          <button @click="confirmRestart" class="btn btn-primary">确认重启</button>
          <button @click="closeRestartModal" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>

    <!-- 复制轮次模态框 -->
    <div v-if="showCopyModalFlag" class="modal-overlay" @click="closeCopyModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>复制轮次</h4>
          <button @click="closeCopyModal" class="close-btn">&times;</button>
        </div>
        <div class="modal-body">
          <p>源轮次: {{ copyRound && copyRound.round_name }}</p>
          <div class="form-group">
            <label>新轮次名称:</label>
            <input
              type="text"
              v-model="newRoundName"
              class="form-control"
              :placeholder="copyRound && copyRound.round_name ? copyRound.round_name + ' (复制)' : ''"
            >
          </div>
          <div class="form-group">
            <label>开始时间:</label>
            <input
              type="datetime-local"
              v-model="copyStartTime"
              class="form-control"
            >
          </div>
          <div class="form-group">
            <label>结束时间:</label>
            <input
              type="datetime-local"
              v-model="copyEndTime"
              class="form-control"
            >
          </div>
        </div>
        <div class="modal-footer">
          <button @click="confirmCopy" class="btn btn-primary">确认复制</button>
          <button @click="closeCopyModal" class="btn btn-secondary">取消</button>
        </div>
      </div>
    </div>

    <!-- 创建轮次模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>创建新轮次</h3>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
          <!-- 整点秒杀时间设置 -->
          <div class="hourly-flash-settings">
            <div class="setting-header">
              <h4>整点秒杀设置</h4>
              <p class="setting-description">每小时整点开始，持续55分钟，24小时不间断</p>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>活动开始日期 *</label>
                <input
                  v-model="newRound.start_date"
                  type="date"
                  class="form-control"
                  required
                />
              </div>
              <div class="form-group">
                <label>活动结束日期 *</label>
                <input
                  v-model="newRound.end_date"
                  type="date"
                  class="form-control"
                  :min="newRound.start_date"
                  required
                />
              </div>
            </div>

            <!-- 自动生成的轮次名称预览 -->
            <div v-if="generatedRoundName" class="round-name-preview">
              <h5>轮次名称：</h5>
              <div class="name-display">{{ generatedRoundName }}</div>
            </div>

            <!-- 时段预览 -->
            <div v-if="hourlySlotPreview.length > 0" class="slot-preview">
              <h5>将生成以下秒杀时段：</h5>
              <div class="slot-list-container">
                <div class="slot-list">
                  <div v-for="(slot, index) in hourlySlotPreview" :key="index" class="slot-item">
                    <span class="slot-number">第{{ index + 1 }}场</span>
                    <span class="slot-time">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>
                    <span class="slot-duration">55分钟</span>
                    <span v-if="isSlotInPast(slot.start)" class="slot-status past">已过期</span>
                    <span v-else-if="isSlotActive(slot.start, slot.end)" class="slot-status active">进行中</span>
                    <span v-else class="slot-status upcoming">待开始</span>
                  </div>
                  <div v-if="hourlySlotPreview.length > 10" class="more-slots">
                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段
                  </div>
                </div>
              </div>
              <div class="slot-summary">
                <span>共 {{ hourlySlotPreview.length }} 个时段</span>
                <span class="valid-slots">（有效时段：{{ validSlotsCount }} 个）</span>
              </div>
            </div>
          </div>

          <!-- 商品选择 -->
          <div class="form-group">
            <label>选择商品 * (点击商品卡片进行选择)</label>

            <!-- 加载状态 -->
            <div v-if="loadingGoods" class="loading-state">
              <i class="loading-icon">⏳</i>
              <span>正在加载商品列表...</span>
            </div>

            <!-- 商品列表 -->
            <div v-else-if="goodsList && goodsList.length > 0" class="goods-selection-grid">
              <div
                v-for="goods in goodsList"
                :key="goods.id"
                class="goods-card"
                :class="{
                  'selected': isGoodsSelected(goods.id),
                  'disabled': !goods.can_select
                }"
                @click="toggleGoods(goods)"
              >
                <!-- 商品基本信息 -->
                <div class="goods-card-header">
                  <div class="goods-image-container">
                    <img :src="goods.list_pic_url" :alt="goods.name" class="goods-card-image" />
                    <div v-if="isGoodsSelected(goods.id)" class="selected-badge">
                      <i class="checkmark">✓</i>
                    </div>
                    <div v-if="!goods.can_select" class="disabled-overlay">
                      <span>已参与其他秒杀</span>
                    </div>
                  </div>
                  <div class="goods-card-info">
                    <h4 class="goods-name">{{ goods.name }}</h4>
                    <p class="original-price">原价: ¥{{ goods.retail_price }}</p>
                    <div v-if="!goods.can_select" class="warning-text">
                      <i class="warning-icon">⚠</i>
                      <span>已参与其他秒杀活动</span>
                    </div>
                  </div>
                </div>

                <!-- 秒杀设置面板 -->
                <div v-if="isGoodsSelected(goods.id)" class="goods-settings-panel">
                  <div class="settings-title">秒杀设置</div>
                  <div class="settings-grid">
                    <div class="setting-item full-width">
                      <label>折扣设置</label>
                      <div class="discount-setting">
                        <div class="discount-input-group">
                          <input
                            v-model.number="getSelectedGoods(goods.id).discount_rate"
                            type="number"
                            step="1"
                            min="10"
                            max="90"
                            class="discount-input"
                            @input="updateFlashPriceByDiscount(goods.id)"
                            @click.stop
                          />
                          <span class="discount-unit">% OFF</span>
                        </div>
                        <div class="price-preview">
                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →
                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}
                        </div>
                        <div class="price-range-hint" v-if="goods.price_range">
                          商品价格区间: {{ goods.price_range }}
                        </div>
                      </div>
                    </div>

                    <div class="setting-item">
                      <label>秒杀库存</label>
                      <input
                        v-model.number="getSelectedGoods(goods.id).stock"
                        type="number"
                        min="1"
                        max="9999"
                        class="stock-input"
                        @click.stop
                      />
                    </div>

                    <div class="setting-item">
                      <label>限购数量</label>
                      <input
                        v-model.number="getSelectedGoods(goods.id).limit_quantity"
                        type="number"
                        min="1"
                        max="99"
                        class="limit-input"
                        @click.stop
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态显示 -->
            <div v-else class="empty-state">
              <div class="empty-icon">📦</div>
              <p class="empty-text">暂无可选商品</p>
              <p class="empty-hint">请确保有上架的商品，且未参与其他秒杀活动</p>
              <button @click="loadGoodsList" class="btn btn-secondary btn-sm">重新加载</button>
            </div>

            <!-- 选择提示 -->
            <div class="selection-hint">
              <p v-if="newRound.goods_list.length === 0" class="hint-text">
                <i class="info-icon">ℹ</i>
                请点击商品卡片选择参与秒杀的商品，可以选择多个商品
              </p>
              <p v-else class="selected-count">
                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品
              </p>
            </div>
          </div>

          <!-- 已选商品汇总 -->
          <div v-if="newRound.goods_list.length > 0" class="selected-summary">
            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>
            <div class="summary-list">
              <div v-for="goods in newRound.goods_list" :key="goods.goods_id" class="summary-item">
                <span>{{ goods.goods_name }}</span>
                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>
                <span>库存: {{ goods.stock }}</span>
                <button @click="removeGoods(goods.goods_id)" class="remove-btn">移除</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeModal" class="btn btn-secondary">取消</button>
          <button
            @click="createRound"
            class="btn btn-primary"
            :disabled="!canCreateRound || isCreating"
          >
            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 轮次详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="showDetailModal = false">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>
          <button @click="showDetailModal = false" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
          <div class="round-details">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p>轮次编号: #{{ selectedRound.round_number }}</p>
              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>
              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>
              <p>状态: {{ getStatusText(selectedRound.status) }}</p>
            </div>
            
            <div class="detail-section">
              <h4>商品列表</h4>
              <table class="table">
                <thead>
                  <tr>
                    <th>商品</th>
                    <th>原价</th>
                    <th>秒杀价</th>
                    <th>折扣</th>
                    <th>库存</th>
                    <th>已售</th>
                    <th>限购</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="goods in selectedRound.goods_list" :key="goods.id">
                    <td>
                      <div class="goods-cell">
                        <img :src="goods.goods_image" :alt="goods.goods_name" />
                        <span>{{ goods.goods_name }}</span>
                      </div>
                    </td>
                    <td>¥{{ goods.original_price }}</td>
                    <td>¥{{ goods.flash_price }}</td>
                    <td>{{ goods.discount_rate }}%</td>
                    <td>{{ goods.stock }}</td>
                    <td>{{ goods.sold_count }}</td>
                    <td>{{ goods.limit_quantity }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlashSaleMultiPage',
  data() {
    return {
      statistics: {},
      currentRounds: { current: [], upcoming: [] },
      roundsList: { data: [], count: 0 },
      goodsList: [],
      loadingGoods: false,
      showAddModal: false,
      showDetailModal: false,
      selectedRound: {},
      isCreating: false,
      newRound: {
        start_date: '',
        end_date: '',
        goods_list: []
      },
      refreshTimer: null,
      creationProgress: {
        current: 0,
        total: 0
      },

      // 批量操作相关
      selectedRounds: [],

      // 自动刷新相关
      autoRefresh: false,
      refreshInterval: 30000, // 30秒
      refreshCountdown: 0,
      countdownTimer: null,
      loading: false,

      // 延期模态框
      showExtendModalFlag: false,
      extendRound: null,
      extendMinutes: null,

      // 重启模态框
      showRestartModalFlag: false,
      restartRound: null,
      newStartTime: '',
      newEndTime: '',

      // 复制模态框
      showCopyModalFlag: false,
      copyRound: null,
      newRoundName: '',
      copyStartTime: '',
      copyEndTime: ''
    };
  },
  
  computed: {
    canCreateRound() {
      const hasStartDate = this.newRound.start_date;
      const hasEndDate = this.newRound.end_date;
      const hasGoods = this.newRound.goods_list.length > 0;
      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);
      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);

      console.log('canCreateRound检查:', {
        hasStartDate,
        hasEndDate,
        hasGoods,
        goodsValid,
        dateValid,
        goodsList: this.newRound.goods_list
      });

      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;
    },

    // 批量选择相关计算属性
    isAllSelected() {
      return this.roundsList.data.length > 0 && this.selectedRounds.length === this.roundsList.data.length;
    },

    isIndeterminate() {
      return this.selectedRounds.length > 0 && this.selectedRounds.length < this.roundsList.data.length;
    },

    // 自动生成轮次名称
    generatedRoundName() {
      if (!this.newRound.start_date || !this.newRound.end_date) {
        return '';
      }

      const startDate = new Date(this.newRound.start_date);
      const endDate = new Date(this.newRound.end_date);

      if (startDate.getTime() === endDate.getTime()) {
        // 单日活动
        const dateStr = startDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        return `${dateStr}整点秒杀`;
      } else {
        // 多日活动
        const startStr = startDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        const endStr = endDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        return `${startStr}至${endStr}整点秒杀`;
      }
    },

    // 整点秒杀时段预览（24小时全天候）
    hourlySlotPreview() {
      if (!this.newRound.start_date || !this.newRound.end_date) {
        return [];
      }

      const slots = [];
      const startDate = new Date(this.newRound.start_date);
      const endDate = new Date(this.newRound.end_date);

      // 设置结束日期为当天的23:59:59
      endDate.setHours(23, 59, 59, 999);

      let currentDate = new Date(startDate);
      currentDate.setHours(0, 0, 0, 0); // 从00:00开始

      while (currentDate <= endDate) {
        for (let hour = 0; hour < 24; hour++) {
          const slotStart = new Date(currentDate);
          slotStart.setHours(hour, 0, 0, 0);

          const slotEnd = new Date(currentDate);
          slotEnd.setHours(hour, 55, 0, 0);

          // 检查轮次开始时间是否超出结束日期
          if (slotStart > endDate) {
            break;
          }

          slots.push({
            start: this.formatLocalDateTime(slotStart),
            end: this.formatLocalDateTime(slotEnd),
            startTime: slotStart,
            endTime: slotEnd
          });
        }

        // 移动到下一天
        currentDate.setDate(currentDate.getDate() + 1);
      }
      return slots;
    },

    // 有效时段数量（未过期的时段）
    validSlotsCount() {
      const now = new Date();
      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;
    }
  },

  mounted() {
    console.log('FlashSaleMultiPage组件已挂载');
    console.log('初始showAddModal值:', this.showAddModal);
    this.loadData();
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },

  methods: {
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadCurrentRounds(),
        this.loadRoundsList(),
        this.loadGoodsList()
      ]);
    },

    async loadStatistics() {
      try {
        const response = await this.axios.get('flashsalemulti/statistics');
        if (response.data.errno === 0) {
          this.statistics = response.data.data;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    async loadCurrentRounds() {
      try {
        const response = await this.axios.get('flashsalemulti/current');
        if (response.data.errno === 0) {
          this.currentRounds = response.data.data;
        }
      } catch (error) {
        console.error('加载当前轮次失败:', error);
      }
    },

    async loadRoundsList() {
      try {
        const response = await this.axios.get('flashsalemulti/list');
        if (response.data.errno === 0) {
          this.roundsList = response.data.data;
        }
      } catch (error) {
        console.error('加载轮次列表失败:', error);
      }
    },

    async loadGoodsList() {
      try {
        this.loadingGoods = true;
        console.log('开始加载商品列表...');

        const response = await this.axios.get('flashsalemulti/goods');
        console.log('商品列表API响应:', response.data);

        if (response.data.errno === 0) {
          this.goodsList = response.data.data || [];
          console.log('商品列表加载成功，数量:', this.goodsList.length);

          if (this.goodsList.length === 0) {
            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');
          }
        } else {
          console.error('API返回错误:', response.data.errmsg);
          this.$message.error(response.data.errmsg || '加载商品列表失败');
          this.goodsList = [];
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
        this.$message.error('网络错误，请检查服务器连接');
        this.goodsList = [];
      } finally {
        this.loadingGoods = false;
      }
    },

    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadCurrentRounds();
        this.loadStatistics();
      }, 30000); // 30秒刷新一次
    },

    isGoodsSelected(goodsId) {
      return this.newRound.goods_list.some(g => g.goods_id === goodsId);
    },

    getSelectedGoods(goodsId) {
      return this.newRound.goods_list.find(g => g.goods_id === goodsId);
    },

    toggleGoods(goods) {
      if (!goods.can_select) return;

      if (this.isGoodsSelected(goods.id)) {
        this.removeGoods(goods.id);
      } else {
        const originalPrice = parseFloat(goods.retail_price) || 0;
        const defaultDiscount = 20; // 默认20%折扣
        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;

        this.newRound.goods_list.push({
          goods_id: goods.id,
          goods_name: goods.name,
          original_price: originalPrice,
          flash_price: flashPrice,
          discount_rate: defaultDiscount,
          stock: 100,
          limit_quantity: 1
        });
      }
    },

    removeGoods(goodsId) {
      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);
      if (index > -1) {
        this.newRound.goods_list.splice(index, 1);
      }
    },

    calculateDiscountRate(originalPrice, flashPrice) {
      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;
      const rate = Math.round((1 - flashPrice / originalPrice) * 100);
      return isNaN(rate) ? 0 : rate;
    },

    updateFlashPriceByDiscount(goodsId) {
      const selectedGoods = this.getSelectedGoods(goodsId);
      if (selectedGoods && selectedGoods.original_price > 0) {
        const discountRate = selectedGoods.discount_rate || 0;
        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;
        selectedGoods.flash_price = flashPrice;
      }
    },

    // 格式化本地日期时间为字符串（避免时区问题）
    formatLocalDateTime(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 格式化本地日期为字符串（避免时区问题）
    formatLocalDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    getCurrentDateTime() {
      const now = new Date();
      return this.formatLocalDateTime(now).slice(0, 16).replace(' ', 'T');
    },

    getCurrentDate() {
      const now = new Date();
      return this.formatLocalDate(now);
    },

    openCreateModal() {
      console.log('点击创建新轮次按钮');
      // 设置默认日期为今天
      const today = this.getCurrentDate();
      this.newRound.start_date = today;
      this.newRound.end_date = today;
      this.showAddModal = true;
      console.log('showAddModal设置为:', this.showAddModal);
    },

    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatSlotTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    isSlotInPast(startTime) {
      const now = new Date();
      const slotStart = new Date(startTime);
      return slotStart < now;
    },

    isSlotActive(startTime, endTime) {
      const now = new Date();
      const slotStart = new Date(startTime);
      const slotEnd = new Date(endTime);
      return now >= slotStart && now <= slotEnd;
    },

    async createRound() {
      if (!this.canCreateRound) {
        this.$message.error('请完善轮次信息');
        return;
      }

      // 生成整点秒杀时段数据
      const hourlySlots = this.hourlySlotPreview;
      const now = new Date();

      // 过滤掉已完全结束的时段，保留当前正在进行的和未来的时段
      const validSlots = hourlySlots.filter(slot => new Date(slot.end) > now);

      if (validSlots.length === 0) {
        this.$message.error('所选时间段内没有有效的秒杀时段');
        return;
      }

      // 如果轮次数量过多，询问用户确认
      if (validSlots.length > 50) {
        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);
        if (!confirmed) {
          return;
        }
      }

      try {
        this.isCreating = true;
        this.creationProgress.current = 0;
        this.creationProgress.total = validSlots.length;

        let createdCount = 0;
        let failedCount = 0;
        const failedReasons = [];

        // 批量处理，每次处理10个轮次
        const batchSize = 10;
        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {
          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);
          const batch = validSlots.slice(batchStart, batchEnd);

          // 并行创建当前批次的轮次
          const batchPromises = batch.map(async (slot, batchIndex) => {
            const globalIndex = batchStart + batchIndex;

            // 重试逻辑
            const maxRetries = 3;
            let lastError = null;

            for (let retry = 0; retry < maxRetries; retry++) {
              try {
                const roundData = {
                  round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,
                  start_time: slot.start,
                  end_time: slot.end,
                  is_hourly_flash: true,
                  slot_index: globalIndex + 1,
                  total_slots: validSlots.length,
                  goods_list: this.newRound.goods_list.map(goods => ({
                    goods_id: goods.goods_id,
                    goods_name: goods.goods_name,
                    goods_image: goods.goods_image,
                    original_price: goods.original_price,
                    flash_price: goods.flash_price,
                    stock: goods.stock,
                    discount_rate: goods.discount_rate
                  }))
                };

                const response = await this.axios.post('flashsalemulti/create', roundData);

                if (response.data.errno === 0) {
                  return { success: true, index: globalIndex + 1 };
                } else {
                  lastError = response.data.errmsg;
                  // 如果是重复键错误，等待一段时间后重试
                  if (lastError.includes('Duplicate entry') && retry < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500 * (retry + 1)));
                    continue;
                  }
                  return { success: false, index: globalIndex + 1, error: lastError };
                }
              } catch (error) {
                lastError = error.message;
                // 如果是网络错误或服务器错误，等待后重试
                if (retry < maxRetries - 1) {
                  await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
                  continue;
                }
              }
            }

            return { success: false, index: globalIndex + 1, error: `创建失败 (已重试${maxRetries}次): ${lastError}` };
          });

          // 等待当前批次完成
          const batchResults = await Promise.all(batchPromises);

          // 统计结果
          batchResults.forEach(result => {
            this.creationProgress.current++;
            if (result.success) {
              createdCount++;
            } else {
              failedCount++;
              if (failedReasons.length < 5) { // 只记录前5个错误
                failedReasons.push(`第${result.index}场: ${result.error}`);
              }
            }
          });

          // 短暂延迟，避免服务器压力过大
          if (batchEnd < validSlots.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }

        // 显示结果
        if (createdCount > 0) {
          let message = `成功创建${createdCount}个整点秒杀轮次`;
          if (failedCount > 0) {
            message += `，${failedCount}个失败`;
            if (failedReasons.length > 0) {
              console.warn('创建失败的轮次:', failedReasons);
              message += `\n主要错误: ${failedReasons[0]}`;
            }
          }
          this.$message.success(message);
          this.closeModal();
          this.loadData();
        } else {
          let errorMessage = '所有轮次创建失败';
          if (failedReasons.length > 0) {
            errorMessage += `\n错误信息: ${failedReasons[0]}`;
          }
          this.$message.error(errorMessage);
        }

      } catch (error) {
        console.error('创建整点秒杀轮次失败:', error);
        this.$message.error('创建过程中发生错误: ' + error.message);
      } finally {
        this.isCreating = false;
        this.creationProgress.current = 0;
        this.creationProgress.total = 0;
      }
    },

    closeModal() {
      this.showAddModal = false;
      this.newRound = {
        start_date: '',
        end_date: '',
        goods_list: []
      };
    },

    viewRoundDetails(round) {
      this.selectedRound = round;
      this.showDetailModal = true;
    },

    async closeRound(round) {
      if (!confirm(`确定要关闭轮次"${round.round_name}"吗？关闭后轮次将立即结束。`)) {
        return;
      }

      try {
        this.$message.info('正在关闭轮次...');
        const response = await this.axios.post('flashsalemulti/close', {
          round_id: round.id
        });

        if (response.data.errno === 0) {
          this.$message.success('轮次已关闭');
          this.loadData(); // 重新加载数据
        } else {
          this.$message.error(response.data.errmsg || '关闭失败');
        }
      } catch (error) {
        console.error('关闭轮次失败:', error);
        this.$message.error('关闭失败');
      }
    },



    formatTime(seconds) {
      if (!seconds || seconds <= 0) return '00:00:00';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    getStatusText(status) {
      const statusMap = {
        'upcoming': '即将开始',
        'active': '进行中',
        'ended': '已结束'
      };
      return statusMap[status] || status;
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'upcoming': 'badge badge-warning',
        'active': 'badge badge-success',
        'ended': 'badge badge-secondary'
      };
      return classMap[status] || 'badge badge-light';
    },

    // 获取倒计时
    getCountdown(timeStr) {
      if (!timeStr) return '';
      const targetTime = new Date(timeStr);
      const now = new Date();
      const diff = targetTime - now;

      if (diff <= 0) return '';

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${seconds}秒`;
      } else {
        return `${seconds}秒`;
      }
    },

    // 自动刷新相关方法
    toggleAutoRefresh() {
      if (this.autoRefresh) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },

    startAutoRefresh() {
      this.stopAutoRefresh(); // 先停止之前的定时器

      this.refreshTimer = setInterval(() => {
        this.refreshData();
      }, this.refreshInterval);

      // 启动倒计时
      this.refreshCountdown = this.refreshInterval / 1000;
      this.countdownTimer = setInterval(() => {
        this.refreshCountdown--;
        if (this.refreshCountdown <= 0) {
          this.refreshCountdown = this.refreshInterval / 1000;
        }
      }, 1000);
    },

    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      this.refreshCountdown = 0;
    },

    async refreshData() {
      this.loading = true;
      try {
        await this.loadData();
      } catch (error) {
        console.error('刷新数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 批量操作相关方法
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedRounds = [];
      } else {
        this.selectedRounds = this.roundsList.data.map(round => round.id);
      }
    },

    updateSelection() {
      // 这个方法会在复选框状态改变时自动调用
    },

    clearSelection() {
      this.selectedRounds = [];
    },

    async batchCloseRounds() {
      if (this.selectedRounds.length === 0) {
        this.$message.warning('请先选择要关闭的轮次');
        return;
      }

      try {
        const confirmResult = await this.$confirm(
          `确定要关闭选中的 ${this.selectedRounds.length} 个轮次吗？`,
          '批量关闭确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        if (confirmResult) {
          let successCount = 0;
          let failCount = 0;

          for (const roundId of this.selectedRounds) {
            try {
              const response = await this.$http.post('/admin/flashsalemulti/close', {
                round_id: roundId
              });

              if (response.data.errno === 0) {
                successCount++;
              } else {
                failCount++;
              }
            } catch (error) {
              failCount++;
            }
          }

          this.$message.success(`批量关闭完成：成功 ${successCount} 个，失败 ${failCount} 个`);
          this.clearSelection();
          this.loadData();
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量关闭失败:', error);
          this.$message.error('批量关闭失败');
        }
      }
    },

    async batchDeleteRounds() {
      if (this.selectedRounds.length === 0) {
        this.$message.warning('请先选择要删除的轮次');
        return;
      }

      try {
        const confirmResult = await this.$confirm(
          `确定要删除选中的 ${this.selectedRounds.length} 个轮次吗？此操作不可恢复！\n\n注意：进行中或即将开始的轮次将先被关闭，然后删除。`,
          '批量删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        if (confirmResult) {
          this.$message.info('正在批量删除轮次...');

          const response = await this.$http.post('flashsalemulti/batchDelete', {
            round_ids: this.selectedRounds
          });

          if (response.data.errno === 0) {
            this.$message.success(response.data.errmsg || '批量删除完成');
            this.clearSelection();
            this.loadData();
          } else {
            this.$message.error(response.data.errmsg || '批量删除失败');
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error);
          this.$message.error('批量删除失败');
        }
      }
    },

    // 延期轮次相关方法
    showExtendModal(round) {
      this.extendRound = round;
      this.extendMinutes = null;
      this.showExtendModalFlag = true;
    },

    closeExtendModal() {
      this.showExtendModalFlag = false;
      this.extendRound = null;
      this.extendMinutes = null;
    },

    async confirmExtend() {
      if (!this.extendMinutes || this.extendMinutes <= 0) {
        this.$message.warning('请输入有效的延期时间');
        return;
      }

      try {
        const response = await this.$http.post('/admin/flashsalemulti/extend', {
          round_id: this.extendRound.id,
          extend_minutes: this.extendMinutes
        });

        if (response.data.errno === 0) {
          this.$message.success(`轮次已延期${this.extendMinutes}分钟`);
          this.closeExtendModal();
          this.loadData();
        } else {
          this.$message.error(response.data.errmsg || '延期失败');
        }
      } catch (error) {
        console.error('延期轮次失败:', error);
        this.$message.error('延期失败');
      }
    },

    // 重启轮次相关方法
    showRestartModal(round) {
      this.restartRound = round;
      // 设置默认时间为当前时间后1小时和2小时
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);

      this.newStartTime = this.formatDateTimeLocal(oneHourLater);
      this.newEndTime = this.formatDateTimeLocal(twoHoursLater);
      this.showRestartModalFlag = true;
    },

    closeRestartModal() {
      this.showRestartModalFlag = false;
      this.restartRound = null;
      this.newStartTime = '';
      this.newEndTime = '';
    },

    async confirmRestart() {
      if (!this.newStartTime || !this.newEndTime) {
        this.$message.warning('请设置开始和结束时间');
        return;
      }

      if (new Date(this.newStartTime) >= new Date(this.newEndTime)) {
        this.$message.warning('开始时间必须早于结束时间');
        return;
      }

      try {
        const response = await this.$http.post('/admin/flashsalemulti/restart', {
          round_id: this.restartRound.id,
          new_start_time: this.newStartTime,
          new_end_time: this.newEndTime
        });

        if (response.data.errno === 0) {
          this.$message.success('轮次已重新启动');
          this.closeRestartModal();
          this.loadData();
        } else {
          this.$message.error(response.data.errmsg || '重启失败');
        }
      } catch (error) {
        console.error('重启轮次失败:', error);
        this.$message.error('重启失败');
      }
    },

    // 复制轮次相关方法
    showCopyModal(round) {
      this.copyRound = round;
      this.newRoundName = round.round_name + ' (复制)';

      // 设置默认时间为当前时间后1小时和2小时
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      const twoHoursLater = new Date(now.getTime() + 2 * 60 * 60 * 1000);

      this.copyStartTime = this.formatDateTimeLocal(oneHourLater);
      this.copyEndTime = this.formatDateTimeLocal(twoHoursLater);
      this.showCopyModalFlag = true;
    },

    closeCopyModal() {
      this.showCopyModalFlag = false;
      this.copyRound = null;
      this.newRoundName = '';
      this.copyStartTime = '';
      this.copyEndTime = '';
    },

    async confirmCopy() {
      if (!this.copyStartTime || !this.copyEndTime) {
        this.$message.warning('请设置开始和结束时间');
        return;
      }

      if (new Date(this.copyStartTime) >= new Date(this.copyEndTime)) {
        this.$message.warning('开始时间必须早于结束时间');
        return;
      }

      try {
        const response = await this.$http.post('/admin/flashsalemulti/copy', {
          round_id: this.copyRound.id,
          new_round_name: this.newRoundName,
          new_start_time: this.copyStartTime,
          new_end_time: this.copyEndTime
        });

        if (response.data.errno === 0) {
          this.$message.success('轮次复制成功');
          this.closeCopyModal();
          this.loadData();
        } else {
          this.$message.error(response.data.errmsg || '复制失败');
        }
      } catch (error) {
        console.error('复制轮次失败:', error);
        this.$message.error('复制失败');
      }
    },

    // 删除轮次
    async deleteRound(round) {
      try {
        const statusText = {
          'upcoming': '即将开始',
          'active': '进行中',
          'ended': '已结束'
        };

        let confirmMessage = `确定要删除轮次"${round.round_name}"吗？此操作不可恢复！`;

        if (round.status === 'active' || round.status === 'upcoming') {
          confirmMessage += `\n\n注意：该轮次当前状态为"${statusText[round.status]}"，删除前将先自动关闭轮次。`;
        }

        const confirmResult = await this.$confirm(
          confirmMessage,
          '删除轮次确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        if (confirmResult) {
          this.$message.info('正在删除轮次...');

          const response = await this.$http.post('flashsalemulti/delete', {
            round_id: round.id
          });

          if (response.data.errno === 0) {
            this.$message.success('轮次删除成功');
            this.loadData();
          } else {
            this.$message.error(response.data.errmsg || '删除失败');
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除轮次失败:', error);
          this.$message.error('删除失败');
        }
      }
    },

    // 编辑轮次
    editRound(round) {
      // 这里可以跳转到编辑页面或打开编辑模态框
      this.$message.info('编辑功能待实现');
    },

    // 格式化日期时间为本地格式（用于datetime-local输入框）
    formatDateTimeLocal(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    }
  }
};
</script>

<style scoped>
.flash-sale-multi-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* 表格控制区域 */
.table-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.auto-refresh-control {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.refresh-countdown {
  color: #666;
  font-size: 12px;
}

/* 批量操作区域 */
.batch-operations {
  margin-bottom: 15px;
}

.batch-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

/* 状态信息显示 */
.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.time-info {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.countdown {
  font-weight: bold;
  color: #f56c6c;
}

/* 操作按钮组 */
.round-actions {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.round-actions .btn {
  margin-bottom: 2px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.round-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.round-actions .btn {
  margin: 0;
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* 整点秒杀设置样式 */
.hourly-flash-settings {
  margin-bottom: 20px;
}

.setting-header {
  margin-bottom: 15px;
}

.setting-header h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.setting-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-separator {
  color: #666;
  font-weight: bold;
}

.slot-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.slot-preview h5 {
  margin: 0 0 10px 0;
  color: #333;
}

.slot-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slot-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.slot-number {
  font-weight: bold;
  color: #007bff;
  min-width: 60px;
}

.slot-time {
  flex: 1;
  color: #333;
}

.slot-duration {
  color: #28a745;
  font-size: 12px;
  font-weight: bold;
}

.slot-summary {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #dee2e6;
  text-align: center;
  color: #666;
  font-weight: bold;
}

.valid-slots {
  color: #28a745;
  margin-left: 10px;
}

.round-name-preview {
  margin-bottom: 20px;
}

.round-name-preview h5 {
  margin-bottom: 8px;
  color: #333;
  font-weight: bold;
}

.name-display {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #007bff;
}

.slot-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.slot-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.slot-status.past {
  background-color: #f8d7da;
  color: #721c24;
}

.slot-status.active {
  background-color: #d4edda;
  color: #155724;
}

.slot-status.upcoming {
  background-color: #d1ecf1;
  color: #0c5460;
}

.more-slots {
  padding: 10px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.stat-number.active {
  color: #28a745;
}

.stat-number.upcoming {
  color: #ffc107;
}

/* 当前轮次 */
.current-rounds {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.round-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.round-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
}

.round-item.active {
  border-color: #28a745;
  background-color: #f8fff9;
}

.round-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.round-info p {
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

.goods-preview {
  display: flex;
  align-items: center;
  gap: 10px;
}

.goods-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 80px;
}

.goods-item img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 4px;
}

.goods-item span {
  font-size: 12px;
  color: #666;
}

.goods-item .price {
  color: #e74c3c;
  font-weight: bold;
}

.more-goods {
  color: #007bff;
  font-size: 12px;
}

/* 表格 */
.rounds-table {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status-upcoming {
  color: #ffc107;
  font-weight: bold;
}

.status-active {
  color: #28a745;
  font-weight: bold;
}

.status-ended {
  color: #6c757d;
  font-weight: bold;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 1000px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

/* 表单 */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control.small {
  width: 80px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 商品选择网格 */
.goods-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
}

/* 商品卡片 */
.goods-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.goods-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.goods-card.selected {
  border-color: #28a745;
  background-color: #f8fff9;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.goods-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
  border-color: #dee2e6;
}

.goods-card.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: #dee2e6;
}

/* 商品卡片头部 */
.goods-card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.goods-image-container {
  position: relative;
  margin-right: 15px;
  flex-shrink: 0;
}

.goods-card-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.selected-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background-color: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 商品信息 */
.goods-card-info {
  flex: 1;
}

.goods-name {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.original-price {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 14px;
}

.warning-text {
  display: flex;
  align-items: center;
  color: #dc3545;
  font-size: 12px;
  font-weight: bold;
}

.warning-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 商品设置面板 */
.goods-settings-panel {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
  margin-top: 15px;
}

.settings-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.settings-title::before {
  content: "⚙";
  margin-right: 6px;
  color: #007bff;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.setting-item {
  display: flex;
  flex-direction: column;
}

.setting-item.full-width {
  grid-column: 1 / -1;
}

.setting-item label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 价格输入组 */
.price-input-group {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.currency {
  background-color: #f8f9fa;
  padding: 6px 8px;
  border-right: 1px solid #ddd;
  font-size: 14px;
  color: #666;
}

.price-input {
  border: none;
  padding: 6px 8px;
  font-size: 14px;
  flex: 1;
  outline: none;
}

.price-input:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.discount-display {
  margin-top: 4px;
  font-size: 12px;
  color: #e74c3c;
  font-weight: bold;
  text-align: center;
  background-color: #fff5f5;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #fecaca;
}

/* 库存和限购输入 */
.stock-input,
.limit-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 14px;
  outline: none;
  background: white;
}

.stock-input:focus,
.limit-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 折扣设置样式 */
.discount-setting {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.discount-input-group {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.discount-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}

.discount-unit {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #dc3545;
}

.price-preview {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.price-range-hint {
  font-size: 12px;
  color: #28a745;
  font-style: italic;
}

/* 选择提示 */
.selection-hint {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.hint-text {
  margin: 0;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.info-icon {
  margin-right: 8px;
  color: #007bff;
  font-size: 16px;
}

.selected-count {
  margin: 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.selected-count strong {
  color: #28a745;
  font-size: 16px;
}

/* 已选商品汇总 */
.selected-summary {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.selected-summary h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.summary-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.remove-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* 轮次详情 */
.round-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.detail-section p {
  margin: 5px 0;
  color: #666;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  font-size: 16px;
}

.loading-state .loading-icon {
  margin-right: 10px;
  font-size: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  text-align: center;
}

.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state .empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #495057;
  margin: 0 0 8px 0;
}

.empty-state .empty-hint {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.empty-state .btn-sm {
  padding: 6px 16px;
  font-size: 14px;
}

.goods-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.goods-cell img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.goods-cell span {
  font-size: 14px;
}

/* 状态徽章样式 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 4px;
  text-align: center;
  white-space: nowrap;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.badge-light {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

/* 模态框样式增强 */
.modal-content.small {
  max-width: 500px;
}

.modal-header h4 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

/* 表单控件增强 */
.form-control[type="number"] {
  text-align: center;
}

.form-control[type="datetime-local"] {
  font-family: monospace;
}

/* 按钮样式增强 */
.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}
</style>
</style>
