{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\router\\index.js", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\router\\index.js", "mtime": 1754302188318}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Router", "use", "constantRoutes", "path", "name", "component", "redirect", "children", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "sources": ["D:/py-ide/hioshop-miniprogram-master/web/src/router/index.js"], "sourcesContent": ["import Vue from \"vue\";\nimport Router from \"vue-router\";\n\nVue.use(Router);\n\n/* Layout */\n\n/**\n * Note: sub-menu only appear when route children.length >= 1\n * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html\n *\n * hidden: true                   if set true, item will not show in the sidebar(default is false)\n * alwaysShow: true               if set true, will always show the root menu\n *                                if not set alwaysShow, when item has more than one children route,\n *                                it will becomes nested mode, otherwise not show the root menu\n * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb\n * name:'router-name'             the name is used by <keep-alive> (must set!!!)\n * meta : {\n    roles: ['admin','editor']    control the page roles (you can set multiple roles)\n    title: 'title'               the name show in sidebar and breadcrumb (recommend set)\n    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar\n    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)\n    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set\n  }\n */\n\n/**\n * constantRoutes\n * a base page that does not have permission () =>importments\n * all roles can be accessed\n */\n// export const constantRoutes = [\n//   {\n//     path: '/login',\n//     component: () => import('@/views/login/index'),\n//     hidden: true\n//   },\n\n//   {\n//     path: '/404',\n//     component: () => import('@/views/404'),\n//     hidden: true\n//   },\n\n//   {\n//     path: '/',\n//     component: Layout,\n//     redirect: '/dashboard',\n//     children: [{\n//       path: 'dashboard',\n//       name: 'Dashboard',\n//       component: () => import('@/views/dashboard/index'),\n//       meta: { title: 'Dashboard', icon: 'dashboard' }\n//     }]\n//   },\n\n//   {\n//     path: '/example',\n//     component: Layout,\n//     redirect: '/example/table',\n//     name: 'Example',\n//     meta: { title: 'Example', icon: 'el-icon-s-help' },\n//     children: [\n//       {\n//         path: 'table',\n//         name: 'Table',\n//         component: () => import('@/views/table/index'),\n//         meta: { title: 'Table', icon: 'table' }\n//       },\n//       {\n//         path: 'tree',\n//         name: 'Tree',\n//         component: () => import('@/views/tree/index'),\n//         meta: { title: 'Tree', icon: 'tree' }\n//       }\n//     ]\n//   },\n\n//   {\n//     path: '/form',\n//     component: Layout,\n//     children: [\n//       {\n//         path: 'index',\n//         name: 'Form',\n//         component: () => import('@/views/form/index'),\n//         meta: { title: 'Form', icon: 'form' }\n//       }\n//     ]\n//   },\n\n//   {\n//     path: '/nested',\n//     component: Layout,\n//     redirect: '/nested/menu1',\n//     name: 'Nested',\n//     meta: {\n//       title: 'Nested',\n//       icon: 'nested'\n//     },\n//     children: [\n//       {\n//         path: 'menu1',\n//         component: () => import('@/views/nested/menu1/index'), // Parent router-view\n//         name: 'Menu1',\n//         meta: { title: 'Menu1' },\n//         children: [\n//           {\n//             path: 'menu1-1',\n//             component: () => import('@/views/nested/menu1/menu1-1'),\n//             name: 'Menu1-1',\n//             meta: { title: 'Menu1-1' }\n//           },\n//           {\n//             path: 'menu1-2',\n//             component: () => import('@/views/nested/menu1/menu1-2'),\n//             name: 'Menu1-2',\n//             meta: { title: 'Menu1-2' },\n//             children: [\n//               {\n//                 path: 'menu1-2-1',\n//                 component: () => import('@/views/nested/menu1/menu1-2/menu1-2-1'),\n//                 name: 'Menu1-2-1',\n//                 meta: { title: 'Menu1-2-1' }\n//               },\n//               {\n//                 path: 'menu1-2-2',\n//                 component: () => import('@/views/nested/menu1/menu1-2/menu1-2-2'),\n//                 name: 'Menu1-2-2',\n//                 meta: { title: 'Menu1-2-2' }\n//               }\n//             ]\n//           },\n//           {\n//             path: 'menu1-3',\n//             component: () => import('@/views/nested/menu1/menu1-3'),\n//             name: 'Menu1-3',\n//             meta: { title: 'Menu1-3' }\n//           }\n//         ]\n//       },\n//       {\n//         path: 'menu2',\n//         component: () => import('@/views/nested/menu2/index'),\n//         name: 'Menu2',\n//         meta: { title: 'menu2' }\n//       }\n//     ]\n//   },\n\n//   {\n//     path: 'external-link',\n//     component: Layout,\n//     children: [\n//       {\n//         path: 'https://panjiachen.github.io/vue-element-admin-site/#/',\n//         meta: { title: 'External Link', icon: 'link' }\n//       }\n//     ]\n//   },\n\n//   // 404 page must be placed at the end !!!\n//   { path: '*', redirect: '/404', hidden: true }\n// ]\n\nexport const constantRoutes = [\n  {\n    path: \"/dashboard\",\n    name: \"dashboard\",\n    component: () => import(\"@/components/DashboardPage\"),\n    redirect: \"/dashboard/welcome\",\n    children: [\n      {\n        path: \"welcome\",\n        name: \"welcome\",\n        component: () => import(\"@/components/WelcomePage\"),\n      },\n      {\n        path: \"data-overview\",\n        name: \"data-overview\",\n        component: () => import(\"@/components/DataOverview/DataOverviewPage\"),\n      },\n      {\n        path: \"goods\",\n        name: \"goods\",\n        component: () => import(\"@/components/Goods/GoodsPage\"),\n      },\n      {\n        path: \"goods/add\",\n        name: \"goods_add\",\n        component: () => import(\"@/components/Goods/GoodsAddPage\"),\n      },\n      {\n        path: \"nature\",\n        name: \"nature\",\n        component: () => import(\"@/components/Nature/NaturePage\"),\n      },\n      {\n        path: \"specification/detail\",\n        name: \"specification_detail\",\n        component: () =>\n          import(\"@/components/Specification/SpecificationAddPage\"),\n      },\n      {\n        path: \"category\",\n        name: \"category\",\n        component: () => import(\"@/components/Category/CategoryPage\"),\n      },\n      {\n        path: \"category/add\",\n        name: \"category_add\",\n        component: () => import(\"@/components/Category/CategoryAddPage\"),\n      },\n      {\n        path: \"order\",\n        name: \"order\",\n        component: () => import(\"@/components/Order/OrderPage\"),\n      },\n      {\n        path: \"order/detail\",\n        name: \"order_detail\",\n        component: () => import(\"@/components/Order/OrderDetailPage\"),\n      },\n      {\n        path: \"user\",\n        name: \"user\",\n        component: () => import(\"@/components/User/UserPage\"),\n      },\n      {\n        path: \"user/add\",\n        name: \"user_add\",\n        component: () => import(\"@/components/User/UserAddPage\"),\n      },\n      {\n        path: \"shipper\",\n        name: \"shipper\",\n        component: () => import(\"@/components/Shipper/ShipperPage\"),\n      },\n      {\n        path: \"shipper/list\",\n        name: \"shipper_list\",\n        component: () => import(\"@/components/Shipper/ShipperListPage\"),\n      },\n      {\n        path: \"shipper/add\",\n        name: \"shipper_add\",\n        component: () => import(\"@/components/Shipper/ShipperAddPage\"),\n      },\n      {\n        path: \"freight\",\n        name: \"freight\",\n        component: () => import(\"@/components/Freight/FreightPage\"),\n      },\n      {\n        path: \"except_area\",\n        name: \"except_area\",\n        component: () => import(\"@/components/Freight/ExceptAreaPage\"),\n      },\n      {\n        path: \"except_area/add\",\n        name: \"except_area_add\",\n        component: () => import(\"@/components/Freight/ExceptAreaAddPage\"),\n      },\n      {\n        path: \"freight/add\",\n        name: \"freight_add\",\n        component: () => import(\"@/components/Freight/FreightAddPage\"),\n      },\n      {\n        path: \"notice\",\n        name: \"notice\",\n        component: () => import(\"@/components/Settings/NoticePage\"),\n      },\n      {\n        path: \"ad\",\n        name: \"ad\",\n        component: () => import(\"@/components/Ad/AdPage\"),\n      },\n      {\n        path: \"ad/add\",\n        name: \"ad_add\",\n        component: () => import(\"@/components/Ad/AdAddPage\"),\n      },\n      {\n        path: \"shopcart\",\n        name: \"shopcart\",\n        component: () => import(\"@/components/ShopCart/ShopCartPage\"),\n      },\n      {\n        path: \"keywords\",\n        name: \"keywords\",\n        component: () => import(\"@/components/Keywords/KeywordsPage\"),\n      },\n      {\n        path: \"keywords/add\",\n        name: \"keywords_add\",\n        component: () => import(\"@/components/Keywords/KeywordsAddPage\"),\n      },\n      {\n        path: \"goodsgalleryedit\",\n        name: \"goodsgalleryedit\",\n        component: () =>\n          import(\"@/components/GoodsGallery/GoodsGalleryEditPage\"),\n      },\n      {\n        path: \"admin\",\n        name: \"admin\",\n        component: () => import(\"@/components/Admin/AdminPage\"),\n      },\n      {\n        path: \"admin/add\",\n        name: \"admin_add\",\n        component: () => import(\"@/components/Admin/AdminAddPage\"),\n      },\n      {\n        path: \"settings/showset\",\n        name: \"showset\",\n        component: () => import(\"@/components/Showset/ShowSetPage\"),\n      },\n\n      {\n        path: \"promotion\",\n        name: \"promotion\",\n        component: () => import(\"@/components/Promotion/PromotionPage\"),\n      },\n      {\n        path: \"promotion/member-management\",\n        name: \"promotion_member_management\",\n        component: () => import(\"@/components/Promotion/MemberManagementPage\"),\n      },\n      {\n        path: \"promotion/data-overview\",\n        name: \"promotion_data_overview\",\n        component: () => import(\"@/components/Promotion/DataOverviewPage\"),\n      },\n      {\n        path: \"promotion/member-list\",\n        name: \"promotion_member_list\",\n        component: () => import(\"@/components/Promotion/DistributorListPage\"),\n      },\n      {\n        path: \"promotion/mode-settings\",\n        name: \"promotion_mode_settings\",\n        component: () => import(\"@/components/Promotion/ModeSettingsPage\"),\n      },\n      {\n        path: \"promotion/review-status\",\n        name: \"promotion_review_status\",\n        component: () => import(\"@/components/Promotion/ReviewStatusPage\"),\n      },\n      {\n        path: \"promotion/commission-settings\",\n        name: \"promotion_commission_settings\",\n        component: () => import(\"@/components/Promotion/CommissionSettingsPage\"),\n      },\n      {\n        path: \"promotion/recruitment\",\n        name: \"promotion_recruitment\",\n        redirect: \"/dashboard/promotion/recruitment-rules\"\n      },\n      {\n        path: \"promotion/product-pool\",\n        name: \"promotion_product_pool\",\n        component: () => import(\"@/components/Promotion/ProductPoolPage\"),\n      },\n      // 个人推广路由\n      {\n        path: \"personal-promotion/promoter-list\",\n        name: \"personal_promotion_promoter_list\",\n        component: () => import(\"@/components/PersonalPromotion/PromoterListPage\"),\n      },\n      {\n        path: \"personal-promotion/data-overview\",\n        name: \"personal_promotion_data_overview\",\n        component: () => import(\"@/components/PersonalPromotion/DataOverviewPage\"),\n      },\n      {\n        path: \"personal-promotion/commission\",\n        name: \"personal_promotion_commission\",\n        component: () => import(\"@/components/PersonalPromotion/CommissionPage\"),\n      },\n\n      {\n        path: \"personal-promotion/share-records\",\n        name: \"personal_promotion_share_records\",\n        component: () => import(\"@/components/Share/ShareRecordsPage\"),\n      },\n      {\n        path: \"promotion/recruitment-rules\",\n        name: \"promotion_recruitment_rules\",\n        component: () => import(\"@/components/Promotion/RecruitmentRulesPage\"),\n      },\n      {\n        path: \"marketing/order-gifts\",\n        name: \"marketing_order_gifts\",\n        component: () => import(\"@/components/Marketing/OrderGiftsPage\"),\n      },\n      {\n        path: \"marketing/coupons\",\n        name: \"marketing_coupons\",\n        component: () => import(\"@/components/Marketing/CouponsPage\"),\n      },\n      {\n        path: \"marketing/flash-sale\",\n        name: \"marketing_flash_sale\",\n        component: () => import(\"@/components/Marketing/FlashSaleMultiPage\"),\n      },\n      {\n        path: \"marketing/group-buy\",\n        name: \"marketing_group_buy\",\n        component: () => import(\"@/components/Marketing/GroupBuyPage\"),\n      },\n      {\n        path: \"marketing/points-goods\",\n        name: \"marketing_points_goods\",\n        component: () => import(\"@/components/Marketing/PointsGoodsPage\"),\n      },\n    ],\n  },\n  {\n    path: \"/login\",\n    name: \"login\",\n    component: () => import(\"@/components/LoginPage\"),\n  },\n  {\n    path: \"*\",\n    redirect: \"/dashboard\",\n  },\n];\n\nconst createRouter = () =>\n  new Router({\n    // mode: 'history', // () =>import service support\n    scrollBehavior: () => ({ y: 0 }),\n    routes: constantRoutes,\n  });\n\nconst router = createRouter();\n\n// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465\nexport function resetRouter() {\n  const newRouter = createRouter();\n  router.matcher = newRouter.matcher; // reset router\n}\n\nexport default router;\n"], "mappings": ";;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,YAAY;AAE/BD,GAAG,CAACE,GAAG,CAACD,MAAM,CAAC;;AAEf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,OAAO,IAAME,cAAc,GAAG,CAC5B;EACEC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE;IAAA;MAAA,uCAAa,4BAA4B;IAAA;EAAA,CAAC;EACrDC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE;MAAA;QAAA,uCAAa,0BAA0B;MAAA;IAAA;EACpD,CAAC,EACD;IACEF,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAE;MAAA;QAAA,uCAAa,4CAA4C;MAAA;IAAA;EACtE,CAAC,EACD;IACEF,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE;MAAA;QAAA,uCAAa,8BAA8B;MAAA;IAAA;EACxD,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE;MAAA;QAAA,uCAAa,iCAAiC;MAAA;IAAA;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;MAAA;QAAA,uCAAa,gCAAgC;MAAA;IAAA;EAC1D,CAAC,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE;MAAA;QAAA,uCACF,iDAAiD;MAAA;IAAA;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE;MAAA;QAAA,uCAAa,oCAAoC;MAAA;IAAA;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;MAAA;QAAA,uCAAa,uCAAuC;MAAA;IAAA;EACjE,CAAC,EACD;IACEF,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE;MAAA;QAAA,uCAAa,8BAA8B;MAAA;IAAA;EACxD,CAAC,EACD;IACEF,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;MAAA;QAAA,uCAAa,oCAAoC;MAAA;IAAA;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE;MAAA;QAAA,uCAAa,4BAA4B;MAAA;IAAA;EACtD,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE;MAAA;QAAA,uCAAa,+BAA+B;MAAA;IAAA;EACzD,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE;MAAA;QAAA,uCAAa,kCAAkC;MAAA;IAAA;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;MAAA;QAAA,uCAAa,sCAAsC;MAAA;IAAA;EAChE,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE;MAAA;QAAA,uCAAa,qCAAqC;MAAA;IAAA;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE;MAAA;QAAA,uCAAa,kCAAkC;MAAA;IAAA;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE;MAAA;QAAA,uCAAa,qCAAqC;MAAA;IAAA;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE;MAAA;QAAA,uCAAa,wCAAwC;MAAA;IAAA;EAClE,CAAC,EACD;IACEF,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAE;MAAA;QAAA,uCAAa,qCAAqC;MAAA;IAAA;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;MAAA;QAAA,uCAAa,kCAAkC;MAAA;IAAA;EAC5D,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE;MAAA;QAAA,uCAAa,wBAAwB;MAAA;IAAA;EAClD,CAAC,EACD;IACEF,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE;MAAA;QAAA,uCAAa,2BAA2B;MAAA;IAAA;EACrD,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE;MAAA;QAAA,uCAAa,oCAAoC;MAAA;IAAA;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE;MAAA;QAAA,uCAAa,oCAAoC;MAAA;IAAA;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE;MAAA;QAAA,uCAAa,uCAAuC;MAAA;IAAA;EACjE,CAAC,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE;MAAA;QAAA,uCACF,gDAAgD;MAAA;IAAA;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE;MAAA;QAAA,uCAAa,8BAA8B;MAAA;IAAA;EACxD,CAAC,EACD;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE;MAAA;QAAA,uCAAa,iCAAiC;MAAA;IAAA;EAC3D,CAAC,EACD;IACEF,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE;MAAA;QAAA,uCAAa,kCAAkC;MAAA;IAAA;EAC5D,CAAC,EAED;IACEF,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE;MAAA;QAAA,uCAAa,sCAAsC;MAAA;IAAA;EAChE,CAAC,EACD;IACEF,IAAI,EAAE,6BAA6B;IACnCC,IAAI,EAAE,6BAA6B;IACnCC,SAAS,EAAE;MAAA;QAAA,uCAAa,6CAA6C;MAAA;IAAA;EACvE,CAAC,EACD;IACEF,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,yBAAyB;IAC/BC,SAAS,EAAE;MAAA;QAAA,uCAAa,yCAAyC;MAAA;IAAA;EACnE,CAAC,EACD;IACEF,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAE;MAAA;QAAA,uCAAa,4CAA4C;MAAA;IAAA;EACtE,CAAC,EACD;IACEF,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,yBAAyB;IAC/BC,SAAS,EAAE;MAAA;QAAA,uCAAa,yCAAyC;MAAA;IAAA;EACnE,CAAC,EACD;IACEF,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,yBAAyB;IAC/BC,SAAS,EAAE;MAAA;QAAA,uCAAa,yCAAyC;MAAA;IAAA;EACnE,CAAC,EACD;IACEF,IAAI,EAAE,+BAA+B;IACrCC,IAAI,EAAE,+BAA+B;IACrCC,SAAS,EAAE;MAAA;QAAA,uCAAa,+CAA+C;MAAA;IAAA;EACzE,CAAC,EACD;IACEF,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,uBAAuB;IAC7BE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAE;MAAA;QAAA,uCAAa,wCAAwC;MAAA;IAAA;EAClE,CAAC;EACD;EACA;IACEF,IAAI,EAAE,kCAAkC;IACxCC,IAAI,EAAE,kCAAkC;IACxCC,SAAS,EAAE;MAAA;QAAA,uCAAa,iDAAiD;MAAA;IAAA;EAC3E,CAAC,EACD;IACEF,IAAI,EAAE,kCAAkC;IACxCC,IAAI,EAAE,kCAAkC;IACxCC,SAAS,EAAE;MAAA;QAAA,uCAAa,iDAAiD;MAAA;IAAA;EAC3E,CAAC,EACD;IACEF,IAAI,EAAE,+BAA+B;IACrCC,IAAI,EAAE,+BAA+B;IACrCC,SAAS,EAAE;MAAA;QAAA,uCAAa,+CAA+C;MAAA;IAAA;EACzE,CAAC,EAED;IACEF,IAAI,EAAE,kCAAkC;IACxCC,IAAI,EAAE,kCAAkC;IACxCC,SAAS,EAAE;MAAA;QAAA,uCAAa,qCAAqC;MAAA;IAAA;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,6BAA6B;IACnCC,IAAI,EAAE,6BAA6B;IACnCC,SAAS,EAAE;MAAA;QAAA,uCAAa,6CAA6C;MAAA;IAAA;EACvE,CAAC,EACD;IACEF,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAE;MAAA;QAAA,uCAAa,uCAAuC;MAAA;IAAA;EACjE,CAAC,EACD;IACEF,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAE;MAAA;QAAA,uCAAa,oCAAoC;MAAA;IAAA;EAC9D,CAAC,EACD;IACEF,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE;MAAA;QAAA,uCAAa,2CAA2C;MAAA;IAAA;EACrE,CAAC,EACD;IACEF,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE;MAAA;QAAA,uCAAa,qCAAqC;MAAA;IAAA;EAC/D,CAAC,EACD;IACEF,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,wBAAwB;IAC9BC,SAAS,EAAE;MAAA;QAAA,uCAAa,wCAAwC;MAAA;IAAA;EAClE,CAAC;AAEL,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE;IAAA;MAAA,uCAAa,wBAAwB;IAAA;EAAA;AAClD,CAAC,EACD;EACEF,IAAI,EAAE,GAAG;EACTG,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,IAAME,YAAY,GAAG,SAAfA,YAAY;EAAA,OAChB,IAAIR,MAAM,CAAC;IACT;IACAS,cAAc,EAAE;MAAA,OAAO;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAChCC,MAAM,EAAET;EACV,CAAC,CAAC;AAAA;AAEJ,IAAMU,MAAM,GAAGJ,YAAY,EAAE;;AAE7B;AACA,OAAO,SAASK,WAAW,GAAG;EAC5B,IAAMC,SAAS,GAAGN,YAAY,EAAE;EAChCI,MAAM,CAACG,OAAO,GAAGD,SAAS,CAACC,OAAO,CAAC,CAAC;AACtC;;AAEA,eAAeH,MAAM"}]}