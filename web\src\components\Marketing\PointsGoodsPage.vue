<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">积分兑好礼管理</h1>
      <p class="text-gray-600 mt-1">从商品库中选择商品规格，设置积分兑换条件</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <i class="ri-gift-line text-2xl text-blue-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总商品数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_goods || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <i class="ri-check-line text-2xl text-green-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">上架商品</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.online_goods || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <i class="ri-shopping-cart-line text-2xl text-yellow-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">兑换订单</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_orders || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <i class="ri-coin-line text-2xl text-red-600"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">消耗积分</p>
            <p class="text-2xl font-semibold text-gray-900">{{ statistics.total_points || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="bg-white rounded-lg shadow mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div class="flex items-center space-x-4">
            <button
              @click="openGoodsSelector"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <i class="ri-add-line mr-2"></i>
              添加积分商品
            </button>
          </div>

          <div class="flex items-center space-x-3">
            <div class="relative">
              <input
                v-model="searchKeyword"
                @input="searchGoods"
                type="text"
                placeholder="搜索商品名称..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>

            <select
              v-model="statusFilter"
              @change="loadPointsGoods"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部状态</option>
              <option value="1">上架</option>
              <option value="0">下架</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                商品信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                规格信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                积分价格
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                库存/销量
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in pointsGoodsList" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-16 w-16">
                    <img
                      :src="item.goods_image || '/static/images/default-goods.png'"
                      :alt="item.goods_name"
                      class="h-16 w-16 rounded-lg object-cover"
                    />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 max-w-xs truncate">
                      {{ item.goods_name }}
                    </div>
                    <div class="text-sm text-gray-500">
                      商品ID: {{ item.goods_id }}
                    </div>
                    <div v-if="item.is_hot" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                      热门
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ item.specification_info || '默认规格' }}
                </div>
                <div class="text-sm text-gray-500">
                  规格ID: {{ item.product_id }}
                </div>
                <div class="text-sm text-gray-500">
                  原价: ¥{{ item.original_price }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-red-600">
                  {{ item.points_price }} 积分
                </div>
                <div v-if="item.cash_price > 0" class="text-sm text-gray-500">
                  + ¥{{ item.cash_price }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>库存: {{ item.stock_limit > 0 ? `${item.stock_limit - item.sold_count}/${item.stock_limit}` : item.stock }}</div>
                <div>销量: {{ item.sold_count }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  :class="[
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    item.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ item.status === 1 ? '上架' : '下架' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editPointsGoods(item)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  编辑
                </button>
                <button
                  @click="toggleStatus(item)"
                  :class="[
                    'hover:opacity-75',
                    item.status === 1 ? 'text-red-600' : 'text-green-600'
                  ]"
                >
                  {{ item.status === 1 ? '下架' : '上架' }}
                </button>
                <button
                  @click="deletePointsGoods(item)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ (pagination.page - 1) * pagination.limit + 1 }} 到
            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="px-3 py-1 text-sm">
              第 {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }} 页
            </span>
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= Math.ceil(pagination.total / pagination.limit)"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="pointsGoodsList.length === 0 && !loading" class="text-center py-12">
        <i class="ri-gift-line text-6xl text-gray-300 mb-4"></i>
        <p class="text-gray-500 text-lg mb-2">暂无积分商品</p>
        <p class="text-gray-400 text-sm">点击"添加积分商品"开始配置</p>
      </div>
    </div>

    <!-- 商品选择器弹窗 -->
    <div v-if="showGoodsSelector" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">选择商品规格</h3>
          <button @click="closeGoodsSelector" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-2xl"></i>
          </button>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <!-- 搜索栏 -->
          <div class="mb-6 flex items-center space-x-4">
            <div class="relative flex-1">
              <input
                v-model="goodsSearchKeyword"
                @input="searchAvailableGoods"
                type="text"
                placeholder="搜索商品名称..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <i class="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>

            <select
              v-model="goodsSearchCategory"
              @change="searchAvailableGoods"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- 商品网格 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="goods in availableGoods"
              :key="goods.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              @click="selectGoods(goods)"
            >
              <div class="flex items-start space-x-4">
                <img
                  :src="goods.list_pic_url || '/static/images/default-goods.png'"
                  :alt="goods.name"
                  class="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ goods.name }}</h4>
                  <p class="text-xs text-gray-500 mt-1 line-clamp-2">{{ goods.goods_brief }}</p>
                  <div class="mt-2 text-xs text-gray-600">
                    <div>商品ID: {{ goods.id }}</div>
                    <div>规格数: {{ goods.product_count || 0 }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 商品分页 -->
          <div v-if="goodsTotal > goodsLimit" class="mt-6 flex items-center justify-center space-x-2">
            <button
              @click="changeGoodsPage(goodsPage - 1)"
              :disabled="goodsPage <= 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="px-3 py-1 text-sm">
              第 {{ goodsPage }} / {{ Math.ceil(goodsTotal / goodsLimit) }} 页
            </span>
            <button
              @click="changeGoodsPage(goodsPage + 1)"
              :disabled="goodsPage >= Math.ceil(goodsTotal / goodsLimit)"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 规格选择弹窗 -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">
            {{ editingGoods ? '编辑积分商品' : '添加积分商品' }}
          </h3>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-2xl"></i>
          </button>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 商品信息 -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900">商品信息</h4>

              <div v-if="selectedProduct" class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-start space-x-4">
                  <img
                    :src="selectedProduct.goods_image || '/static/images/default-goods.png'"
                    :alt="selectedProduct.goods_name"
                    class="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                  />
                  <div class="flex-1">
                    <h5 class="text-sm font-medium text-gray-900">{{ selectedProduct.goods_name }}</h5>
                    <p class="text-xs text-gray-500 mt-1">{{ selectedProduct.goods_brief }}</p>
                    <div class="mt-2 text-xs text-gray-600">
                      <div>商品ID: {{ selectedProduct.goods_id }}</div>
                      <div>规格ID: {{ selectedProduct.product_id }}</div>
                      <div>原价: ¥{{ selectedProduct.original_price }}</div>
                      <div>库存: {{ selectedProduct.stock }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 积分设置 -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900">积分设置</h4>

              <div class="grid grid-cols-1 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">积分价格 *</label>
                  <input
                    v-model="formData.points_price"
                    type="number"
                    min="0"
                    placeholder="请输入积分价格"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">现金价格</label>
                  <input
                    v-model="formData.cash_price"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="可选，积分+现金模式"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">库存限制</label>
                  <input
                    v-model="formData.stock_limit"
                    type="number"
                    min="0"
                    placeholder="0表示不限制，使用商品原库存"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">每日限购</label>
                  <input
                    v-model="formData.daily_limit"
                    type="number"
                    min="0"
                    placeholder="0表示不限制"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">用户限购</label>
                  <input
                    v-model="formData.user_limit"
                    type="number"
                    min="0"
                    placeholder="0表示不限制"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">排序</label>
                  <input
                    v-model="formData.sort"
                    type="number"
                    min="0"
                    placeholder="数字越大排序越靠前"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div class="flex items-center space-x-6">
                  <label class="flex items-center">
                    <input
                      v-model="formData.status"
                      type="checkbox"
                      :true-value="1"
                      :false-value="0"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">立即上架</span>
                  </label>

                  <label class="flex items-center">
                    <input
                      v-model="formData.is_hot"
                      type="checkbox"
                      :true-value="1"
                      :false-value="0"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">热门商品</span>
                  </label>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">商品描述</label>
                  <textarea
                    v-model="formData.description"
                    rows="3"
                    placeholder="可选，积分商品的特殊描述"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex items-center justify-end space-x-3">
            <button
              @click="closeModal"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              @click="savePointsGoods"
              :disabled="!formData.points_price || loading"
              class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ editingGoods ? '更新' : '添加' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script>
import pointsGoodsApi from '@/api/points-goods'

export default {
  name: 'PointsGoodsPage',
  data() {
    return {
      loading: false,
      statistics: {},
      pointsGoodsList: [],
      categories: [],
      availableGoods: [],
      selectedProduct: null,

      // 分页
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },

      // 搜索和筛选
      searchKeyword: '',
      statusFilter: '',

      // 商品选择器
      showGoodsSelector: false,
      goodsSearchKeyword: '',
      goodsSearchCategory: '',
      goodsPage: 1,
      goodsLimit: 12,
      goodsTotal: 0,

      // 弹窗
      showModal: false,
      editingGoods: null,

      // 表单数据
      formData: {
        goods_id: '',
        product_id: '',
        points_price: '',
        cash_price: 0,
        stock_limit: 0,
        daily_limit: 0,
        user_limit: 0,
        sort: 0,
        status: 1,
        is_hot: 0,
        description: ''
      }
    }
  },

  mounted() {
    this.init()
  },

  methods: {
    async init() {
      await this.loadStatistics()
      await this.loadPointsGoods()
      await this.loadCategories()
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await pointsGoodsApi.getStatistics()
        if (response.errno === 0) {
          this.statistics = response.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 加载积分商品列表
    async loadPointsGoods(page = 1) {
      this.loading = true
      try {
        const params = {
          page: page,
          limit: this.pagination.limit,
          keyword: this.searchKeyword,
          status: this.statusFilter
        }

        const response = await pointsGoodsApi.getList(params)
        if (response.errno === 0) {
          this.pointsGoodsList = response.data.list
          this.pagination = {
            page: response.data.page,
            limit: response.data.limit,
            total: response.data.total
          }
        } else {
          this.$message.error(response.errmsg || '加载积分商品列表失败')
        }
      } catch (error) {
        console.error('加载积分商品列表失败:', error)
        this.$message.error('加载积分商品列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载分类
    async loadCategories() {
      try {
        const response = await pointsGoodsApi.getCategories()
        if (response.errno === 0) {
          this.categories = response.data
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },

    // 搜索商品
    searchGoods() {
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.loadPointsGoods(1)
      }, 500)
    },

    // 分页
    changePage(page) {
      if (page >= 1 && page <= Math.ceil(this.pagination.total / this.pagination.limit)) {
        this.loadPointsGoods(page)
      }
    },

    // 打开商品选择器
    async openGoodsSelector() {
      this.showGoodsSelector = true
      await this.loadAvailableGoods(1)
    },

    // 关闭商品选择器
    closeGoodsSelector() {
      this.showGoodsSelector = false
      this.goodsSearchKeyword = ''
      this.goodsSearchCategory = ''
      this.goodsPage = 1
      this.availableGoods = []
    },

    // 加载可选商品
    async loadAvailableGoods(page = 1) {
      this.loading = true
      try {
        const params = {
          page: page,
          limit: this.goodsLimit,
          keyword: this.goodsSearchKeyword,
          category_id: this.goodsSearchCategory
        }

        const response = await pointsGoodsApi.getAvailableGoods(params)
        if (response.errno === 0) {
          this.availableGoods = response.data.list
          this.goodsPage = response.data.page
          this.goodsTotal = response.data.total
        } else {
          this.$message.error(response.errmsg || '加载可选商品失败')
        }
      } catch (error) {
        console.error('加载可选商品失败:', error)
        this.$message.error('加载可选商品失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索可选商品
    searchAvailableGoods() {
      clearTimeout(this.goodsSearchTimer)
      this.goodsSearchTimer = setTimeout(() => {
        this.loadAvailableGoods(1)
      }, 500)
    },

    // 商品分页
    changeGoodsPage(page) {
      if (page >= 1 && page <= Math.ceil(this.goodsTotal / this.goodsLimit)) {
        this.loadAvailableGoods(page)
      }
    },

    // 选择商品
    async selectGoods(goods) {
      this.loading = true
      try {
        // 获取商品的规格列表
        const response = await pointsGoodsApi.getGoodsProducts(goods.id)
        if (response.errno === 0) {
          const products = response.data

          if (products.length === 1) {
            // 只有一个规格，直接选择
            this.selectedProduct = {
              goods_id: goods.id,
              goods_name: goods.name,
              goods_image: goods.list_pic_url,
              goods_brief: goods.goods_brief,
              product_id: products[0].id,
              original_price: products[0].retail_price,
              stock: products[0].goods_number,
              specification_info: products[0].specification_info || '默认规格'
            }
            this.openModal()
          } else {
            // 多个规格，显示规格选择
            this.showProductSelector(goods, products)
          }
        } else {
          this.$message.error(response.errmsg || '获取商品规格失败')
        }
      } catch (error) {
        console.error('获取商品规格失败:', error)
        this.$message.error('获取商品规格失败')
      } finally {
        this.loading = false
      }
    },

    // 显示规格选择器
    showProductSelector(goods, products) {
      // 创建规格选择的HTML内容
      const productOptions = products.map(product =>
        `<div class="border border-gray-200 rounded-lg p-3 cursor-pointer hover:bg-gray-50 mb-2" onclick="window.selectProduct(${product.id})">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium">${product.specification_info || '默认规格'}</span>
            <span class="text-sm text-gray-500">¥${product.retail_price}</span>
          </div>
          <div class="text-xs text-gray-400 mt-1">库存: ${product.goods_number}</div>
        </div>`
      ).join('');

      const content = `
        <div>
          <p class="mb-4 text-gray-600">请选择"${goods.name}"的规格:</p>
          <div class="space-y-2">${productOptions}</div>
        </div>
      `;

      // 临时设置全局选择函数
      window.selectProduct = (productId) => {
        const product = products.find(p => p.id === productId);
        if (product) {
          this.selectedProduct = {
            goods_id: goods.id,
            goods_name: goods.name,
            goods_image: goods.list_pic_url,
            goods_brief: goods.goods_brief,
            product_id: product.id,
            original_price: product.retail_price,
            stock: product.goods_number,
            specification_info: product.specification_info || '默认规格'
          };
          this.openModal();
          // 清理全局函数
          delete window.selectProduct;
        }
      };

      this.$alert(content, '选择商品规格', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        showCancelButton: false,
        customClass: 'product-selector-modal'
      });
    },

    // 打开弹窗
    openModal() {
      this.showGoodsSelector = false
      this.showModal = true
      this.resetForm()

      if (this.selectedProduct) {
        this.formData.goods_id = this.selectedProduct.goods_id
        this.formData.product_id = this.selectedProduct.product_id
      }
    },

    // 关闭弹窗
    closeModal() {
      this.showModal = false
      this.editingGoods = null
      this.selectedProduct = null
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        goods_id: '',
        product_id: '',
        points_price: '',
        cash_price: 0,
        stock_limit: 0,
        daily_limit: 0,
        user_limit: 0,
        sort: 0,
        status: 1,
        is_hot: 0,
        description: ''
      }
    },

    // 编辑积分商品
    editPointsGoods(item) {
      this.editingGoods = item
      this.selectedProduct = {
        goods_id: item.goods_id,
        goods_name: item.goods_name,
        goods_image: item.goods_image,
        goods_brief: item.goods_brief,
        product_id: item.product_id,
        original_price: item.original_price,
        stock: item.stock,
        specification_info: item.specification_info
      }

      this.formData = {
        goods_id: item.goods_id,
        product_id: item.product_id,
        points_price: item.points_price,
        cash_price: item.cash_price,
        stock_limit: item.stock_limit,
        daily_limit: item.daily_limit,
        user_limit: item.user_limit,
        sort: item.sort,
        status: item.status,
        is_hot: item.is_hot,
        description: item.description || ''
      }

      this.showModal = true
    },

    // 保存积分商品
    async savePointsGoods() {
      if (!this.formData.points_price) {
        this.$message.error('请输入积分价格')
        return
      }

      this.loading = true
      try {
        let response
        if (this.editingGoods) {
          response = await pointsGoodsApi.update(this.editingGoods.id, this.formData)
        } else {
          response = await pointsGoodsApi.add(this.formData)
        }

        if (response.errno === 0) {
          this.$message.success(this.editingGoods ? '更新成功' : '添加成功')
          this.closeModal()
          await this.loadPointsGoods()
          await this.loadStatistics()
        } else {
          this.$message.error(response.errmsg || '操作失败')
        }
      } catch (error) {
        console.error('保存积分商品失败:', error)
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 切换状态
    async toggleStatus(item) {
      const newStatus = item.status === 1 ? 0 : 1
      const action = newStatus === 1 ? '上架' : '下架'

      try {
        await this.$confirm(`确定要${action}这个积分商品吗？`, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.loading = true
        const response = await pointsGoodsApi.updateStatus(item.id, newStatus)

        if (response.errno === 0) {
          this.$message.success(`${action}成功`)
          await this.loadPointsGoods()
          await this.loadStatistics()
        } else {
          this.$message.error(response.errmsg || `${action}失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('切换状态失败:', error)
          this.$message.error(`${action}失败`)
        }
      } finally {
        this.loading = false
      }
    },

    // 删除积分商品
    async deletePointsGoods(item) {
      try {
        await this.$confirm('确定要删除这个积分商品吗？删除后不可恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.loading = true
        const response = await pointsGoodsApi.delete(item.id)

        if (response.errno === 0) {
          this.$message.success('删除成功')
          await this.loadPointsGoods()
          await this.loadStatistics()
        } else {
          this.$message.error(response.errmsg || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除积分商品失败:', error)
          this.$message.error('删除失败')
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格样式优化 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.online {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.offline {
  background-color: #fef2f2;
  color: #991b1b;
}

/* 按钮组样式 */
.btn-group {
  display: flex;
  gap: 0.5rem;
}

.btn-group button {
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-group button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 弹窗样式优化 */
.modal-overlay {
  backdrop-filter: blur(4px);
}

/* 商品卡片样式 */
.goods-card {
  transition: all 0.2s;
  border: 1px solid #e5e7eb;
}

.goods-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

/* 统计卡片样式 */
.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 1.5rem;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-card.blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.green {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stats-card.yellow {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stats-card.red {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stats-card.purple {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .goods-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .btn-group {
    flex-direction: column;
  }

  .table-container {
    overflow-x: auto;
  }
}
</style>