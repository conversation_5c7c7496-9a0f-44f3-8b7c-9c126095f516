const mysql = require('mysql2/promise');
const fs = require('fs');

async function executePointsGoodsTables() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    const sqlContent = fs.readFileSync('./service/database/points_goods_tables.sql', 'utf8');
    
    // 分割SQL语句，处理多行语句
    const statements = [];
    const lines = sqlContent.split('\n');
    let currentStatement = '';

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('--') && !trimmedLine.startsWith('USE')) {
        currentStatement += line + '\n';
        if (trimmedLine.endsWith(';')) {
          statements.push(currentStatement.trim());
          currentStatement = '';
        }
      }
    }

    console.log(`📝 开始执行 ${statements.length} 条SQL语句...`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`执行第 ${i + 1} 条语句: ${statement.substring(0, 50)}...`);
          await connection.execute(statement);
          console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error.message);
          console.error(`语句内容: ${statement.substring(0, 200)}...`);
          // 继续执行其他语句
        }
      }
    }
    
    console.log('🎉 积分商品表创建完成！');
    
    // 验证表是否创建成功
    console.log('\n=== 验证表创建结果 ===');
    const [tables] = await connection.execute("SHOW TABLES LIKE 'hiolabs_points_%'");
    
    console.log('📋 积分相关表列表:');
    tables.forEach(table => {
      console.log('  -', Object.values(table)[0]);
    });

    // 检查积分商品表数据
    try {
      const [goods] = await connection.execute('SELECT COUNT(*) as count FROM hiolabs_points_goods');
      console.log('✅ 积分商品表存在，记录数:', goods[0].count);
    } catch (error) {
      console.log('❌ 积分商品表检查失败:', error.message);
    }

    await connection.end();
    console.log('📝 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 执行失败:', error);
  }
}

executePointsGoodsTables();
