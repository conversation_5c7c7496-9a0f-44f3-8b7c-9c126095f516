{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\api\\points-goods.js", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\api\\points-goods.js", "mtime": 1754302407659}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "api", "baseURL", "rootUrl", "request", "create", "timeout", "interceptors", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "res", "data", "errno", "console", "errmsg", "Error", "pointsGoodsApi", "getList", "params", "url", "method", "getDetail", "id", "getAvailableGoods", "add", "update", "delete", "batchStatus", "ids", "status", "getStatistics", "getCategories", "getOrders"], "sources": ["D:/py-ide/hioshop-miniprogram-master/web/src/api/points-goods.js"], "sourcesContent": ["import axios from 'axios'\nimport api from '@/config/api'\n\n// 设置基础URL\nconst baseURL = api.rootUrl\n\n// 创建axios实例\nconst request = axios.create({\n  baseURL: baseURL,\n  timeout: 10000\n})\n\n// 请求拦截器\nrequest.interceptors.request.use(\n  config => {\n    // 可以在这里添加token等认证信息\n    const token = localStorage.getItem('token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nrequest.interceptors.response.use(\n  response => {\n    const res = response.data\n    \n    // 根据后端返回的数据结构进行处理\n    if (res.errno === 0) {\n      return res.data\n    } else {\n      // 处理错误情况\n      console.error('API Error:', res.errmsg)\n      return Promise.reject(new Error(res.errmsg || '请求失败'))\n    }\n  },\n  error => {\n    console.error('Request Error:', error)\n    return Promise.reject(error)\n  }\n)\n\n// 积分商品管理API\nexport const pointsGoodsApi = {\n  // 获取积分商品列表\n  getList(params) {\n    return request({\n      url: 'points-goods/list',\n      method: 'get',\n      params\n    })\n  },\n\n  // 获取积分商品详情\n  getDetail(id) {\n    return request({\n      url: `points-goods/detail`,\n      method: 'get',\n      params: { id }\n    })\n  },\n\n  // 获取可选商品列表\n  getAvailableGoods() {\n    return request({\n      url: 'points-goods/available-goods',\n      method: 'get'\n    })\n  },\n\n  // 添加积分商品\n  add(data) {\n    return request({\n      url: 'points-goods/add',\n      method: 'post',\n      data\n    })\n  },\n\n  // 更新积分商品\n  update(id, data) {\n    return request({\n      url: `points-goods/update`,\n      method: 'post',\n      data: { id, ...data }\n    })\n  },\n\n  // 删除积分商品\n  delete(id) {\n    return request({\n      url: 'points-goods/delete',\n      method: 'post',\n      data: { id }\n    })\n  },\n\n  // 批量更新状态\n  batchStatus(ids, status) {\n    return request({\n      url: 'points-goods/batch-status',\n      method: 'post',\n      data: { ids, status }\n    })\n  },\n\n  // 获取统计数据\n  getStatistics() {\n    return request({\n      url: 'points-goods/statistics',\n      method: 'get'\n    })\n  },\n\n  // 获取积分商品分类\n  getCategories() {\n    return request({\n      url: 'points-goods/categories',\n      method: 'get'\n    })\n  },\n\n  // 获取积分兑换订单列表\n  getOrders(params) {\n    return request({\n      url: 'points-goods/orders',\n      method: 'get',\n      params\n    })\n  }\n}\n\nexport default pointsGoodsApi\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA,IAAMC,OAAO,GAAGD,GAAG,CAACE,OAAO;;AAE3B;AACA,IAAMC,OAAO,GAAGJ,KAAK,CAACK,MAAM,CAAC;EAC3BH,OAAO,EAAEA,OAAO;EAChBI,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAF,OAAO,CAACG,YAAY,CAACH,OAAO,CAACI,GAAG,CAC9B,UAAAC,MAAM,EAAI;EACR;EACA,IAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,oBAAaJ,KAAK,CAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACD,UAAAM,KAAK,EAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;;AAED;AACAX,OAAO,CAACG,YAAY,CAACW,QAAQ,CAACV,GAAG,CAC/B,UAAAU,QAAQ,EAAI;EACV,IAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAID,GAAG,CAACE,KAAK,KAAK,CAAC,EAAE;IACnB,OAAOF,GAAG,CAACC,IAAI;EACjB,CAAC,MAAM;IACL;IACAE,OAAO,CAACP,KAAK,CAAC,YAAY,EAAEI,GAAG,CAACI,MAAM,CAAC;IACvC,OAAOP,OAAO,CAACC,MAAM,CAAC,IAAIO,KAAK,CAACL,GAAG,CAACI,MAAM,IAAI,MAAM,CAAC,CAAC;EACxD;AACF,CAAC,EACD,UAAAR,KAAK,EAAI;EACPO,OAAO,CAACP,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CAAC,CACF;;AAED;AACA,OAAO,IAAMU,cAAc,GAAG;EAC5B;EACAC,OAAO,mBAACC,MAAM,EAAE;IACd,OAAOvB,OAAO,CAAC;MACbwB,GAAG,EAAE,mBAAmB;MACxBC,MAAM,EAAE,KAAK;MACbF,MAAM,EAANA;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAG,SAAS,qBAACC,EAAE,EAAE;IACZ,OAAO3B,OAAO,CAAC;MACbwB,GAAG,uBAAuB;MAC1BC,MAAM,EAAE,KAAK;MACbF,MAAM,EAAE;QAAEI,EAAE,EAAFA;MAAG;IACf,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,iBAAiB,+BAAG;IAClB,OAAO5B,OAAO,CAAC;MACbwB,GAAG,EAAE,8BAA8B;MACnCC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,GAAG,eAACb,IAAI,EAAE;IACR,OAAOhB,OAAO,CAAC;MACbwB,GAAG,EAAE,kBAAkB;MACvBC,MAAM,EAAE,MAAM;MACdT,IAAI,EAAJA;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAc,MAAM,kBAACH,EAAE,EAAEX,IAAI,EAAE;IACf,OAAOhB,OAAO,CAAC;MACbwB,GAAG,uBAAuB;MAC1BC,MAAM,EAAE,MAAM;MACdT,IAAI;QAAIW,EAAE,EAAFA;MAAE,GAAKX,IAAI;IACrB,CAAC,CAAC;EACJ,CAAC;EAED;EACAe,MAAM,mBAACJ,EAAE,EAAE;IACT,OAAO3B,OAAO,CAAC;MACbwB,GAAG,EAAE,qBAAqB;MAC1BC,MAAM,EAAE,MAAM;MACdT,IAAI,EAAE;QAAEW,EAAE,EAAFA;MAAG;IACb,CAAC,CAAC;EACJ,CAAC;EAED;EACAK,WAAW,uBAACC,GAAG,EAAEC,MAAM,EAAE;IACvB,OAAOlC,OAAO,CAAC;MACbwB,GAAG,EAAE,2BAA2B;MAChCC,MAAM,EAAE,MAAM;MACdT,IAAI,EAAE;QAAEiB,GAAG,EAAHA,GAAG;QAAEC,MAAM,EAANA;MAAO;IACtB,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,aAAa,2BAAG;IACd,OAAOnC,OAAO,CAAC;MACbwB,GAAG,EAAE,yBAAyB;MAC9BC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAW,aAAa,2BAAG;IACd,OAAOpC,OAAO,CAAC;MACbwB,GAAG,EAAE,yBAAyB;MAC9BC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;EACAY,SAAS,qBAACd,MAAM,EAAE;IAChB,OAAOvB,OAAO,CAAC;MACbwB,GAAG,EAAE,qBAAqB;MAC1BC,MAAM,EAAE,KAAK;MACbF,MAAM,EAANA;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAeF,cAAc"}]}