// 测试秒杀API的简单脚本
// 在项目根目录运行: node test_seckill_api.js

const http = require('http');

const API_BASE = 'http://127.0.0.1:8360/api';

function makeRequest(path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: '127.0.0.1',
            port: 8360,
            path: `/api${path}`,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        data: data,
                        error: '解析JSON失败'
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('请求超时'));
        });

        req.end();
    });
}

async function testSeckillAPIs() {
    console.log('🚀 开始测试秒杀API...\n');

    // 测试1: 获取秒杀时间段
    console.log('📅 测试1: 获取秒杀时间段');
    try {
        const timeResult = await makeRequest('/seckill/index');
        console.log('状态码:', timeResult.status);
        console.log('响应数据:', JSON.stringify(timeResult.data, null, 2));
        console.log('✅ 时间段API测试完成\n');
    } catch (error) {
        console.log('❌ 时间段API测试失败:', error.message);
        console.log('💡 请确保后端服务已启动 (npm start)\n');
    }

    // 测试2: 获取首页秒杀数据
    console.log('🏠 测试2: 获取首页秒杀数据');
    try {
        const homeResult = await makeRequest('/seckill/home');
        console.log('状态码:', homeResult.status);
        console.log('响应数据:', JSON.stringify(homeResult.data, null, 2));
        console.log('✅ 首页秒杀API测试完成\n');
    } catch (error) {
        console.log('❌ 首页秒杀API测试失败:', error.message);
        console.log('💡 请确保后端服务已启动 (npm start)\n');
    }

    // 测试3: 获取秒杀商品列表（使用时间段ID 1）
    console.log('📦 测试3: 获取秒杀商品列表');
    try {
        const listResult = await makeRequest('/seckill/list?timeId=1&page=1&limit=5');
        console.log('状态码:', listResult.status);
        console.log('响应数据:', JSON.stringify(listResult.data, null, 2));
        console.log('✅ 商品列表API测试完成\n');
    } catch (error) {
        console.log('❌ 商品列表API测试失败:', error.message);
        console.log('💡 请确保后端服务已启动并且数据库中有测试数据\n');
    }

    // 测试4: 获取秒杀商品详情（使用商品ID 1）
    console.log('🔍 测试4: 获取秒杀商品详情');
    try {
        const detailResult = await makeRequest('/seckill/detail?id=1&time_id=1');
        console.log('状态码:', detailResult.status);
        console.log('响应数据:', JSON.stringify(detailResult.data, null, 2));
        console.log('✅ 商品详情API测试完成\n');
    } catch (error) {
        console.log('❌ 商品详情API测试失败:', error.message);
        console.log('💡 请确保后端服务已启动并且数据库中有测试数据\n');
    }

    console.log('🎉 所有API测试完成！');
    console.log('\n📋 测试总结:');
    console.log('- 如果看到 ✅ 表示API正常工作');
    console.log('- 如果看到 ❌ 表示API有问题，请检查:');
    console.log('  1. 后端服务是否启动 (cd service && npm start)');
    console.log('  2. 数据库连接是否正常');
    console.log('  3. 数据库中是否有测试数据');
    console.log('\n💡 提示: 可以运行 service/database/ 目录下的SQL文件来创建测试数据');
}

// 运行测试
testSeckillAPIs().catch(console.error);
