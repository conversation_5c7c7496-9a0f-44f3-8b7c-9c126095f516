-- 重新设计积分兑好礼数据库表结构
-- 基于现有商品库和规格系统

USE hiolabsDB;

-- 删除旧的积分商品表
DROP TABLE IF EXISTS `hiolabs_points_goods`;

-- 创建新的积分商品规格表（关联到具体的商品规格）
CREATE TABLE `hiolabs_points_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '关联的商品ID（hiolabs_goods表）',
  `product_id` int(11) NOT NULL COMMENT '关联的商品规格ID（hiolabs_product表）',
  `points_price` int(11) NOT NULL COMMENT '积分价格',
  `cash_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '现金价格（积分+现金混合支付）',
  `stock_limit` int(11) NOT NULL DEFAULT '0' COMMENT '积分兑换库存限制（0表示不限制，使用商品原库存）',
  `sold_count` int(11) NOT NULL DEFAULT '0' COMMENT '已兑换数量',
  `daily_limit` int(11) NOT NULL DEFAULT '0' COMMENT '每日限兑数量（0表示不限制）',
  `user_limit` int(11) NOT NULL DEFAULT '0' COMMENT '每人限兑数量（0表示不限制）',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重，数值越大越靠前',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0=下架，1=上架',
  `is_hot` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否热门：0=否，1=是',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间（NULL表示立即开始）',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间（NULL表示永久有效）',
  `description` text COMMENT '积分兑换说明',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否，1=是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_id` (`product_id`), -- 每个商品规格只能设置一次积分兑换
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_sort` (`sort`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品规格表';

-- 创建积分兑换订单表（保持不变，但调整关联关系）
DROP TABLE IF EXISTS `hiolabs_points_orders`;
CREATE TABLE `hiolabs_points_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_goods_id` int(11) NOT NULL COMMENT '积分商品ID（hiolabs_points_goods表）',
  `goods_id` int(11) NOT NULL COMMENT '商品ID（hiolabs_goods表）',
  `product_id` int(11) NOT NULL COMMENT '商品规格ID（hiolabs_product表）',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `goods_specification` varchar(500) DEFAULT '' COMMENT '商品规格信息',
  `goods_image` varchar(500) DEFAULT '' COMMENT '商品图片',
  `points_price` int(11) NOT NULL COMMENT '积分价格',
  `cash_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '现金价格',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '兑换数量',
  `total_points` int(11) NOT NULL COMMENT '总积分',
  `total_cash` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总现金',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态：0=待发货，1=已发货，2=已完成，3=已取消',
  `address_info` text COMMENT '收货地址信息JSON',
  `express_company` varchar(100) DEFAULT '' COMMENT '快递公司',
  `express_no` varchar(100) DEFAULT '' COMMENT '快递单号',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否，1=是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sn` (`order_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_points_goods_id` (`points_goods_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分兑换订单表';

-- 创建用户积分兑换限制记录表
CREATE TABLE `hiolabs_points_user_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_goods_id` int(11) NOT NULL COMMENT '积分商品ID',
  `exchange_count` int(11) NOT NULL DEFAULT '0' COMMENT '已兑换数量',
  `last_exchange_date` date DEFAULT NULL COMMENT '最后兑换日期',
  `daily_count` int(11) NOT NULL DEFAULT '0' COMMENT '今日已兑换数量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_goods` (`user_id`, `points_goods_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_points_goods_id` (`points_goods_id`),
  KEY `idx_last_exchange_date` (`last_exchange_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分兑换限制记录表';

-- 保留积分商城配置表和分类表（不变）
-- 这些表结构已经合理，继续使用

COMMIT;
