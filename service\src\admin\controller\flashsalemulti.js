const Base = require('./base.js');
const FlashSaleMultiScheduler = require('../../common/service/flash_sale_multi_scheduler');

module.exports = class extends Base {

  /**
   * 格式化本地时间为数据库格式（避免时区问题）
   * @param {Date} date 日期对象
   * @returns {string} 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
   */
  formatLocalDateTime(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 格式化本地日期为数据库格式（避免时区问题）
   * @param {Date} date 日期对象
   * @returns {string} 格式化的日期字符串 YYYY-MM-DD
   */
  formatLocalDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 获取统计数据
   * GET /admin/flashsalemulti/statistics
   */
  async statisticsAction() {
    try {
      const roundModel = this.model('flash_sale_rounds');
      const orderModel = this.model('flash_sale_orders');

      // 统计轮次数据
      const totalRounds = await roundModel.count();
      const activeRounds = await roundModel.where({ status: 'active' }).count();
      const upcomingRounds = await roundModel.where({ status: 'upcoming' }).count();
      const endedRounds = await roundModel.where({ status: 'ended' }).count();

      // 统计订单数据
      const totalOrders = await orderModel.count();
      const today = this.formatLocalDate(new Date());
      const todayOrders = await orderModel.where({
        created_at: ['>=', today + ' 00:00:00']
      }).count();

      // 统计销售额
      const totalSales = await orderModel.sum('total_amount') || 0;

      return this.success({
        totalRounds,
        activeRounds,
        upcomingRounds,
        endedRounds,
        totalOrders,
        todayOrders,
        totalSales
      });

    } catch (error) {
      console.error('获取统计数据失败:', error);
      return this.fail('获取统计数据失败');
    }
  }

  /**
   * 测试API - 简单查询轮次
   * GET /admin/flashsalemulti/test
   */
  async testAction() {
    try {
      console.log('=== 测试API调用 ===');
      const roundModel = this.model('flash_sale_rounds');

      // 简单查询所有轮次
      const rounds = await roundModel.select();
      console.log('测试查询结果:', rounds.length);

      return this.success({
        message: '测试成功',
        count: rounds.length,
        data: rounds
      });
    } catch (error) {
      console.error('测试API失败:', error);
      return this.fail('测试失败: ' + error.message);
    }
  }

  /**
   * 获取轮次列表
   * GET /admin/flashsalemulti/list
   */
  async listAction() {
    try {
      console.log('=== 获取轮次列表 ===');
      const page = this.get('page') || 1;
      const limit = this.get('limit') || 20;
      const status = this.get('status');

      console.log('查询参数:', { page, limit, status });

      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      let where = {};
      if (status) {
        where.status = status;
      }

      // 获取轮次列表
      console.log('查询条件:', where);
      const rounds = await roundModel.where(where)
        .order('round_number DESC')
        .page(page, limit)
        .countSelect();

      console.log('查询到的轮次数量:', rounds.data ? rounds.data.length : 0);
      console.log('轮次数据结构:', rounds);

      // 为每个轮次获取商品信息
      if (rounds.data && rounds.data.length > 0) {
        for (let round of rounds.data) {
          try {
            console.log('处理轮次:', round.id, round.round_name);
            const goods = await roundGoodsModel.getRoundGoodsList(round.id);
            console.log('轮次商品数量:', goods.length);

            round.goods_list = goods || [];
            round.goods_count = goods ? goods.length : 0;

            // 计算总库存和总销量
            round.total_stock = goods ? goods.reduce((sum, g) => sum + (g.stock || 0), 0) : 0;
            round.total_sold = goods ? goods.reduce((sum, g) => sum + (g.sold_count || 0), 0) : 0;
          } catch (roundError) {
            console.error('处理轮次失败:', round.id, roundError);
            // 即使单个轮次处理失败，也继续处理其他轮次
            round.goods_list = [];
            round.goods_count = 0;
            round.total_stock = 0;
            round.total_sold = 0;
          }
        }
      }

      console.log('返回轮次列表，总数:', rounds.data.length);
      return this.success(rounds);

    } catch (error) {
      console.error('获取轮次列表失败:', error);
      return this.fail('获取轮次列表失败');
    }
  }

  /**
   * 获取当前轮次
   * GET /admin/flashsalemulti/current
   */
  async currentAction() {
    try {
      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      // 获取当前活跃轮次
      const currentRounds = await roundModel.where({ status: 'active' }).select();
      
      // 获取即将开始的轮次
      const upcomingRounds = await roundModel.where({ status: 'upcoming' })
        .order('start_time ASC')
        .limit(5)
        .select();

      // 为每个轮次添加商品信息
      for (let round of [...currentRounds, ...upcomingRounds]) {
        const goods = await roundGoodsModel.getRoundGoodsList(round.id);
        round.goods_list = goods;
        round.goods_count = goods.length;
        
        // 计算倒计时
        const now = new Date();
        const startTime = new Date(round.start_time);
        const endTime = new Date(round.end_time);
        
        if (round.status === 'active') {
          round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));
        } else if (round.status === 'upcoming') {
          round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));
        }
      }

      return this.success({
        current: currentRounds,
        upcoming: upcomingRounds,
        total: currentRounds.length + upcomingRounds.length
      });

    } catch (error) {
      console.error('获取当前轮次失败:', error);
      return this.fail('获取当前轮次失败');
    }
  }

  /**
   * 创建新轮次（支持多商品）
   * POST /admin/flashsalemulti/create
   */
  async createAction() {
    try {
      console.log('=== 创建新轮次（多商品） ===');

      const data = this.post();
      console.log('接收到的数据:', JSON.stringify(data, null, 2));

      // 验证必填字段
      if (!data.round_name) {
        return this.fail('请填写轮次名称');
      }

      if (!data.start_time) {
        return this.fail('请选择开始时间');
      }

      if (!data.end_time) {
        return this.fail('请选择结束时间');
      }

      if (!data.goods_list || !Array.isArray(data.goods_list) || data.goods_list.length === 0) {
        return this.fail('请选择参与秒杀的商品');
      }

      // 验证时间
      const startTime = new Date(data.start_time);
      const endTime = new Date(data.end_time);
      const now = new Date();

      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
        return this.fail('时间格式不正确');
      }

      if (startTime <= now) {
        return this.fail('开始时间必须晚于当前时间');
      }

      if (endTime <= startTime) {
        return this.fail('结束时间必须晚于开始时间');
      }

      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');
      const goodsModel = this.model('goods');
      
      // 验证商品数据
      for (let i = 0; i < data.goods_list.length; i++) {
        const goods = data.goods_list[i];
        if (!goods.goods_id || !goods.flash_price || !goods.stock) {
          return this.fail(`第${i+1}个商品信息不完整`);
        }
      }

      // 检查商品是否已参与其他活跃轮次
      // 对于整点秒杀，允许商品在不同时段重复使用
      if (!data.is_hourly_flash) {
        const goodsIds = data.goods_list.map(g => g.goods_id);
        const conflictGoods = await roundGoodsModel.checkGoodsInActiveRounds(goodsIds);
        if (conflictGoods.length > 0) {
          const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(轮次${g.round_number})`).join(', ');
          return this.fail(`以下商品已参与其他活跃轮次: ${conflictNames}`);
        }
      } else {
        // 整点秒杀：检查时间重叠的轮次
        const goodsIds = data.goods_list.map(g => g.goods_id);
        // 确保时间格式正确
        const formattedStartTime = this.formatLocalDateTime(startTime);
        const formattedEndTime = this.formatLocalDateTime(endTime);
        const conflictGoods = await roundGoodsModel.checkGoodsInOverlappingRounds(goodsIds, formattedStartTime, formattedEndTime);

        if (conflictGoods.length > 0) {
          // 判断是否是当前正在进行的轮次
          const now = new Date();
          const currentConflicts = conflictGoods.filter(g => {
            const gStartTime = new Date(g.start_time);
            const gEndTime = new Date(g.end_time);
            return now >= gStartTime && now < gEndTime && g.status === 'active';
          });

          if (currentConflicts.length > 0) {
            const conflictNames = currentConflicts.map(g => `商品ID:${g.goods_id}`).join(', ');
            return this.fail(`以下商品正在当前时段参与秒杀，无需重复创建: ${conflictNames}`);
          } else {
            const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(${g.start_time}-${g.end_time})`).join(', ');
            return this.fail(`以下商品在该时段已有冲突: ${conflictNames}`);
          }
        }
      }

      // 移除系统配置依赖，直接使用用户提供的时间

      // 获取下一个轮次编号 - 使用更安全的方法（多次重试）
      let nextRoundNumber = 1;
      let maxRetries = 5;
      let retryCount = 0;

      while (retryCount < maxRetries) {
        try {
          // 使用MAX函数获取最大轮次编号
          const maxRoundResult = await roundModel.field('MAX(round_number) as max_round').find();
          if (maxRoundResult && maxRoundResult.max_round) {
            nextRoundNumber = parseInt(maxRoundResult.max_round) + 1;
          }
          console.log(`获取到的下一个轮次编号: ${nextRoundNumber} (尝试 ${retryCount + 1}/${maxRetries})`);
          break;
        } catch (error) {
          retryCount++;
          console.error(`获取轮次编号失败 (尝试 ${retryCount}/${maxRetries}):`, error);
          if (retryCount >= maxRetries) {
            // 如果所有重试都失败，使用时间戳作为后备方案
            nextRoundNumber = Date.now() % 1000000; // 使用时间戳的后6位
            console.log('使用时间戳作为轮次编号:', nextRoundNumber);
          } else {
            // 等待一小段时间后重试
            await new Promise(resolve => setTimeout(resolve, 100 * retryCount));
          }
        }
      }

      // 根据时间判断轮次状态
      const now = new Date();
      const startTimeObj = new Date(startTime);
      const endTimeObj = new Date(endTime);

      let status = 'upcoming';
      if (now >= startTimeObj && now < endTimeObj) {
        status = 'active';  // 当前时间在轮次时间范围内，设为进行中
      } else if (now >= endTimeObj) {
        status = 'ended';   // 当前时间已超过结束时间，设为已结束
      }

      // 创建轮次数据（只使用数据库中存在的字段）
      const roundData = {
        round_number: nextRoundNumber,
        round_name: data.round_name || `第${nextRoundNumber}场秒杀`,
        start_time: this.formatLocalDateTime(startTime),
        end_time: this.formatLocalDateTime(endTime),
        status: status,
        created_by: think.userId || 0
      };

      console.log('准备创建轮次:', roundData);

      // 声明roundId变量在外层作用域
      let roundId;
      try {
        roundId = await roundModel.add(roundData);
        if (!roundId) {
          throw new Error('创建轮次失败');
        }
        console.log('轮次创建成功，ID:', roundId);
      } catch (error) {
        console.error('创建轮次时发生错误:', error);
        console.error('错误详情:', error.message);
        console.error('轮次数据:', roundData);

        // 如果是重复键错误，尝试多次重新获取轮次编号
        if (error.code === 'ER_DUP_ENTRY') {
          console.log('检测到轮次编号重复，尝试重新获取编号...');

          let retrySuccess = false;
          let retryAttempts = 3;

          for (let i = 0; i < retryAttempts; i++) {
            try {
              // 等待一小段时间，避免并发冲突
              await new Promise(resolve => setTimeout(resolve, 200 * (i + 1)));

              // 重新获取最大轮次编号
              const retryMaxResult = await roundModel.field('MAX(round_number) as max_round').find();
              const retryNextNumber = retryMaxResult && retryMaxResult.max_round ?
                parseInt(retryMaxResult.max_round) + 1 :
                Date.now() % 1000000; // 使用时间戳作为后备

              // 更新轮次数据
              roundData.round_number = retryNextNumber;
              roundData.round_name = data.round_name || `第${retryNextNumber}场秒杀`;

              console.log(`重试创建轮次 (第${i + 1}次):`, roundData);
              roundId = await roundModel.add(roundData);

              if (roundId) {
                console.log('重试轮次创建成功，ID:', roundId);
                nextRoundNumber = retryNextNumber; // 更新轮次编号用于后续返回
                retrySuccess = true;
                break;
              }
            } catch (retryError) {
              console.error(`重试创建轮次失败 (第${i + 1}次):`, retryError);
              if (i === retryAttempts - 1) {
                throw new Error(`创建轮次失败，已重试${retryAttempts}次: ${retryError.message}`);
              }
            }
          }

          if (!retrySuccess) {
            throw new Error('创建轮次失败，所有重试都失败了');
          }
        } else {
          throw new Error(`创建轮次失败: ${error.message}`);
        }
      }

      // 获取商品详细信息并创建轮次商品
      const goodsList = [];
      for (const goodsItem of data.goods_list) {
        console.log('处理商品:', goodsItem.goods_id);
        const goods = await goodsModel.where({ id: goodsItem.goods_id }).find();
        if (think.isEmpty(goods)) {
          throw new Error(`商品ID ${goodsItem.goods_id} 不存在`);
        }

        // 计算折扣率
        const originalPrice = parseFloat(goods.retail_price) || 0;
        const flashPrice = parseFloat(goodsItem.flash_price) || 0;
        const discountRate = originalPrice > 0 ? Math.round((1 - flashPrice / originalPrice) * 100) : 0;

        goodsList.push({
          goods_id: goodsItem.goods_id,
          goods_name: goods.name,
          goods_image: goods.list_pic_url || '',
          original_price: originalPrice,
          flash_price: flashPrice,
          discount_rate: discountRate,
          stock: goodsItem.stock,
          limit_quantity: goodsItem.limit_quantity || 1
        });
      }

      console.log('准备添加轮次商品，数量:', goodsList.length);
      // 批量添加轮次商品
      await roundGoodsModel.addRoundGoods(roundId, goodsList);

      const result = {
        id: roundId,
        round_number: nextRoundNumber,
        start_time: roundData.start_time,
        end_time: roundData.end_time,
        goods_count: goodsList.length
      };

      console.log('准备返回成功响应:', result);
      return this.success({
        message: '轮次创建成功',
        data: result
      });

    } catch (error) {
      console.error('创建轮次失败:', error);
      return this.fail('创建失败: ' + error.message);
    }
  }

  /**
   * 获取可选商品列表（排除已参与活跃轮次的商品）
   * GET /admin/flashsalemulti/goods
   */
  async goodsAction() {
    try {
      const goodsModel = this.model('goods');
      const productModel = this.model('product');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      // 获取所有上架商品
      const allGoods = await goodsModel.where({
        is_on_sale: 1,
        is_delete: 0
      }).field('id, name, retail_price, list_pic_url, goods_number').select();

      // 获取已参与活跃轮次的商品ID
      const activeGoodsIds = await roundGoodsModel.alias('rg')
        .join({
          table: 'flash_sale_rounds',
          join: 'inner',
          as: 'r',
          on: ['rg.round_id', 'r.id']
        })
        .where({
          'r.status': ['IN', ['upcoming', 'active']]
        })
        .field('rg.goods_id')
        .select();

      const activeIds = activeGoodsIds.map(item => item.goods_id);

      // 为每个商品获取规格信息和价格区间
      const goodsList = await Promise.all(allGoods.map(async (goods) => {
        // 获取商品的所有规格产品
        const products = await productModel
          .where({
            goods_id: goods.id,
            is_delete: 0
          })
          .field('retail_price, goods_number')
          .select();

        let priceRange = null;
        let totalStock = 0;

        if (products && products.length > 0) {
          const prices = products.map(p => parseFloat(p.retail_price)).filter(p => p > 0);
          totalStock = products.reduce((sum, p) => sum + (p.goods_number || 0), 0);

          if (prices.length > 1) {
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            priceRange = `¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)}`;
          }
        }

        // 使用实际库存或商品表中的库存
        const actualStock = totalStock > 0 ? totalStock : (goods.goods_number || 0);
        const isInFlashSale = activeIds.includes(goods.id);

        return {
          ...goods,
          price_range: priceRange,
          actual_stock: actualStock,
          is_in_flash_sale: isInFlashSale,
          can_select: !isInFlashSale && actualStock > 0,
          warning: actualStock <= 0 ? '库存不足' : (isInFlashSale ? '已参与其他轮次' : null)
        };
      }));

      return this.success(goodsList);

    } catch (error) {
      console.error('获取商品列表失败:', error);
      return this.fail('获取商品列表失败');
    }
  }

  /**
   * 获取/保存系统配置
   */
  async configAction() {
    try {
      const configModel = this.model('flash_sale_config');

      if (this.isGet) {
        // 获取配置
        const config = await configModel.where({ id: 1 }).find();
        return this.success(config || {});
      } else {
        // 保存配置
        const data = this.post();
        await configModel.where({ id: 1 }).update(data);
        return this.success('配置保存成功');
      }

    } catch (error) {
      console.error('配置操作失败:', error);
      return this.fail('配置操作失败');
    }
  }

  /**
   * 手动关闭轮次
   * POST /admin/flashsalemulti/close
   */
  async closeAction() {
    try {
      const roundId = this.post('round_id');
      if (!roundId) {
        return this.fail('请提供轮次ID');
      }

      console.log('=== 手动关闭轮次 ===');
      console.log('轮次ID:', roundId);

      const roundModel = this.model('flash_sale_rounds');

      // 检查轮次是否存在
      const round = await roundModel.where({ id: roundId }).find();
      if (think.isEmpty(round)) {
        return this.fail('轮次不存在');
      }

      // 检查轮次状态
      if (round.status === 'ended') {
        return this.fail('轮次已经结束');
      }

      // 更新轮次状态为已结束，并标记为手动关闭
      await roundModel.where({ id: roundId }).update({
        status: 'ended',
        closed_manually: 1,
        closed_at: this.formatLocalDateTime(new Date()),
        updated_at: this.formatLocalDateTime(new Date())
      });

      console.log('轮次手动关闭成功:', roundId);
      return this.success({
        message: '轮次已关闭',
        data: { id: roundId, status: 'ended', closed_manually: true }
      });

    } catch (error) {
      console.error('关闭轮次失败:', error);
      return this.fail('关闭失败: ' + error.message);
    }
  }

  /**
   * 手动触发状态更新（用于调试）
   * POST /admin/flashsalemulti/updateStatus
   */
  async updateStatusAction() {
    try {
      console.log('🔧 手动触发轮次状态更新...');

      const scheduler = new FlashSaleMultiScheduler();
      const result = await scheduler.updateRoundStatus(this.model.bind(this));

      return this.success({
        message: '状态更新完成',
        result: result
      });

    } catch (error) {
      console.error('手动更新状态失败:', error);
      return this.fail('更新失败: ' + error.message);
    }
  }

  /**
   * 重新启动轮次
   * POST /admin/flashsalemulti/restart
   */
  async restartAction() {
    try {
      const { round_id, new_start_time, new_end_time } = this.post();
      if (!round_id) {
        return this.fail('请提供轮次ID');
      }

      console.log('=== 重新启动轮次 ===');
      console.log('轮次ID:', round_id);

      const roundModel = this.model('flash_sale_rounds');

      // 检查轮次是否存在
      const round = await roundModel.where({ id: round_id }).find();
      if (think.isEmpty(round)) {
        return this.fail('轮次不存在');
      }

      // 只有已结束的轮次才能重新启动
      if (round.status !== 'ended') {
        return this.fail('只有已结束的轮次才能重新启动');
      }

      const updateData = {
        status: 'upcoming',
        closed_manually: 0,
        closed_at: null,
        updated_at: this.formatLocalDateTime(new Date())
      };

      // 如果提供了新的时间，则更新时间
      if (new_start_time) {
        updateData.start_time = this.formatLocalDateTime(new Date(new_start_time));
      }
      if (new_end_time) {
        updateData.end_time = this.formatLocalDateTime(new Date(new_end_time));
      }

      await roundModel.where({ id: round_id }).update(updateData);

      console.log('轮次重新启动成功:', round_id);
      return this.success({
        message: '轮次已重新启动',
        data: { id: round_id, status: 'upcoming' }
      });

    } catch (error) {
      console.error('重新启动轮次失败:', error);
      return this.fail('重新启动失败: ' + error.message);
    }
  }

  /**
   * 延期轮次
   * POST /admin/flashsalemulti/extend
   */
  async extendAction() {
    try {
      const { round_id, extend_minutes } = this.post();
      if (!round_id || !extend_minutes) {
        return this.fail('请提供轮次ID和延期时间');
      }

      console.log('=== 延期轮次 ===');
      console.log('轮次ID:', round_id, '延期分钟:', extend_minutes);

      const roundModel = this.model('flash_sale_rounds');

      // 检查轮次是否存在
      const round = await roundModel.where({ id: round_id }).find();
      if (think.isEmpty(round)) {
        return this.fail('轮次不存在');
      }

      // 只有进行中的轮次才能延期
      if (round.status !== 'active') {
        return this.fail('只有进行中的轮次才能延期');
      }

      // 计算新的结束时间
      const currentEndTime = new Date(round.end_time);
      const newEndTime = new Date(currentEndTime.getTime() + extend_minutes * 60 * 1000);

      await roundModel.where({ id: round_id }).update({
        end_time: this.formatLocalDateTime(newEndTime),
        updated_at: this.formatLocalDateTime(new Date())
      });

      console.log('轮次延期成功:', round_id);
      return this.success({
        message: `轮次已延期${extend_minutes}分钟`,
        data: { id: round_id, new_end_time: newEndTime }
      });

    } catch (error) {
      console.error('延期轮次失败:', error);
      return this.fail('延期失败: ' + error.message);
    }
  }

  /**
   * 复制轮次
   * POST /admin/flashsalemulti/copy
   */
  async copyAction() {
    try {
      const { round_id, new_start_time, new_end_time, new_round_name } = this.post();
      if (!round_id || !new_start_time || !new_end_time) {
        return this.fail('请提供轮次ID和新的时间信息');
      }

      console.log('=== 复制轮次 ===');
      console.log('源轮次ID:', round_id);

      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      // 获取源轮次信息
      const sourceRound = await roundModel.where({ id: round_id }).find();
      if (think.isEmpty(sourceRound)) {
        return this.fail('源轮次不存在');
      }

      // 获取下一个轮次编号
      const lastRound = await roundModel.order('round_number DESC').find();
      const nextRoundNumber = (lastRound && lastRound.round_number) ? lastRound.round_number + 1 : 1;

      // 创建新轮次（只使用数据库中存在的字段）
      const newRoundData = {
        round_number: nextRoundNumber,
        round_name: new_round_name || `${sourceRound.round_name} (复制)`,
        start_time: this.formatLocalDateTime(new Date(new_start_time)),
        end_time: this.formatLocalDateTime(new Date(new_end_time)),
        status: 'upcoming',
        created_by: think.userId || 0
      };

      const newRoundId = await roundModel.add(newRoundData);
      if (!newRoundId) {
        throw new Error('创建新轮次失败');
      }

      // 复制轮次商品
      const sourceGoods = await roundGoodsModel.where({ round_id: round_id }).select();
      if (sourceGoods.length > 0) {
        const newGoodsData = sourceGoods.map(goods => ({
          round_id: newRoundId,
          goods_id: goods.goods_id,
          goods_name: goods.goods_name,
          goods_image: goods.goods_image,
          original_price: goods.original_price,
          flash_price: goods.flash_price,
          discount_rate: goods.discount_rate,
          stock: goods.stock,
          sold_count: 0, // 重置销售数量
          limit_quantity: goods.limit_quantity,
          sort_order: goods.sort_order
        }));

        await roundGoodsModel.addMany(newGoodsData);
      }

      console.log('轮次复制成功:', newRoundId);
      return this.success({
        message: '轮次复制成功',
        data: { id: newRoundId, round_number: nextRoundNumber }
      });

    } catch (error) {
      console.error('复制轮次失败:', error);
      return this.fail('复制失败: ' + error.message);
    }
  }

  /**
   * 删除轮次
   * POST /admin/flashsalemulti/delete
   */
  async deleteAction() {
    try {
      const roundId = this.post('round_id');
      if (!roundId) {
        return this.fail('请提供轮次ID');
      }

      console.log('=== 删除轮次 ===');
      console.log('轮次ID:', roundId);

      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      // 检查轮次是否存在
      const round = await roundModel.where({ id: roundId }).find();
      if (think.isEmpty(round)) {
        return this.fail('轮次不存在');
      }

      // 如果轮次还在进行中或即将开始，先关闭它
      if (round.status === 'active' || round.status === 'upcoming') {
        console.log('轮次状态为:', round.status, '先关闭轮次');
        await roundModel.where({ id: roundId }).update({
          status: 'ended',
          closed_manually: 1,
          closed_at: this.formatLocalDateTime(new Date()),
          updated_at: this.formatLocalDateTime(new Date())
        });
      }

      // 删除轮次相关的商品
      await roundGoodsModel.where({ round_id: roundId }).delete();
      console.log('已删除轮次商品数据');

      // 删除轮次
      await roundModel.where({ id: roundId }).delete();
      console.log('已删除轮次数据');

      return this.success({
        message: '轮次删除成功',
        data: { id: roundId }
      });

    } catch (error) {
      console.error('删除轮次失败:', error);
      return this.fail('删除失败: ' + error.message);
    }
  }

  /**
   * 批量删除轮次
   * POST /admin/flashsalemulti/batchDelete
   */
  async batchDeleteAction() {
    try {
      const roundIds = this.post('round_ids');
      if (!roundIds || !Array.isArray(roundIds) || roundIds.length === 0) {
        return this.fail('请提供要删除的轮次ID列表');
      }

      console.log('=== 批量删除轮次 ===');
      console.log('轮次IDs:', roundIds);

      const roundModel = this.model('flash_sale_rounds');
      const roundGoodsModel = this.model('flash_sale_round_goods');

      let successCount = 0;
      let failCount = 0;
      const results = [];

      for (const roundId of roundIds) {
        try {
          // 检查轮次是否存在
          const round = await roundModel.where({ id: roundId }).find();
          if (think.isEmpty(round)) {
            failCount++;
            results.push({ id: roundId, success: false, message: '轮次不存在' });
            continue;
          }

          // 如果轮次还在进行中或即将开始，先关闭它
          if (round.status === 'active' || round.status === 'upcoming') {
            await roundModel.where({ id: roundId }).update({
              status: 'ended',
              closed_manually: 1,
              closed_at: this.formatLocalDateTime(new Date()),
              updated_at: this.formatLocalDateTime(new Date())
            });
          }

          // 删除轮次相关的商品
          await roundGoodsModel.where({ round_id: roundId }).delete();

          // 删除轮次
          await roundModel.where({ id: roundId }).delete();

          successCount++;
          results.push({ id: roundId, success: true, message: '删除成功' });

        } catch (error) {
          failCount++;
          results.push({ id: roundId, success: false, message: error.message });
        }
      }

      return this.success({
        message: `批量删除完成：成功 ${successCount} 个，失败 ${failCount} 个`,
        data: {
          successCount,
          failCount,
          results
        }
      });

    } catch (error) {
      console.error('批量删除轮次失败:', error);
      return this.fail('批量删除失败: ' + error.message);
    }
  }

  /**
   * 获取时间调试信息
   * GET /admin/flashsalemulti/timeDebug
   */
  async timeDebugAction() {
    try {
      const scheduler = new FlashSaleMultiScheduler();
      const currentTime = scheduler.getCurrentLocalTimeString();
      const now = new Date();

      // 获取所有upcoming轮次
      const upcomingRounds = await this.model('flash_sale_rounds').where({
        status: 'upcoming'
      }).select();

      const debugInfo = {
        current_local_time: currentTime,
        current_js_time: now.toLocaleString('zh-CN'),
        current_utc_time: now.toISOString(),
        upcoming_rounds: upcomingRounds.map(r => ({
          id: r.id,
          round_number: r.round_number,
          round_name: r.round_name,
          start_time: r.start_time,
          end_time: r.end_time,
          status: r.status,
          should_be_active: r.start_time <= currentTime,
          should_be_ended: r.end_time < currentTime
        }))
      };

      return this.success(debugInfo);

    } catch (error) {
      console.error('获取调试信息失败:', error);
      return this.fail('获取调试信息失败: ' + error.message);
    }
  }

  /**
   * 手动更新轮次状态
   */
  async updateStatusAction() {
    try {
      console.log('🔄 手动触发轮次状态更新...');

      const FlashSaleMultiScheduler = require('../../common/service/flash_sale_multi_scheduler');
      const scheduler = new FlashSaleMultiScheduler();

      // 更新轮次状态
      const result = await scheduler.updateRoundStatus(this.model.bind(this));

      // 获取统计信息
      const stats = await scheduler.getActiveRoundsStats(this.model.bind(this));

      return this.success({
        message: '轮次状态更新完成',
        data: {
          updated: result,
          stats: stats
        }
      });

    } catch (error) {
      console.error('❌ 更新轮次状态失败:', error);
      return this.fail('更新状态失败: ' + error.message);
    }
  }

};
