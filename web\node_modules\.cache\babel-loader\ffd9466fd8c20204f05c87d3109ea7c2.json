{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue", "mtime": 1754305493472}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8fA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEAC;YAAA;cAAAC;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAAhC;cACA;cAAA;cAEAiC;gBACAjC;gBACAC;gBACAiC;gBACAZ;cACA;cAAA;cAAA,OAEAO;YAAA;cAAAC;cACA;gBACA;gBACA;kBACA9B;kBACAC;kBACAC;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA6B;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEAN;YAAA;cAAAC;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAAzC;cACA;cAAA;cAEAiC;gBACAjC;gBACAC;gBACAiC;gBACAQ;cACA;cAAA;cAAA,OAEAb;YAAA;cAAAC;cACA;gBACA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MACAN;MACA;QACA;MACA;IACA;IAEA;IACAO;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cACA;cAAA;cAAA;cAAA,OAGAhB;YAAA;cAAAC;cACA;gBACAgB;gBAEA;kBACA;kBACA;oBACAhC;oBACAiC;oBACAC;oBACAC;oBACAlC;oBACAmC;oBACAC;oBACAC;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEArB;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MAAA;MACA;QACAC;QACAC;UACA,iBACAC;YAAAC;UAAA,wEACAD;YAAAC;UAAA;YAAA,OACAD;cACAE;cACAD;cACAE;gBACAC;kBACA;oBACA9C;oBACAiC;oBACAC;oBACAC;oBACAlC;oBACAmC;oBACAC;oBACAC;kBACA;kBACA;gBACA;cACA;YACA,IACAI;cAAAC;YAAA,IACAD;cAAAC;YAAA,0CACAD;cAAAC;YAAA,wCACA,GACAD;cAAAC;YAAA,kDACA;UAAA,EACA,EACA;QACA;QACAI;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACApD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA2C;MACA;MACA;QACArD;QACAiC;QACAC;QACAC;QACAlC;QACAmC;QACAC;QACAC;MACA;MAEA;QACAtC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;IACA;IAEA;IACA4C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACA;cAAA;YAAA;cAIA;cAAA;cAAA,KAGA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACAvC;YAAA;cAAAC;cAAA;cAAA;YAAA;cAAA;cAAA,OAEAD;YAAA;cAAAC;YAAA;cAAA,MAGAA;gBAAA;gBAAA;cAAA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAC;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cACAC;cACAC;cAAA;cAAA;cAAA,OAGA;YAAA;cAAAC;cAAA,KACAA;gBAAA;gBAAA;cAAA;cACA;cAAA;cAAA,OACA3C;YAAA;cAAAC;cAAA,MAEAA;gBAAA;gBAAA;cAAA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAIA;gBACAC;gBACA;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAD;cAAA,KACAA;gBAAA;gBAAA;cAAA;cACA;cAAA;cAAA,OACA3C;YAAA;cAAAC;cAAA,MAEAA;gBAAA;gBAAA;cAAA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAIA;gBACAC;gBACA;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA", "names": ["name", "data", "loading", "statistics", "pointsGoodsList", "categories", "availableGoods", "selectedProduct", "pagination", "page", "limit", "total", "searchKeyword", "statusFilter", "showGoodsSelector", "goodsSearchKeyword", "goodsSearchCategory", "goodsPage", "goodsLimit", "goodsTotal", "showModal", "editingGoods", "formData", "goods_id", "product_id", "points_price", "cash_price", "stock_limit", "daily_limit", "user_limit", "sort", "status", "is_hot", "description", "mounted", "methods", "init", "loadStatistics", "pointsGoodsApi", "response", "console", "loadPointsGoods", "params", "keyword", "loadCategories", "searchGoods", "clearTimeout", "changePage", "openGoodsSelector", "closeGoodsSelector", "loadAvailableGoods", "category_id", "searchAvailableGoods", "changeGoodsPage", "selectGoods", "products", "goods_name", "goods_image", "goods_brief", "original_price", "stock", "specification_info", "showProductSelector", "title", "content", "h", "class", "key", "on", "click", "showCancelButton", "showConfirmButton", "customClass", "openModal", "closeModal", "resetForm", "editPointsGoods", "savePointsGoods", "toggleStatus", "newStatus", "action", "confirmed", "deletePointsGoods"], "sourceRoot": "src/components/Marketing", "sources": ["PointsGoodsPage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"p-6\">\r\n    <!-- 页面标题 -->\r\n    <div class=\"mb-6\">\r\n      <h1 class=\"text-2xl font-bold text-gray-900\">积分兑好礼管理</h1>\r\n      <p class=\"text-gray-600 mt-1\">从商品库中选择商品规格，设置积分兑换条件</p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-blue-100 rounded-lg\">\r\n            <i class=\"ri-gift-line text-2xl text-blue-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">总商品数</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_goods || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-green-100 rounded-lg\">\r\n            <i class=\"ri-check-line text-2xl text-green-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">上架商品</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.online_goods || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-yellow-100 rounded-lg\">\r\n            <i class=\"ri-shopping-cart-line text-2xl text-yellow-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">兑换订单</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_orders || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"bg-white rounded-lg shadow p-6\">\r\n        <div class=\"flex items-center\">\r\n          <div class=\"p-2 bg-red-100 rounded-lg\">\r\n            <i class=\"ri-coin-line text-2xl text-red-600\"></i>\r\n          </div>\r\n          <div class=\"ml-4\">\r\n            <p class=\"text-sm font-medium text-gray-600\">消耗积分</p>\r\n            <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.total_points || 0 }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 操作栏 -->\r\n    <div class=\"bg-white rounded-lg shadow mb-6\">\r\n      <div class=\"px-6 py-4 border-b border-gray-200\">\r\n        <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0\">\r\n          <div class=\"flex items-center space-x-4\">\r\n            <button\r\n              @click=\"openGoodsSelector\"\r\n              class=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            >\r\n              <i class=\"ri-add-line mr-2\"></i>\r\n              添加积分商品\r\n            </button>\r\n          </div>\r\n\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"relative\">\r\n              <input\r\n                v-model=\"searchKeyword\"\r\n                @input=\"searchGoods\"\r\n                type=\"text\"\r\n                placeholder=\"搜索商品名称...\"\r\n                class=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              />\r\n              <i class=\"ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"></i>\r\n            </div>\r\n\r\n            <select\r\n              v-model=\"statusFilter\"\r\n              @change=\"loadPointsGoods\"\r\n              class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            >\r\n              <option value=\"\">全部状态</option>\r\n              <option value=\"1\">上架</option>\r\n              <option value=\"0\">下架</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 商品列表 -->\r\n      <div class=\"overflow-x-auto\">\r\n        <table class=\"min-w-full divide-y divide-gray-200\">\r\n          <thead class=\"bg-gray-50\">\r\n            <tr>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                商品信息\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                规格信息\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                积分价格\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                库存/销量\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                状态\r\n              </th>\r\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                操作\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody class=\"bg-white divide-y divide-gray-200\">\r\n            <tr v-for=\"item in pointsGoodsList\" :key=\"item.id\" class=\"hover:bg-gray-50\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-shrink-0 h-16 w-16\">\r\n                    <img\r\n                      :src=\"item.goods_image || '/static/images/default-goods.png'\"\r\n                      :alt=\"item.goods_name\"\r\n                      class=\"h-16 w-16 rounded-lg object-cover\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"ml-4\">\r\n                    <div class=\"text-sm font-medium text-gray-900 max-w-xs truncate\">\r\n                      {{ item.goods_name }}\r\n                    </div>\r\n                    <div class=\"text-sm text-gray-500\">\r\n                      商品ID: {{ item.goods_id }}\r\n                    </div>\r\n                    <div v-if=\"item.is_hot\" class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1\">\r\n                      热门\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">\r\n                  {{ item.specification_info || '默认规格' }}\r\n                </div>\r\n                <div class=\"text-sm text-gray-500\">\r\n                  规格ID: {{ item.product_id }}\r\n                </div>\r\n                <div class=\"text-sm text-gray-500\">\r\n                  原价: ¥{{ item.original_price }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm font-medium text-red-600\">\r\n                  {{ item.points_price }} 积分\r\n                </div>\r\n                <div v-if=\"item.cash_price > 0\" class=\"text-sm text-gray-500\">\r\n                  + ¥{{ item.cash_price }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\r\n                <div>库存: {{ item.stock_limit > 0 ? `${item.stock_limit - item.sold_count}/${item.stock_limit}` : item.stock }}</div>\r\n                <div>销量: {{ item.sold_count }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <span\r\n                  :class=\"[\r\n                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\r\n                    item.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\r\n                  ]\"\r\n                >\r\n                  {{ item.status === 1 ? '上架' : '下架' }}\r\n                </span>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\r\n                <button\r\n                  @click=\"editPointsGoods(item)\"\r\n                  class=\"text-blue-600 hover:text-blue-900\"\r\n                >\r\n                  编辑\r\n                </button>\r\n                <button\r\n                  @click=\"toggleStatus(item)\"\r\n                  :class=\"[\r\n                    'hover:opacity-75',\r\n                    item.status === 1 ? 'text-red-600' : 'text-green-600'\r\n                  ]\"\r\n                >\r\n                  {{ item.status === 1 ? '下架' : '上架' }}\r\n                </button>\r\n                <button\r\n                  @click=\"deletePointsGoods(item)\"\r\n                  class=\"text-red-600 hover:text-red-900\"\r\n                >\r\n                  删除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分页 -->\r\n      <div v-if=\"pagination.total > 0\" class=\"px-6 py-4 border-t border-gray-200\">\r\n        <div class=\"flex items-center justify-between\">\r\n          <div class=\"text-sm text-gray-700\">\r\n            显示 {{ (pagination.page - 1) * pagination.limit + 1 }} 到\r\n            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，\r\n            共 {{ pagination.total }} 条记录\r\n          </div>\r\n          <div class=\"flex items-center space-x-2\">\r\n            <button\r\n              @click=\"changePage(pagination.page - 1)\"\r\n              :disabled=\"pagination.page <= 1\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              上一页\r\n            </button>\r\n            <span class=\"px-3 py-1 text-sm\">\r\n              第 {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }} 页\r\n            </span>\r\n            <button\r\n              @click=\"changePage(pagination.page + 1)\"\r\n              :disabled=\"pagination.page >= Math.ceil(pagination.total / pagination.limit)\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              下一页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 空状态 -->\r\n      <div v-if=\"pointsGoodsList.length === 0 && !loading\" class=\"text-center py-12\">\r\n        <i class=\"ri-gift-line text-6xl text-gray-300 mb-4\"></i>\r\n        <p class=\"text-gray-500 text-lg mb-2\">暂无积分商品</p>\r\n        <p class=\"text-gray-400 text-sm\">点击\"添加积分商品\"开始配置</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 商品选择器弹窗 -->\r\n    <div v-if=\"showGoodsSelector\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden\">\r\n        <div class=\"px-6 py-4 border-b border-gray-200 flex items-center justify-between\">\r\n          <h3 class=\"text-lg font-medium text-gray-900\">选择商品规格</h3>\r\n          <button @click=\"closeGoodsSelector\" class=\"text-gray-400 hover:text-gray-600\">\r\n            <i class=\"ri-close-line text-2xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\">\r\n          <!-- 搜索栏 -->\r\n          <div class=\"mb-6 flex items-center space-x-4\">\r\n            <div class=\"relative flex-1\">\r\n              <input\r\n                v-model=\"goodsSearchKeyword\"\r\n                @input=\"searchAvailableGoods\"\r\n                type=\"text\"\r\n                placeholder=\"搜索商品名称...\"\r\n                class=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              />\r\n              <i class=\"ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"></i>\r\n            </div>\r\n\r\n            <select\r\n              v-model=\"goodsSearchCategory\"\r\n              @change=\"searchAvailableGoods\"\r\n              class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            >\r\n              <option value=\"\">全部分类</option>\r\n              <option v-for=\"category in categories\" :key=\"category.id\" :value=\"category.id\">\r\n                {{ category.name }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n\r\n          <!-- 商品网格 -->\r\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            <div\r\n              v-for=\"goods in availableGoods\"\r\n              :key=\"goods.id\"\r\n              class=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer\"\r\n              @click=\"selectGoods(goods)\"\r\n            >\r\n              <div class=\"flex items-start space-x-4\">\r\n                <img\r\n                  :src=\"goods.list_pic_url || '/static/images/default-goods.png'\"\r\n                  :alt=\"goods.name\"\r\n                  class=\"w-16 h-16 rounded-lg object-cover flex-shrink-0\"\r\n                />\r\n                <div class=\"flex-1 min-w-0\">\r\n                  <h4 class=\"text-sm font-medium text-gray-900 truncate\">{{ goods.name }}</h4>\r\n                  <p class=\"text-xs text-gray-500 mt-1 line-clamp-2\">{{ goods.goods_brief }}</p>\r\n                  <div class=\"mt-2 text-xs text-gray-600\">\r\n                    <div>商品ID: {{ goods.id }}</div>\r\n                    <div>规格数: {{ goods.product_count || 0 }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 商品分页 -->\r\n          <div v-if=\"goodsTotal > goodsLimit\" class=\"mt-6 flex items-center justify-center space-x-2\">\r\n            <button\r\n              @click=\"changeGoodsPage(goodsPage - 1)\"\r\n              :disabled=\"goodsPage <= 1\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              上一页\r\n            </button>\r\n            <span class=\"px-3 py-1 text-sm\">\r\n              第 {{ goodsPage }} / {{ Math.ceil(goodsTotal / goodsLimit) }} 页\r\n            </span>\r\n            <button\r\n              @click=\"changeGoodsPage(goodsPage + 1)\"\r\n              :disabled=\"goodsPage >= Math.ceil(goodsTotal / goodsLimit)\"\r\n              class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              下一页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 规格选择弹窗 -->\r\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\r\n        <div class=\"px-6 py-4 border-b border-gray-200 flex items-center justify-between\">\r\n          <h3 class=\"text-lg font-medium text-gray-900\">\r\n            {{ editingGoods ? '编辑积分商品' : '添加积分商品' }}\r\n          </h3>\r\n          <button @click=\"closeModal\" class=\"text-gray-400 hover:text-gray-600\">\r\n            <i class=\"ri-close-line text-2xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"p-6 overflow-y-auto max-h-[calc(90vh-120px)]\">\r\n          <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            <!-- 商品信息 -->\r\n            <div class=\"space-y-4\">\r\n              <h4 class=\"text-md font-medium text-gray-900\">商品信息</h4>\r\n\r\n              <div v-if=\"selectedProduct\" class=\"border border-gray-200 rounded-lg p-4\">\r\n                <div class=\"flex items-start space-x-4\">\r\n                  <img\r\n                    :src=\"selectedProduct.goods_image || '/static/images/default-goods.png'\"\r\n                    :alt=\"selectedProduct.goods_name\"\r\n                    class=\"w-20 h-20 rounded-lg object-cover flex-shrink-0\"\r\n                  />\r\n                  <div class=\"flex-1\">\r\n                    <h5 class=\"text-sm font-medium text-gray-900\">{{ selectedProduct.goods_name }}</h5>\r\n                    <p class=\"text-xs text-gray-500 mt-1\">{{ selectedProduct.goods_brief }}</p>\r\n                    <div class=\"mt-2 text-xs text-gray-600\">\r\n                      <div>商品ID: {{ selectedProduct.goods_id }}</div>\r\n                      <div>规格ID: {{ selectedProduct.product_id }}</div>\r\n                      <div>原价: ¥{{ selectedProduct.original_price }}</div>\r\n                      <div>库存: {{ selectedProduct.stock }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 积分设置 -->\r\n            <div class=\"space-y-4\">\r\n              <h4 class=\"text-md font-medium text-gray-900\">积分设置</h4>\r\n\r\n              <div class=\"grid grid-cols-1 gap-4\">\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">积分价格 *</label>\r\n                  <input\r\n                    v-model=\"formData.points_price\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"请输入积分价格\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">现金价格</label>\r\n                  <input\r\n                    v-model=\"formData.cash_price\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    step=\"0.01\"\r\n                    placeholder=\"可选，积分+现金模式\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">库存限制</label>\r\n                  <input\r\n                    v-model=\"formData.stock_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制，使用商品原库存\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">每日限购</label>\r\n                  <input\r\n                    v-model=\"formData.daily_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">用户限购</label>\r\n                  <input\r\n                    v-model=\"formData.user_limit\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"0表示不限制\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">排序</label>\r\n                  <input\r\n                    v-model=\"formData.sort\"\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    placeholder=\"数字越大排序越靠前\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div class=\"flex items-center space-x-6\">\r\n                  <label class=\"flex items-center\">\r\n                    <input\r\n                      v-model=\"formData.status\"\r\n                      type=\"checkbox\"\r\n                      :true-value=\"1\"\r\n                      :false-value=\"0\"\r\n                      class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span class=\"ml-2 text-sm text-gray-700\">立即上架</span>\r\n                  </label>\r\n\r\n                  <label class=\"flex items-center\">\r\n                    <input\r\n                      v-model=\"formData.is_hot\"\r\n                      type=\"checkbox\"\r\n                      :true-value=\"1\"\r\n                      :false-value=\"0\"\r\n                      class=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span class=\"ml-2 text-sm text-gray-700\">热门商品</span>\r\n                  </label>\r\n                </div>\r\n\r\n                <div>\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">商品描述</label>\r\n                  <textarea\r\n                    v-model=\"formData.description\"\r\n                    rows=\"3\"\r\n                    placeholder=\"可选，积分商品的特殊描述\"\r\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  ></textarea>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mt-6 flex items-center justify-end space-x-3\">\r\n            <button\r\n              @click=\"closeModal\"\r\n              class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\r\n            >\r\n              取消\r\n            </button>\r\n            <button\r\n              @click=\"savePointsGoods\"\r\n              :disabled=\"!formData.points_price || loading\"\r\n              class=\"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {{ editingGoods ? '更新' : '添加' }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div class=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\r\n        <div class=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\r\n        <span class=\"text-gray-700\">加载中...</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport pointsGoodsApi from '@/api/points-goods'\r\n\r\nexport default {\r\n  name: 'PointsGoodsPage',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      statistics: {},\r\n      pointsGoodsList: [],\r\n      categories: [],\r\n      availableGoods: [],\r\n      selectedProduct: null,\r\n\r\n      // 分页\r\n      pagination: {\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n\r\n      // 搜索和筛选\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n\r\n      // 商品选择器\r\n      showGoodsSelector: false,\r\n      goodsSearchKeyword: '',\r\n      goodsSearchCategory: '',\r\n      goodsPage: 1,\r\n      goodsLimit: 12,\r\n      goodsTotal: 0,\r\n\r\n      // 弹窗\r\n      showModal: false,\r\n      editingGoods: null,\r\n\r\n      // 表单数据\r\n      formData: {\r\n        goods_id: '',\r\n        product_id: '',\r\n        points_price: '',\r\n        cash_price: 0,\r\n        stock_limit: 0,\r\n        daily_limit: 0,\r\n        user_limit: 0,\r\n        sort: 0,\r\n        status: 1,\r\n        is_hot: 0,\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.init()\r\n  },\r\n\r\n  methods: {\r\n    async init() {\r\n      await this.loadStatistics()\r\n      await this.loadPointsGoods()\r\n      await this.loadCategories()\r\n    },\r\n\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      try {\r\n        const response = await pointsGoodsApi.getStatistics()\r\n        if (response.errno === 0) {\r\n          this.statistics = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 加载积分商品列表\r\n    async loadPointsGoods(page = 1) {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: page,\r\n          limit: this.pagination.limit,\r\n          keyword: this.searchKeyword,\r\n          status: this.statusFilter\r\n        }\r\n\r\n        const response = await pointsGoodsApi.getList(params)\r\n        if (response.errno === 0) {\r\n          this.pointsGoodsList = response.data.list\r\n          this.pagination = {\r\n            page: response.data.page,\r\n            limit: response.data.limit,\r\n            total: response.data.total\r\n          }\r\n        } else {\r\n          this.$message.error(response.errmsg || '加载积分商品列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载积分商品列表失败:', error)\r\n        this.$message.error('加载积分商品列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 加载分类\r\n    async loadCategories() {\r\n      try {\r\n        const response = await pointsGoodsApi.getCategories()\r\n        if (response.errno === 0) {\r\n          this.categories = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载分类失败:', error)\r\n      }\r\n    },\r\n\r\n    // 搜索商品\r\n    searchGoods() {\r\n      clearTimeout(this.searchTimer)\r\n      this.searchTimer = setTimeout(() => {\r\n        this.loadPointsGoods(1)\r\n      }, 500)\r\n    },\r\n\r\n    // 分页\r\n    changePage(page) {\r\n      if (page >= 1 && page <= Math.ceil(this.pagination.total / this.pagination.limit)) {\r\n        this.loadPointsGoods(page)\r\n      }\r\n    },\r\n\r\n    // 打开商品选择器\r\n    async openGoodsSelector() {\r\n      this.showGoodsSelector = true\r\n      await this.loadAvailableGoods(1)\r\n    },\r\n\r\n    // 关闭商品选择器\r\n    closeGoodsSelector() {\r\n      this.showGoodsSelector = false\r\n      this.goodsSearchKeyword = ''\r\n      this.goodsSearchCategory = ''\r\n      this.goodsPage = 1\r\n      this.availableGoods = []\r\n    },\r\n\r\n    // 加载可选商品\r\n    async loadAvailableGoods(page = 1) {\r\n      this.loading = true\r\n      try {\r\n        const params = {\r\n          page: page,\r\n          limit: this.goodsLimit,\r\n          keyword: this.goodsSearchKeyword,\r\n          category_id: this.goodsSearchCategory\r\n        }\r\n\r\n        const response = await pointsGoodsApi.getAvailableGoods(params)\r\n        if (response.errno === 0) {\r\n          this.availableGoods = response.data.list\r\n          this.goodsPage = response.data.page\r\n          this.goodsTotal = response.data.total\r\n        } else {\r\n          this.$message.error(response.errmsg || '加载可选商品失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载可选商品失败:', error)\r\n        this.$message.error('加载可选商品失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 搜索可选商品\r\n    searchAvailableGoods() {\r\n      clearTimeout(this.goodsSearchTimer)\r\n      this.goodsSearchTimer = setTimeout(() => {\r\n        this.loadAvailableGoods(1)\r\n      }, 500)\r\n    },\r\n\r\n    // 商品分页\r\n    changeGoodsPage(page) {\r\n      if (page >= 1 && page <= Math.ceil(this.goodsTotal / this.goodsLimit)) {\r\n        this.loadAvailableGoods(page)\r\n      }\r\n    },\r\n\r\n    // 选择商品\r\n    async selectGoods(goods) {\r\n      this.loading = true\r\n      try {\r\n        // 获取商品的规格列表\r\n        const response = await pointsGoodsApi.getGoodsProducts(goods.id)\r\n        if (response.errno === 0) {\r\n          const products = response.data\r\n\r\n          if (products.length === 1) {\r\n            // 只有一个规格，直接选择\r\n            this.selectedProduct = {\r\n              goods_id: goods.id,\r\n              goods_name: goods.name,\r\n              goods_image: goods.list_pic_url,\r\n              goods_brief: goods.goods_brief,\r\n              product_id: products[0].id,\r\n              original_price: products[0].retail_price,\r\n              stock: products[0].goods_number,\r\n              specification_info: products[0].specification_info || '默认规格'\r\n            }\r\n            this.openModal()\r\n          } else {\r\n            // 多个规格，显示规格选择\r\n            this.showProductSelector(goods, products)\r\n          }\r\n        } else {\r\n          this.$message.error(response.errmsg || '获取商品规格失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取商品规格失败:', error)\r\n        this.$message.error('获取商品规格失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 显示规格选择器\r\n    showProductSelector(goods, products) {\r\n      this.$confirm({\r\n        title: '选择商品规格',\r\n        content: h => {\r\n          return h('div', [\r\n            h('p', { class: 'mb-4 text-gray-600' }, `请选择\"${goods.name}\"的规格:`),\r\n            h('div', { class: 'space-y-2' }, products.map(product =>\r\n              h('div', {\r\n                key: product.id,\r\n                class: 'border border-gray-200 rounded-lg p-3 cursor-pointer hover:bg-gray-50',\r\n                on: {\r\n                  click: () => {\r\n                    this.selectedProduct = {\r\n                      goods_id: goods.id,\r\n                      goods_name: goods.name,\r\n                      goods_image: goods.list_pic_url,\r\n                      goods_brief: goods.goods_brief,\r\n                      product_id: product.id,\r\n                      original_price: product.retail_price,\r\n                      stock: product.goods_number,\r\n                      specification_info: product.specification_info || '默认规格'\r\n                    }\r\n                    this.openModal()\r\n                  }\r\n                }\r\n              }, [\r\n                h('div', { class: 'flex justify-between items-center' }, [\r\n                  h('span', { class: 'text-sm font-medium' }, product.specification_info || '默认规格'),\r\n                  h('span', { class: 'text-sm text-gray-500' }, `¥${product.retail_price}`)\r\n                ]),\r\n                h('div', { class: 'text-xs text-gray-400 mt-1' }, `库存: ${product.goods_number}`)\r\n              ])\r\n            ))\r\n          ])\r\n        },\r\n        showCancelButton: false,\r\n        showConfirmButton: false,\r\n        customClass: 'product-selector-modal'\r\n      })\r\n    },\r\n\r\n    // 打开弹窗\r\n    openModal() {\r\n      this.showGoodsSelector = false\r\n      this.showModal = true\r\n      this.resetForm()\r\n\r\n      if (this.selectedProduct) {\r\n        this.formData.goods_id = this.selectedProduct.goods_id\r\n        this.formData.product_id = this.selectedProduct.product_id\r\n      }\r\n    },\r\n\r\n    // 关闭弹窗\r\n    closeModal() {\r\n      this.showModal = false\r\n      this.editingGoods = null\r\n      this.selectedProduct = null\r\n      this.resetForm()\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        goods_id: '',\r\n        product_id: '',\r\n        points_price: '',\r\n        cash_price: 0,\r\n        stock_limit: 0,\r\n        daily_limit: 0,\r\n        user_limit: 0,\r\n        sort: 0,\r\n        status: 1,\r\n        is_hot: 0,\r\n        description: ''\r\n      }\r\n    },\r\n\r\n    // 编辑积分商品\r\n    editPointsGoods(item) {\r\n      this.editingGoods = item\r\n      this.selectedProduct = {\r\n        goods_id: item.goods_id,\r\n        goods_name: item.goods_name,\r\n        goods_image: item.goods_image,\r\n        goods_brief: item.goods_brief,\r\n        product_id: item.product_id,\r\n        original_price: item.original_price,\r\n        stock: item.stock,\r\n        specification_info: item.specification_info\r\n      }\r\n\r\n      this.formData = {\r\n        goods_id: item.goods_id,\r\n        product_id: item.product_id,\r\n        points_price: item.points_price,\r\n        cash_price: item.cash_price,\r\n        stock_limit: item.stock_limit,\r\n        daily_limit: item.daily_limit,\r\n        user_limit: item.user_limit,\r\n        sort: item.sort,\r\n        status: item.status,\r\n        is_hot: item.is_hot,\r\n        description: item.description || ''\r\n      }\r\n\r\n      this.showModal = true\r\n    },\r\n\r\n    // 保存积分商品\r\n    async savePointsGoods() {\r\n      if (!this.formData.points_price) {\r\n        this.$message.error('请输入积分价格')\r\n        return\r\n      }\r\n\r\n      this.loading = true\r\n      try {\r\n        let response\r\n        if (this.editingGoods) {\r\n          response = await pointsGoodsApi.update(this.editingGoods.id, this.formData)\r\n        } else {\r\n          response = await pointsGoodsApi.add(this.formData)\r\n        }\r\n\r\n        if (response.errno === 0) {\r\n          this.$message.success(this.editingGoods ? '更新成功' : '添加成功')\r\n          this.closeModal()\r\n          await this.loadPointsGoods()\r\n          await this.loadStatistics()\r\n        } else {\r\n          this.$message.error(response.errmsg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('保存积分商品失败:', error)\r\n        this.$message.error('操作失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 切换状态\r\n    async toggleStatus(item) {\r\n      const newStatus = item.status === 1 ? 0 : 1\r\n      const action = newStatus === 1 ? '上架' : '下架'\r\n\r\n      try {\r\n        const confirmed = await this.$confirm(`确定要${action}这个积分商品吗？`, '确认操作')\r\n        if (confirmed) {\r\n          this.loading = true\r\n          const response = await pointsGoodsApi.updateStatus(item.id, newStatus)\r\n\r\n          if (response.errno === 0) {\r\n            this.$message.success(`${action}成功`)\r\n            await this.loadPointsGoods()\r\n            await this.loadStatistics()\r\n          } else {\r\n            this.$message.error(response.errmsg || `${action}失败`)\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('切换状态失败:', error)\r\n          this.$message.error(`${action}失败`)\r\n        }\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 删除积分商品\r\n    async deletePointsGoods(item) {\r\n      try {\r\n        const confirmed = await this.$confirm('确定要删除这个积分商品吗？删除后不可恢复。', '确认删除')\r\n        if (confirmed) {\r\n          this.loading = true\r\n          const response = await pointsGoodsApi.delete(item.id)\r\n\r\n          if (response.errno === 0) {\r\n            this.$message.success('删除成功')\r\n            await this.loadPointsGoods()\r\n            await this.loadStatistics()\r\n          } else {\r\n            this.$message.error(response.errmsg || '删除失败')\r\n          }\r\n        }\r\n      } catch (error) {\r\n        if (error !== 'cancel') {\r\n          console.error('删除积分商品失败:', error)\r\n          this.$message.error('删除失败')\r\n        }\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>"]}]}