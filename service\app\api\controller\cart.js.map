{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\cart.js"], "names": ["Base", "require", "moment", "pinyin", "module", "exports", "getCart", "type", "userId", "getLoginUserId", "cartList", "model", "where", "user_id", "is_delete", "is_fast", "select", "goodsCount", "goodsAmount", "checkedGoodsCount", "checkedGoodsAmount", "numberChange", "cartItem", "product", "id", "product_id", "find", "think", "isEmpty", "update", "retail_price", "productNum", "goods_number", "seckill_round_id", "seckillGoods", "round_id", "goods_id", "original_price", "discountRate", "flash_price", "Math", "round", "max", "console", "log", "is_on_sale", "checked", "number", "Number", "info", "field", "list_pic_url", "weight_count", "goods_weight", "add_price", "cAmount", "toFixed", "aAmount", "cartTotal", "indexAction", "success", "addAgain", "goodsId", "productId", "currentTime", "parseInt", "Date", "getTime", "goodsInfo", "fail", "productInfo", "cartInfo", "goodsSepcifitionValue", "goods_specification_ids", "split", "getField", "cartData", "goods_sn", "goods_name", "name", "goods_aka", "freight_template_id", "goods_specifition_name_value", "join", "goods_specifition_ids", "add_time", "add", "addAction", "post", "addType", "seckillRoundId", "seckillPrice", "seckillRound", "status", "parseFloat", "stock", "limit_quantity", "userBoughtCount", "sum", "is_seckill", "increment", "updateAction", "checkedAction", "toString", "isChecked", "deleteAction", "goodsCountAction", "checkoutAction", "orderFrom", "get", "addressId", "goodsMoney", "freightPrice", "outStock", "getAgainCart", "checkedGoodsList", "filter", "v", "item", "againGoods", "order_id", "againGoodsCount", "checkedAddress", "allAddresses", "order", "length", "addr", "is_default", "province_id", "cartGoods", "freightTempArray", "freightData", "money", "freight_type", "province_name", "getRegionName", "city_name", "city_id", "district_name", "district_id", "full_region", "ex", "template_id", "area", "freight_price", "groupData", "group_id", "free_by_number", "free_by_money", "templateInfo", "start", "start_fee", "add_fee", "goodsTotalPrice", "orderTotalPrice", "def", "actualPrice", "num", "testSeckillAddAction", "message", "cart_data", "error", "updateCartTableAction", "sql1", "sql2", "execute", "e", "sql"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,SAASF,QAAQ,QAAR,CAAf;AACAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;AAC1BM,WAAN,CAAcC,IAAd,EAAoB;AAAA;;AAAA;AACtB,kBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;AACM,gBAAIC,WAAW,EAAf;AACA,gBAAGH,QAAQ,CAAX,EAAa;AACTG,2BAAW,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACtCC,6BAASL,MAD6B;AAEtCM,+BAAW,CAF2B;AAGtCC,6BAAS;AAH6B,iBAAzB,EAIdC,MAJc,EAAjB;AAKH,aAND,MAOI;AACAN,2BAAW,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACtCC,6BAASL,MAD6B;AAEtCM,+BAAW,CAF2B;AAGtCC,6BAAS;AAH6B,iBAAzB,EAIdC,MAJc,EAAjB;AAKH;AACD;AACA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,oBAAoB,CAAxB;AACA,gBAAIC,qBAAqB,CAAzB;AACA,gBAAIC,eAAe,CAAnB;AACA,iBAAK,MAAMC,QAAX,IAAuBZ,QAAvB,EAAiC;AAC7B,oBAAIa,UAAU,MAAM,MAAKZ,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CY,wBAAIF,SAASG,UAD+B;AAE5CX,+BAAW;AAFiC,iBAA5B,EAGjBY,IAHiB,EAApB;AAIA,oBAAIC,MAAMC,OAAN,CAAcL,OAAd,CAAJ,EAA4B;AACxB,0BAAM,MAAKZ,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3BM,mCAAW;AAHgB,qBAAzB,EAIHe,MAJG,CAII;AACNf,mCAAW;AADL,qBAJJ,CAAN;AAOH,iBARD,MAQO;AACH,wBAAIgB,eAAeP,QAAQO,YAA3B;AACA,wBAAIC,aAAaR,QAAQS,YAAzB;;AAEA;AACA,wBAAIV,SAASW,gBAAb,EAA+B;AAC3B,8BAAMC,eAAe,MAAM,MAAKvB,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAClEuB,sCAAUb,SAASW,gBAD+C;AAElEG,sCAAUd,SAASc;AAF+C,yBAA3C,EAGxBV,IAHwB,EAA3B;;AAKA,4BAAI,CAACC,MAAMC,OAAN,CAAcM,YAAd,CAAL,EAAkC;AAC9B;AACA,gCAAIA,aAAaG,cAAb,GAA8B,CAAlC,EAAqC;AACjC,sCAAMC,eAAeJ,aAAaK,WAAb,GAA2BL,aAAaG,cAA7D;AACAP,+CAAeU,KAAKC,KAAL,CAAWlB,QAAQO,YAAR,GAAuBQ,YAAvB,GAAsC,GAAjD,IAAwD,GAAvE;AACAR,+CAAeU,KAAKE,GAAL,CAAS,IAAT,EAAeZ,YAAf,CAAf;AACH,6BAJD,MAIO;AACHA,+CAAeI,aAAaK,WAA5B;AACH;AACDI,oCAAQC,GAAR,CAAa,SAAQtB,SAASc,QAAS,YAAWN,YAAa,EAA/D;AACH;AACJ;AACb;AACY,wBAAIC,cAAc,CAAd,IAAmBR,QAAQsB,UAAR,IAAsB,CAA7C,EAAgD;AAC5C,8BAAM,MAAKlC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,wCAAYH,SAASG,UADM;AAE3BZ,qCAASL,MAFkB;AAG3BsC,qCAAS,CAHkB;AAI3BhC,uCAAW;AAJgB,yBAAzB,EAKHe,MALG,CAKI;AACNiB,qCAAS;AADH,yBALJ,CAAN;AAQAxB,iCAASyB,MAAT,GAAkB,CAAlB;AACH,qBAVD,MAUO,IAAIhB,aAAa,CAAb,IAAkBA,aAAaT,SAASyB,MAA5C,EAAoD;AACvDzB,iCAASyB,MAAT,GAAkBhB,UAAlB;AACAV,uCAAe,CAAf;AACH,qBAHM,MAGA,IAAIU,aAAa,CAAb,IAAkBT,SAASyB,MAAT,IAAmB,CAAzC,EAA4C;AAC/CzB,iCAASyB,MAAT,GAAkB,CAAlB;AACA1B,uCAAe,CAAf;AACH;AACDJ,kCAAcK,SAASyB,MAAvB;AACA7B,mCAAeI,SAASyB,MAAT,GAAkBjB,YAAjC;AACAR,6BAASQ,YAAT,GAAwBA,YAAxB;AACA,wBAAI,CAACH,MAAMC,OAAN,CAAcN,SAASwB,OAAT,IAAoBf,aAAa,CAA/C,CAAL,EAAwD;AACpDZ,6CAAqBG,SAASyB,MAA9B;AACA3B,8CAAsBE,SAASyB,MAAT,GAAkBC,OAAOlB,YAAP,CAAxC;AACH;AACD;AACA,wBAAImB,OAAO,MAAM,MAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvCY,4BAAIF,SAASc;AAD0B,qBAA1B,EAEdc,KAFc,CAER,cAFQ,EAEQxB,IAFR,EAAjB;AAGAJ,6BAAS6B,YAAT,GAAwBF,KAAKE,YAA7B;AACA7B,6BAAS8B,YAAT,GAAwB9B,SAASyB,MAAT,GAAkBC,OAAO1B,SAAS+B,YAAhB,CAA1C;AACA,0BAAM,MAAK1C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3BM,mCAAW;AAHgB,qBAAzB,EAIHe,MAJG,CAII;AACNkB,gCAAQzB,SAASyB,MADX;AAENO,mCAAUxB;AAFJ,qBAJJ,CAAN;AAQH;AACJ;AACD,gBAAIyB,UAAUnC,mBAAmBoC,OAAnB,CAA2B,CAA3B,CAAd;AACA,gBAAIC,UAAUrC,kBAAd;AACA,mBAAO;AACHV,0BAAUA,QADP;AAEHgD,2BAAW;AACPzC,gCAAYA,UADL;AAEPC,iCAAaA,YAAYsC,OAAZ,CAAoB,CAApB,CAFN;AAGPrC,uCAAmBA,iBAHZ;AAIPC,wCAAoBmC,OAJb;AAKP1C,6BAASL,MALF;AAMPa,kCAAcA;AANP;AAFR,aAAP;AAtGgB;AAiHnB;AACD;;;;AAIMsC,eAAN,GAAoB;AAAA;;AAAA;AAChB,mBAAO,OAAKC,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AADgB;AAEnB;AACKuD,YAAN,CAAeC,OAAf,EAAwBC,SAAxB,EAAmChB,MAAnC,EAA2C;AAAA;;AAAA;AAC7C,kBAAMvC,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMuD,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAMC,YAAY,MAAM,OAAKzD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CY,oBAAIsC;AAD0C,aAA1B,EAErBpC,IAFqB,EAAxB;AAGA,gBAAIC,MAAMC,OAAN,CAAcwC,SAAd,KAA4BA,UAAUvB,UAAV,IAAwB,CAAxD,EAA2D;AACvD,uBAAO,OAAKwB,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD;AACA;AACA,kBAAMC,cAAc,MAAM,OAAK3D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAIuC;AAD8C,aAA5B,EAEvBrC,IAFuB,EAA1B;AAGA;AACA,gBAAIC,MAAMC,OAAN,CAAc0C,WAAd,KAA8BA,YAAYtC,YAAZ,GAA2Be,MAA7D,EAAqE;AACjE,uBAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACD;AACA,kBAAME,WAAW,MAAM,OAAK5D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CiB,4BAAYsC,SAFgC;AAG5CjD,2BAAW;AAHiC,aAAzB,EAIpBY,IAJoB,EAAvB;AAKA,gBAAII,eAAewC,YAAYxC,YAA/B;AACA,gBAAIH,MAAMC,OAAN,CAAc2C,QAAd,CAAJ,EAA6B;AACzB;AACA;AACA,oBAAIC,wBAAwB,EAA5B;AACA,oBAAI,CAAC7C,MAAMC,OAAN,CAAc0C,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,4CAAwB,MAAM,OAAK7D,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClEwB,kCAAUkC,YAAYlC,QAD4C;AAElEtB,mCAAW,CAFuD;AAGlEU,4BAAI;AACA,kCAAM8C,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,qBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,sBAAMC,WAAW;AACbxC,8BAAUkC,YAAYlC,QADT;AAEbX,gCAAYsC,SAFC;AAGbc,8BAAUP,YAAYO,QAHT;AAIbC,gCAAYV,UAAUW,IAJT;AAKbC,+BAAWV,YAAYQ,UALV;AAMb;AACAG,yCAAqBb,UAAUa,mBAPlB;AAQb9B,kCAAciB,UAAUjB,YARX;AASbJ,4BAAQA,MATK;AAUblC,6BAASL,MAVI;AAWbsB,kCAAcA,YAXD;AAYbwB,+BAAWxB,YAZE;AAaboD,kDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,2CAAuBd,YAAYG,uBAdtB;AAeb3B,6BAAS,CAfI;AAgBbuC,8BAAUrB;AAhBG,iBAAjB;AAkBA,sBAAM,OAAKrD,KAAL,CAAW,MAAX,EAAmB2E,GAAnB,CAAuBV,QAAvB,CAAN;AACH,aAjCD,MAiCO;AACH;AACA,oBAAIN,YAAYtC,YAAZ,GAA4Be,SAASwB,SAASxB,MAAlD,EAA2D;AACvD,2BAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,sBAAM,OAAK1D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,6BAASL,MADkB;AAE3BiB,gCAAYsC,SAFe;AAG3BjD,+BAAW,CAHgB;AAI3BU,wBAAI+C,SAAS/C;AAJc,iBAAzB,EAKHK,MALG,CAKI;AACNC,kCAAcA,YADR;AAENgB,6BAAS,CAFH;AAGNC,4BAAQA;AAHF,iBALJ,CAAN;AAUH;AAzEsC;AA0E1C;AACD;;;;AAIMwC,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMzB,UAAU,OAAK0B,IAAL,CAAU,SAAV,CAAhB;AACN,kBAAMhF,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMsD,YAAY,OAAKyB,IAAL,CAAU,WAAV,CAAlB;AACA,kBAAMzC,SAAS,OAAKyC,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMC,UAAU,OAAKD,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAME,iBAAiB,OAAKF,IAAL,CAAU,kBAAV,CAAvB;AACA,kBAAMG,eAAe,OAAKH,IAAL,CAAU,eAAV,CAArB;AACA,kBAAMxB,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEAxB,oBAAQC,GAAR,CAAY,UAAZ,EAAwB,EAAEkB,OAAF,EAAWC,SAAX,EAAsBhB,MAAtB,EAA8B0C,OAA9B,EAAuCC,cAAvC,EAAuDC,YAAvD,EAAxB;AACA;AACA,kBAAMvB,YAAY,MAAM,OAAKzD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CY,oBAAIsC;AAD0C,aAA1B,EAErBpC,IAFqB,EAAxB;AAGA,gBAAIC,MAAMC,OAAN,CAAcwC,SAAd,KAA4BA,UAAUvB,UAAV,IAAwB,CAAxD,EAA2D;AACvD,uBAAO,OAAKwB,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD;AACA;AACA,kBAAMC,cAAc,MAAM,OAAK3D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAIuC;AAD8C,aAA5B,EAEvBrC,IAFuB,EAA1B;AAGA;AACA,gBAAIC,MAAMC,OAAN,CAAc0C,WAAd,KAA8BA,YAAYtC,YAAZ,GAA2Be,MAA7D,EAAqE;AACjE,uBAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;;AAED;AACA,gBAAIvC,eAAewC,YAAYxC,YAA/B;AACA,gBAAI4D,cAAJ,EAAoB;AAChB/C,wBAAQC,GAAR,CAAY,cAAZ,EAA4B8C,cAA5B;;AAEA;AACA,sBAAME,eAAe,MAAM,OAAKjF,KAAL,CAAW,mBAAX,EAAgCC,KAAhC,CAAsC;AAC7DY,wBAAIkE,cADyD;AAE7DG,4BAAQ;AAFqD,iBAAtC,EAGxBnE,IAHwB,EAA3B;;AAKA,oBAAIC,MAAMC,OAAN,CAAcgE,YAAd,CAAJ,EAAiC;AAC7B,2BAAO,OAAKvB,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACH;;AAED;AACA,sBAAMnC,eAAe,MAAM,OAAKvB,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAClEuB,8BAAUuD,cADwD;AAElEtD,8BAAU0B;AAFwD,iBAA3C,EAGxBpC,IAHwB,EAA3B;;AAKA,oBAAIC,MAAMC,OAAN,CAAcM,YAAd,CAAJ,EAAiC;AAC7B,2BAAO,OAAKmC,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACH;;AAED;AACA,oBAAIyB,WAAWH,YAAX,MAA6BG,WAAW5D,aAAaK,WAAxB,CAAjC,EAAuE;AACnE,2BAAO,OAAK8B,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;;AAED;AACA,oBAAInC,aAAa6D,KAAb,GAAqBhD,MAAzB,EAAiC;AAC7B,2BAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;;AAED;AACA,oBAAItB,SAASb,aAAa8D,cAA1B,EAA0C;AACtC,2BAAO,OAAK3B,IAAL,CAAU,GAAV,EAAgB,OAAMnC,aAAa8D,cAAe,GAAlD,CAAP;AACH;;AAED;AACA,sBAAMC,kBAAkB,MAAM,OAAKtF,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACnDC,6BAASL,MAD0C;AAEnD4B,8BAAU0B,OAFyC;AAGnD7B,sCAAkByD,cAHiC;AAInD5E,+BAAW;AAJwC,iBAAzB,EAK3BoF,GAL2B,CAKvB,QALuB,CAA9B;;AAOA,oBAAI,CAACD,mBAAmB,CAApB,IAAyBlD,MAAzB,GAAkCb,aAAa8D,cAAnD,EAAmE;AAC/D,2BAAO,OAAK3B,IAAL,CAAU,GAAV,EAAgB,OAAM4B,mBAAmB,CAAE,UAA3C,CAAP;AACH;;AAED;AACAnE,+BAAeI,aAAaK,WAA5B;AACAI,wBAAQC,GAAR,CAAY,gBAAZ,EAA8Bd,YAA9B;AACH;AACD;AACA,kBAAMyC,WAAW,MAAM,OAAK5D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CiB,4BAAYsC,SAFgC;AAG5CjD,2BAAW;AAHiC,aAAzB,EAIpBY,IAJoB,EAAvB;AAKA,gBAAI+D,WAAW,CAAf,EAAkB;AACd,sBAAM,OAAK9E,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BE,+BAAW,CADgB;AAE3BD,6BAASL;AAFkB,iBAAzB,EAGHqB,MAHG,CAGI;AACNiB,6BAAS;AADH,iBAHJ,CAAN;AAMA,oBAAI0B,wBAAwB,EAA5B;AACA,oBAAI,CAAC7C,MAAMC,OAAN,CAAc0C,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,4CAAwB,MAAM,OAAK7D,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClEwB,kCAAUkC,YAAYlC,QAD4C;AAElEtB,mCAAW,CAFuD;AAGlEU,4BAAI;AACA,kCAAM8C,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,qBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,sBAAMC,WAAW;AACbxC,8BAAUkC,YAAYlC,QADT;AAEbX,gCAAYsC,SAFC;AAGbc,8BAAUP,YAAYO,QAHT;AAIbC,gCAAYV,UAAUW,IAJT;AAKbC,+BAAWV,YAAYQ,UALV;AAMb;AACAG,yCAAqBb,UAAUa,mBAPlB;AAQb9B,kCAAciB,UAAUjB,YARX;AASbJ,4BAAQA,MATK;AAUblC,6BAASL,MAVI;AAWbsB,kCAAcA,YAXD;AAYbwB,+BAAWxB,YAZE;AAaboD,kDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,2CAAuBd,YAAYG,uBAdtB;AAeb3B,6BAAS,CAfI;AAgBbuC,8BAAUrB,WAhBG;AAiBbjD,6BAAS,CAjBI;AAkBbkB,sCAAkByD,kBAAkB,IAlBvB;AAmBbS,gCAAYT,iBAAiB,CAAjB,GAAqB;AAnBpB,iBAAjB;AAqBA,sBAAM,OAAK/E,KAAL,CAAW,MAAX,EAAmB2E,GAAnB,CAAuBV,QAAvB,CAAN;AACA,uBAAO,OAAKhB,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH,aAzCD,MAyCO;AACH,oBAAIqB,MAAMC,OAAN,CAAc2C,QAAd,CAAJ,EAA6B;AACzB;AACA;AACA,wBAAIC,wBAAwB,EAA5B;AACA,wBAAI,CAAC7C,MAAMC,OAAN,CAAc0C,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,gDAAwB,MAAM,OAAK7D,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClEwB,sCAAUkC,YAAYlC,QAD4C;AAElEtB,uCAAW,CAFuD;AAGlEU,gCAAI;AACA,sCAAM8C,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,yBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,0BAAMC,WAAW;AACbxC,kCAAUkC,YAAYlC,QADT;AAEbX,oCAAYsC,SAFC;AAGbc,kCAAUP,YAAYO,QAHT;AAIbC,oCAAYV,UAAUW,IAJT;AAKbC,mCAAWV,YAAYQ,UALV;AAMb;AACAG,6CAAqBb,UAAUa,mBAPlB;AAQb9B,sCAAciB,UAAUjB,YARX;AASbJ,gCAAQA,MATK;AAUblC,iCAASL,MAVI;AAWbsB,sCAAcA,YAXD;AAYbwB,mCAAWxB,YAZE;AAaboD,sDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,+CAAuBd,YAAYG,uBAdtB;AAeb3B,iCAAS,CAfI;AAgBbuC,kCAAUrB,WAhBG;AAiBb/B,0CAAkByD,kBAAkB,IAjBvB;AAkBbS,oCAAYT,iBAAiB,CAAjB,GAAqB;AAlBpB,qBAAjB;AAoBA,0BAAM,OAAK/E,KAAL,CAAW,MAAX,EAAmB2E,GAAnB,CAAuBV,QAAvB,CAAN;AACH,iBAnCD,MAmCO;AACH;AACA,wBAAIN,YAAYtC,YAAZ,GAA4Be,SAASwB,SAASxB,MAAlD,EAA2D;AACvD,+BAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,0BAAM,OAAK1D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,iCAASL,MADkB;AAE3BiB,oCAAYsC,SAFe;AAG3BjD,mCAAW,CAHgB;AAI3BU,4BAAI+C,SAAS/C;AAJc,qBAAzB,EAKHK,MALG,CAKI;AACNC,sCAAcA;AADR,qBALJ,CAAN;AAQA,0BAAM,OAAKnB,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,iCAASL,MADkB;AAE3BiB,oCAAYsC,SAFe;AAG3BjD,mCAAW,CAHgB;AAI3BU,4BAAI+C,SAAS/C;AAJc,qBAAzB,EAKH4E,SALG,CAKO,QALP,EAKiBrD,MALjB,CAAN;AAMH;AACD,uBAAO,OAAKa,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH;AA5La;AA6LjB;AACD;AACM+F,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMtC,YAAY,OAAKyB,IAAL,CAAU,WAAV,CAAlB,CADiB,CACyB;AAC1C,kBAAMhE,KAAK,OAAKgE,IAAL,CAAU,IAAV,CAAX,CAFiB,CAEW;AAC5B,kBAAMzC,SAASkB,SAAS,OAAKuB,IAAL,CAAU,QAAV,CAAT,CAAf,CAHiB,CAG6B;AAC9C;AACA,kBAAMlB,cAAc,MAAM,OAAK3D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAIuC,SAD8C;AAElDjD,2BAAW;AAFuC,aAA5B,EAGvBY,IAHuB,EAA1B;AAIA,gBAAIC,MAAMC,OAAN,CAAc0C,WAAd,KAA8BA,YAAYtC,YAAZ,GAA2Be,MAA7D,EAAqE;AACjE,uBAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACD;AACA,kBAAME,WAAW,MAAM,OAAK5D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CY,oBAAIA,EADwC;AAE5CV,2BAAW;AAFiC,aAAzB,EAGpBY,IAHoB,EAAvB;AAIA;AACA,gBAAI6C,SAAS9C,UAAT,KAAwBsC,SAA5B,EAAuC;AACnC,sBAAM,OAAKpD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BY,wBAAIA,EADuB;AAE3BV,+BAAW;AAFgB,iBAAzB,EAGHe,MAHG,CAGI;AACNkB,4BAAQA;AADF,iBAHJ,CAAN;AAMA,uBAAO,OAAKa,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH;AA1BgB;AA2BpB;AACD;AACMgG,iBAAN,GAAsB;AAAA;;AAAA;AACxB,kBAAM9F,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAIsD,YAAY,OAAKyB,IAAL,CAAU,YAAV,EAAwBe,QAAxB,EAAhB;AACA,kBAAMC,YAAY,OAAKhB,IAAL,CAAU,WAAV,CAAlB;AACA,gBAAI7D,MAAMC,OAAN,CAAcmC,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAKM,IAAL,CAAU,MAAV,CAAP;AACH;AACDN,wBAAYA,UAAUW,KAAV,CAAgB,GAAhB,CAAZ;AACA,kBAAM,OAAK/D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,4BAAY;AACR,0BAAMsC;AADE,iBADe;AAI3BlD,yBAASL,MAJkB;AAK3BM,2BAAW;AALgB,aAAzB,EAMHe,MANG,CAMI;AACNiB,yBAASmB,SAASuC,SAAT;AADH,aANJ,CAAN;AASA,mBAAO,OAAK5C,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AAjBkB;AAkBrB;AACD;AACMmG,gBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAI1C,YAAY,OAAKyB,IAAL,CAAU,YAAV,CAAhB;AACN,kBAAMhF,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAIkB,MAAMC,OAAN,CAAcmC,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAKM,IAAL,CAAU,MAAV,CAAP;AACH;AACD,kBAAM,OAAK1D,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,4BAAYsC,SADe;AAE3BlD,yBAASL,MAFkB;AAG3BM,2BAAW;AAHgB,aAAzB,EAIHe,MAJG,CAII;AACNf,2BAAW;AADL,aAJJ,CAAN;AAOA,mBAAO,OAAK8C,OAAL,EAAa,MAAM,OAAKtD,OAAL,CAAa,CAAb,CAAnB,EAAP;AACA;AAdiB;AAepB;AACD;AACMoG,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAM9B,WAAW,MAAM,OAAKtE,OAAL,CAAa,CAAb,CAAvB;AACN,kBAAME,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAM,OAAKE,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,yBAASL,MADkB;AAE3BM,2BAAW,CAFgB;AAG3BC,yBAAS;AAHkB,aAAzB,EAIHc,MAJG,CAII;AACNf,2BAAW;AADL,aAJJ,CAAN;AAOA,mBAAO,OAAK8C,OAAL,CAAa;AAChBF,2BAAW;AACPzC,gCAAY2D,SAASlB,SAAT,CAAmBzC;AADxB;AADK,aAAb,CAAP;AAVqB;AAexB;AACD;;;;AAIM0F,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAM3C,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACN,kBAAM3D,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAImG,YAAY,OAAKC,GAAL,CAAS,WAAT,CAAhB;AACA,kBAAMtG,OAAO,OAAKsG,GAAL,CAAS,MAAT,CAAb,CAJmB,CAIY;AAC/B,kBAAMC,YAAY,OAAKD,GAAL,CAAS,WAAT,CAAlB,CALmB,CAKsB;AACzC,kBAAMpB,UAAU,OAAKoB,GAAL,CAAS,SAAT,CAAhB;AACA,gBAAI5F,aAAa,CAAjB,CAPmB,CAOC;AACpB,gBAAI8F,aAAa,CAAjB,CARmB,CAQC;AACpB,gBAAIC,eAAe,CAAnB;AACA,gBAAIC,WAAW,CAAf;AACA,gBAAIrC,WAAW,EAAf;AACA;AACA,gBAAIrE,QAAQ,CAAZ,EAAe;AACX,oBAAIkF,WAAW,CAAf,EAAkB;AACdb,+BAAW,MAAM,OAAKtE,OAAL,CAAa,CAAb,CAAjB;AACH,iBAFD,MAEO,IAAImF,WAAW,CAAf,EAAkB;AACrBb,+BAAW,MAAM,OAAKtE,OAAL,CAAa,CAAb,CAAjB;AACH,iBAFM,MAEA,IAAImF,WAAW,CAAf,EAAkB;AACrBb,+BAAW,MAAM,OAAKsC,YAAL,CAAkBN,SAAlB,CAAjB;AACH;AACJ;AACD,kBAAMO,mBAAmBvC,SAASlE,QAAT,CAAkB0G,MAAlB,CAAyB,UAASC,CAAT,EAAY;AAC1D,uBAAOA,EAAEvE,OAAF,KAAc,CAArB;AACH,aAFwB,CAAzB;AAGA,iBAAK,MAAMwE,IAAX,IAAmBH,gBAAnB,EAAqC;AACjClG,6BAAaA,aAAaqG,KAAKvE,MAA/B;AACAgE,6BAAaA,aAAaO,KAAKvE,MAAL,GAAcuE,KAAKxF,YAA7C;AACA,oBAAIwF,KAAKtF,YAAL,IAAqB,CAArB,IAA0BsF,KAAKzE,UAAL,IAAmB,CAAjD,EAAoD;AAChDoE,+BAAWjE,OAAOiE,QAAP,IAAmB,CAA9B;AACH;AACJ;AACD,gBAAIxB,WAAW,CAAf,EAAkB;AACd,oBAAI8B,aAAa,MAAM,OAAK5G,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACnD4G,8BAAUZ;AADyC,iBAAhC,EAEpB5F,MAFoB,EAAvB;AAGA,oBAAIyG,kBAAkB,CAAtB;AACA,qBAAK,MAAMH,IAAX,IAAmBC,UAAnB,EAA+B;AAC3BE,sCAAkBA,kBAAkBH,KAAKvE,MAAzC;AACH;AACD,oBAAI9B,cAAcwG,eAAlB,EAAmC;AAC/BR,+BAAW,CAAX;AACH;AACJ;AACD;AACA,gBAAIS,iBAAiB,IAArB;AACA,gBAAIZ,aAAa,EAAb,IAAmBA,aAAa,CAApC,EAAuC;AACnC;;AAEA;AACA,sBAAMa,eAAe,MAAM,OAAKhH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACnDC,6BAASL,MAD0C;AAEnDM,+BAAW;AAFwC,iBAA5B,EAGxB8G,KAHwB,CAGlB,SAHkB,EAGP5G,MAHO,EAA3B;;AAKA,oBAAI2G,aAAaE,MAAb,KAAwB,CAA5B,EAA+B;AAC3B;AACAH,qCAAiB,IAAjB;AACH,iBAHD,MAGO,IAAIC,aAAaE,MAAb,KAAwB,CAA5B,EAA+B;AAClC;AACAH,qCAAiBC,aAAa,CAAb,CAAjB;AACH,iBAHM,MAGA;AACH;AACAD,qCAAiBC,aAAajG,IAAb,CAAkB;AAAA,+BAAQoG,KAAKC,UAAL,KAAoB,CAA5B;AAAA,qBAAlB,CAAjB;;AAEA;AACA,wBAAI,CAACL,cAAL,EAAqB;AACjBA,yCAAiBC,aAAa,CAAb,CAAjB;AACH;AACJ;AACJ,aAxBD,MAwBO;AACH;AACAD,iCAAiB,MAAM,OAAK/G,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CY,wBAAIsF,SAD2C;AAE/CjG,6BAASL,MAFsC;AAG3DM,+BAAU;AAHiD,iBAA5B,EAIpBY,IAJoB,EAAvB;AAKH;AACD,gBAAI,CAACC,MAAMC,OAAN,CAAc8F,cAAd,CAAL,EAAoC;AAChC;AACA;AACA;AACA,oBAAIM,cAAcN,eAAeM,WAAjC;AACA;AACA;AACA,oBAAIC,YAAYd,gBAAhB;AACA,oBAAIe,mBAAmB,MAAM,OAAKvH,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC9DE,+BAAW;AADmD,iBAArC,EAE1BE,MAF0B,EAA7B;AAGA,oBAAImH,cAAc,EAAlB;AACA,qBAAK,MAAMb,IAAX,IAAmBY,gBAAnB,EAAqC;AACjCC,gCAAYb,IAAZ,IAAoB;AAChB9F,4BAAI0G,iBAAiBZ,IAAjB,EAAuB9F,EADX;AAEhBuB,gCAAQ,CAFQ;AAGhBqF,+BAAO,CAHS;AAIhB/E,sCAAc,CAJE;AAKhBgF,sCAAcH,iBAAiBZ,IAAjB,EAAuBe;AALrB,qBAApB;AAOH;AACD;AACA;AACA,qBAAK,MAAMf,IAAX,IAAmBa,WAAnB,EAAgC;AAC5B,yBAAK,MAAM7G,QAAX,IAAuB2G,SAAvB,EAAkC;AAC9B,4BAAIX,KAAK9F,EAAL,IAAWF,SAAS2D,mBAAxB,EAA6C;AACzC;AACAqC,iCAAKvE,MAAL,GAAcuE,KAAKvE,MAAL,GAAczB,SAASyB,MAArC;AACAuE,iCAAKc,KAAL,GAAad,KAAKc,KAAL,GAAa9G,SAASyB,MAAT,GAAkBzB,SAASQ,YAArD;AACAwF,iCAAKjE,YAAL,GAAoBiE,KAAKjE,YAAL,GAAoB/B,SAASyB,MAAT,GAAkBzB,SAAS+B,YAAnE;AACH;AACJ;AACJ;AACDqE,+BAAeY,aAAf,GAA+B,MAAM,OAAK3H,KAAL,CAAW,QAAX,EAAqB4H,aAArB,CAAmCb,eAAeM,WAAlD,CAArC;AACAN,+BAAec,SAAf,GAA2B,MAAM,OAAK7H,KAAL,CAAW,QAAX,EAAqB4H,aAArB,CAAmCb,eAAee,OAAlD,CAAjC;AACAf,+BAAegB,aAAf,GAA+B,MAAM,OAAK/H,KAAL,CAAW,QAAX,EAAqB4H,aAArB,CAAmCb,eAAeiB,WAAlD,CAArC;AACAjB,+BAAekB,WAAf,GAA6BlB,eAAeY,aAAf,GAA+BZ,eAAec,SAA9C,GAA0Dd,eAAegB,aAAtG;AACA,qBAAK,MAAMpB,IAAX,IAAmBa,WAAnB,EAAgC;AAC5B,wBAAIb,KAAKvE,MAAL,IAAe,CAAnB,EAAsB;AAClB;AACH;AACD,wBAAI8F,KAAK,MAAM,OAAKlI,KAAL,CAAW,yBAAX,EAAsCC,KAAtC,CAA4C;AACvDkI,qCAAaxB,KAAK9F,EADqC;AAEvDuH,8BAAMf,WAFiD;AAGvDlH,mCAAW;AAH4C,qBAA5C,EAIZY,IAJY,EAAf;AAKA,wBAAIsH,gBAAgB,CAApB;AACA,wBAAI,CAACrH,MAAMC,OAAN,CAAciH,EAAd,CAAL,EAAwB;AACpB;AACA,4BAAII,YAAY,MAAM,OAAKtI,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAC7DY,gCAAIqH,GAAGK,QADsD;AAE7DpI,uCAAU;AAFmD,yBAA3C,EAGnBY,IAHmB,EAAtB;AAIA;AACA,4BAAIyH,iBAAiBF,UAAUE,cAA/B;AACA,4BAAIC,gBAAgBH,UAAUG,aAA9B;AACA;AACA,4BAAIC,eAAe,MAAM,OAAK1I,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC1DY,gCAAI8F,KAAK9F,EADiD;AAE1DV,uCAAW;AAF+C,yBAArC,EAGtBY,IAHsB,EAAzB;AAIA,4BAAI2G,eAAegB,aAAahB,YAAhC;AACA,4BAAIA,gBAAgB,CAApB,EAAuB;AACnB,gCAAIf,KAAKvE,MAAL,GAAckG,UAAUK,KAA5B,EAAmC;AAAE;AACjCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKvE,MAAL,GAAc,CAAf,IAAoBkG,UAAUO,OAAtF,CAD+B,CACgE;AAClG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ,yBAND,MAMO,IAAIlB,gBAAgB,CAApB,EAAuB;AAC1B,gCAAIf,KAAKjE,YAAL,GAAoB4F,UAAUK,KAAlC,EAAyC;AAAE;AACvCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKjE,YAAL,GAAoB,CAArB,IAA0B4F,UAAUO,OAA5F,CADqC,CACgE;AACxG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ;AACD,4BAAIJ,iBAAiB,CAArB,EAAwB;AACpB,gCAAI7B,KAAKvE,MAAL,IAAeoG,cAAnB,EAAmC;AAC/BH,gDAAgB,CAAhB;AACH;AACJ;AACD,4BAAII,gBAAgB,CAApB,EAAuB;AACnB,gCAAI9B,KAAKc,KAAL,IAAcgB,aAAlB,EAAiC;AAC7BJ,gDAAgB,CAAhB;AACH;AACJ;AACJ,qBAtCD,MAsCO;AACH;AACA,4BAAIC,YAAY,MAAM,OAAKtI,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAC7DkI,yCAAaxB,KAAK9F,EAD2C;AAE7DuH,kCAAM,CAFuD;AAG7DjI,uCAAU;AAHmD,yBAA3C,EAInBY,IAJmB,EAAtB;AAKA,4BAAIyH,iBAAiBF,UAAUE,cAA/B;AACA,4BAAIC,gBAAgBH,UAAUG,aAA9B;AACA,4BAAIC,eAAe,MAAM,OAAK1I,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC1DY,gCAAI8F,KAAK9F,EADiD;AAE1DV,uCAAW;AAF+C,yBAArC,EAGtBY,IAHsB,EAAzB;AAIA,4BAAI2G,eAAegB,aAAahB,YAAhC;AACA,4BAAIA,gBAAgB,CAApB,EAAuB;AACnB,gCAAIf,KAAKvE,MAAL,GAAckG,UAAUK,KAA5B,EAAmC;AAAE;AACjCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKvE,MAAL,GAAc,CAAf,IAAoBkG,UAAUO,OAAtF,CAD+B,CACgE;AAClG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ,yBAND,MAMO,IAAIlB,gBAAgB,CAApB,EAAuB;AAC1B,gCAAIf,KAAKjE,YAAL,GAAoB4F,UAAUK,KAAlC,EAAyC;AAAE;AACvCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKjE,YAAL,GAAoB,CAArB,IAA0B4F,UAAUO,OAA5F,CADqC,CACgE;AACxG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ;AACD,4BAAIJ,iBAAiB,CAArB,EAAwB;AACpB,gCAAI7B,KAAKvE,MAAL,IAAeoG,cAAnB,EAAmC;AAC/BH,gDAAgB,CAAhB;AACH;AACJ;AACD,4BAAII,gBAAgB,CAApB,EAAuB;AACnB,gCAAI9B,KAAKc,KAAL,IAAcgB,aAAlB,EAAiC;AAC7BJ,gDAAgB,CAAhB;AACH;AACJ;AACJ;AACbhC,mCAAeA,eAAegC,aAAf,GAA6BhC,YAA7B,GAA0CgC,aAAzD;AACY;AACA;AACH;AACJ,aA/HD,MA+HO;AACHtB,iCAAiB,CAAjB;AACH;AACD;AACA,gBAAI+B,kBAAkB7E,SAASlB,SAAT,CAAmBtC,kBAAzC,CAjNmB,CAiN0C;AAC7D;AACA,gBAAIgH,QAAQxD,SAASlB,SAAT,CAAmBtC,kBAA/B;AACA,gBAAIsI,kBAAkB,CAAtB;AACA,gBAAIC,MAAM,MAAM,OAAKhJ,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AACzCY,oBAAI;AADqC,aAA7B,EAEbE,IAFa,EAAhB;AAGAgI,8BAAkB1G,OAAOoF,KAAP,IAAgBpF,OAAOgE,YAAP,CAAlC,CAxNmB,CAwNoC;AACvD,kBAAM4C,cAAcF,eAApB,CAzNmB,CAyNkB;AACrC,gBAAIrI,eAAeuD,SAASlB,SAAT,CAAmBrC,YAAtC;AACA,mBAAO,OAAKuC,OAAL,CAAa;AAChB8D,gCAAgBA,cADA;AAEhBV,8BAAcA,YAFE;AAGhBG,kCAAkBA,gBAHF;AAIhBsC,iCAAiBA,eAJD;AAKhBC,iCAAiBA,gBAAgBlG,OAAhB,CAAwB,CAAxB,CALD;AAMhBoG,6BAAaA,YAAYpG,OAAZ,CAAoB,CAApB,CANG;AAOhBvC,4BAAYA,UAPI;AAQhBgG,0BAAUA,QARM;AAShB5F,8BAAcA;AATE,aAAb,CAAP;AA3NmB;AAsOtB;AACK6F,gBAAN,CAAmBN,SAAnB,EAA8B;AAAA;;AAAA;AAChC,kBAAMpG,SAAS,MAAM,QAAKC,cAAL,EAArB;AACM,kBAAM8G,aAAa,MAAM,QAAK5G,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACrD4G,0BAAUZ;AAD2C,aAAhC,EAEtB5F,MAFsB,EAAzB;AAGA,kBAAM,QAAKL,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BE,2BAAW,CADgB;AAE3BD,yBAASL;AAFkB,aAAzB,EAGHqB,MAHG,CAGI;AACNiB,yBAAS;AADH,aAHJ,CAAN;AAMA,iBAAK,MAAMwE,IAAX,IAAmBC,UAAnB,EAA+B;AAC3B,sBAAM,QAAK1D,QAAL,CAAcyD,KAAKlF,QAAnB,EAA6BkF,KAAK7F,UAAlC,EAA8C6F,KAAKvE,MAAnD,CAAN;AACH;AACD,kBAAMrC,WAAW,MAAM,QAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CO,yBAAS,CAFmC;AAG5CD,2BAAW;AAHiC,aAAzB,EAIpBE,MAJoB,EAAvB;AAKA;AACA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,oBAAoB,CAAxB;AACA,gBAAIC,qBAAqB,CAAzB;AACA,iBAAK,MAAME,QAAX,IAAuBZ,QAAvB,EAAiC;AAC7BO,8BAAcK,SAASyB,MAAvB;AACA7B,+BAAeI,SAASyB,MAAT,GAAkBzB,SAASQ,YAA1C;AACA,oBAAI,CAACH,MAAMC,OAAN,CAAcN,SAASwB,OAAvB,CAAL,EAAsC;AAClC3B,yCAAqBG,SAASyB,MAA9B;AACA3B,0CAAsBE,SAASyB,MAAT,GAAkBC,OAAO1B,SAASQ,YAAhB,CAAxC;AACH;AACD;AACA,oBAAImB,OAAO,MAAM,QAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvCY,wBAAIF,SAASc;AAD0B,iBAA1B,EAEdc,KAFc,CAER,sCAFQ,EAEgCxB,IAFhC,EAAjB;AAGA;AACA,oBAAImI,MAAM5G,KAAKjB,YAAf;AACA,oBAAI6H,OAAO,CAAX,EAAc;AACV,0BAAM,QAAKlJ,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3BsC,iCAAS,CAHkB;AAI3BhC,mCAAW;AAJgB,qBAAzB,EAKHe,MALG,CAKI;AACNiB,iCAAS;AADH,qBALJ,CAAN;AAQH;AACDxB,yBAAS6B,YAAT,GAAwBF,KAAKE,YAA7B;AACA7B,yBAASU,YAAT,GAAwBiB,KAAKjB,YAA7B;AACAV,yBAAS8B,YAAT,GAAwB9B,SAASyB,MAAT,GAAkBC,OAAO1B,SAAS+B,YAAhB,CAA1C;AACH;AACD,gBAAIE,UAAUnC,mBAAmBoC,OAAnB,CAA2B,CAA3B,CAAd;AACA,gBAAIC,UAAUrC,kBAAd;AACA,mBAAO;AACHV,0BAAUA,QADP;AAEHgD,2BAAW;AACPzC,gCAAYA,UADL;AAEPC,iCAAaA,YAAYsC,OAAZ,CAAoB,CAApB,CAFN;AAGPrC,uCAAmBA,iBAHZ;AAIPC,wCAAoBmC,OAJb;AAKP1C,6BAASL;AALF;AAFR,aAAP;AArD0B;AA+D7B;;AAED;;;;AAIMsJ,wBAAN,GAA6B;AAAA;;AAAA;AACzB,gBAAI;AACA,sBAAMhG,UAAU,QAAK0B,IAAL,CAAU,SAAV,KAAwB,OAAxC;AACA,sBAAMzB,YAAY,QAAKyB,IAAL,CAAU,WAAV,KAA0B,OAA5C;AACA,sBAAMzC,SAAS,QAAKyC,IAAL,CAAU,QAAV,KAAuB,CAAtC;AACA,sBAAMC,UAAU,QAAKD,IAAL,CAAU,SAAV,KAAwB,CAAxC;AACA,sBAAME,iBAAiB,QAAKF,IAAL,CAAU,kBAAV,KAAiC,EAAxD;AACA,sBAAMG,eAAe,QAAKH,IAAL,CAAU,eAAV,KAA8B,IAAnD;AACA,sBAAMhF,SAAS,CAAf,CAPA,CAOkB;AAClB,sBAAMwD,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEAxB,wBAAQC,GAAR,CAAY,cAAZ,EAA4B,EAAEkB,OAAF,EAAWC,SAAX,EAAsBhB,MAAtB,EAA8B0C,OAA9B,EAAuCC,cAAvC,EAAuDC,YAAvD,EAAqEnF,MAArE,EAA5B;;AAEA;AACA,sBAAM4D,YAAY,MAAM,QAAKzD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CY,wBAAIsC;AAD0C,iBAA1B,EAErBpC,IAFqB,EAAxB;AAGA,oBAAIC,MAAMC,OAAN,CAAcwC,SAAd,KAA4BA,UAAUvB,UAAV,IAAwB,CAAxD,EAA2D;AACvD,2BAAO,QAAKwB,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;;AAED;AACA,sBAAMC,cAAc,MAAM,QAAK3D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,wBAAIuC;AAD8C,iBAA5B,EAEvBrC,IAFuB,EAA1B;;AAIA,oBAAIC,MAAMC,OAAN,CAAc0C,WAAd,KAA8BA,YAAYtC,YAAZ,GAA2Be,MAA7D,EAAqE;AACjE,2BAAO,QAAKsB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;;AAED;AACA,oBAAIvC,eAAewC,YAAYxC,YAA/B;AACA,oBAAI4D,cAAJ,EAAoB;AAChB/C,4BAAQC,GAAR,CAAY,cAAZ,EAA4B8C,cAA5B;;AAEA;AACA,0BAAME,eAAe,MAAM,QAAKjF,KAAL,CAAW,mBAAX,EAAgCC,KAAhC,CAAsC;AAC7DY,4BAAIkE,cADyD;AAE7DG,gCAAQ;AAFqD,qBAAtC,EAGxBnE,IAHwB,EAA3B;;AAKA,wBAAIC,MAAMC,OAAN,CAAcgE,YAAd,CAAJ,EAAiC;AAC7B,+BAAO,QAAKvB,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACH;;AAED;AACA,0BAAMnC,eAAe,MAAM,QAAKvB,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAClEuB,kCAAUuD,cADwD;AAElEtD,kCAAU0B;AAFwD,qBAA3C,EAGxBpC,IAHwB,EAA3B;;AAKA,wBAAIC,MAAMC,OAAN,CAAcM,YAAd,CAAJ,EAAiC;AAC7B,+BAAO,QAAKmC,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACH;;AAED;AACA,wBAAIyB,WAAWH,YAAX,MAA6BG,WAAW5D,aAAaK,WAAxB,CAAjC,EAAuE;AACnE,+BAAO,QAAK8B,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;;AAED;AACA,wBAAInC,aAAa6D,KAAb,GAAqBhD,MAAzB,EAAiC;AAC7B,+BAAO,QAAKsB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;;AAED;AACA,wBAAItB,SAASb,aAAa8D,cAA1B,EAA0C;AACtC,+BAAO,QAAK3B,IAAL,CAAU,GAAV,EAAgB,OAAMnC,aAAa8D,cAAe,GAAlD,CAAP;AACH;;AAED;AACA,0BAAMC,kBAAkB,MAAM,QAAKtF,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACnDC,iCAASL,MAD0C;AAEnD4B,kCAAU0B,OAFyC;AAGnD7B,0CAAkByD,cAHiC;AAInD5E,mCAAW;AAJwC,qBAAzB,EAK3BoF,GAL2B,CAKvB,QALuB,CAA9B;;AAOA,wBAAI,CAACD,mBAAmB,CAApB,IAAyBlD,MAAzB,GAAkCb,aAAa8D,cAAnD,EAAmE;AAC/D,+BAAO,QAAK3B,IAAL,CAAU,GAAV,EAAgB,OAAM4B,mBAAmB,CAAE,UAA3C,CAAP;AACH;;AAED;AACAnE,mCAAeI,aAAaK,WAA5B;AACAI,4BAAQC,GAAR,CAAY,gBAAZ,EAA8Bd,YAA9B;AACH;;AAED;AACA,sBAAM,QAAKnB,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BE,+BAAW,CADgB;AAE3BD,6BAASL;AAFkB,iBAAzB,EAGHqB,MAHG,CAGI;AACNiB,6BAAS;AADH,iBAHJ,CAAN;;AAOA;AACA,sBAAM8B,WAAW;AACbxC,8BAAU0B,OADG;AAEbrC,gCAAYsC,SAFC;AAGbc,8BAAUP,YAAYO,QAHT;AAIbC,gCAAYV,UAAUW,IAJT;AAKbC,+BAAWV,YAAYQ,UALV;AAMbG,yCAAqBb,UAAUa,mBANlB;AAOb9B,kCAAciB,UAAUjB,YAPX;AAQbJ,4BAAQA,MARK;AASblC,6BAASL,MATI;AAUbsB,kCAAcA,YAVD;AAWbwB,+BAAWxB,YAXE;AAYboD,kDAA8B,EAZjB;AAabE,2CAAuBd,YAAYG,uBAbtB;AAcb3B,6BAAS,CAdI;AAebuC,8BAAUrB,WAfG;AAgBbjD,6BAAS,CAhBI;AAiBbkB,sCAAkByD,kBAAkB,IAjBvB;AAkBbS,gCAAYT,iBAAiB,CAAjB,GAAqB;AAlBpB,iBAAjB;;AAqBA/C,wBAAQC,GAAR,CAAY,UAAZ,EAAwBgC,QAAxB;AACA,sBAAM,QAAKjE,KAAL,CAAW,MAAX,EAAmB2E,GAAnB,CAAuBV,QAAvB,CAAN;;AAEA,uBAAO,QAAKhB,OAAL,CAAa;AAChBmG,6BAAS,cADO;AAEhBC,+BAAWpF;AAFK,iBAAb,CAAP;AAKH,aA5HD,CA4HE,OAAOqF,KAAP,EAAc;AACZtH,wBAAQsH,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,uBAAO,QAAK5F,IAAL,CAAU,GAAV,EAAe,WAAW4F,MAAMF,OAAhC,CAAP;AACH;AAhIwB;AAiI5B;;AAED;;;;AAIMG,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMC,OAAQ,wFAAd;AACA,sBAAMC,OAAQ,sFAAd;;AAEA,oBAAI;AACA,0BAAM,QAAKzJ,KAAL,GAAa0J,OAAb,CAAqBF,IAArB,CAAN;AACAxH,4BAAQC,GAAR,CAAY,0BAAZ;AACH,iBAHD,CAGE,OAAO0H,CAAP,EAAU;AACR3H,4BAAQC,GAAR,CAAY,2BAAZ,EAAyC0H,EAAEP,OAA3C;AACH;;AAED,oBAAI;AACA,0BAAM,QAAKpJ,KAAL,GAAa0J,OAAb,CAAqBD,IAArB,CAAN;AACAzH,4BAAQC,GAAR,CAAY,oBAAZ;AACH,iBAHD,CAGE,OAAO0H,CAAP,EAAU;AACR3H,4BAAQC,GAAR,CAAY,qBAAZ,EAAmC0H,EAAEP,OAArC;AACH;;AAED,uBAAO,QAAKnG,OAAL,CAAa;AAChBmG,6BAAS,YADO;AAEhBQ,yBAAK,CAACJ,IAAD,EAAOC,IAAP;AAFW,iBAAb,CAAP;AAIH,aAtBD,CAsBE,OAAOH,KAAP,EAAc;AACZtH,wBAAQsH,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,QAAK5F,IAAL,CAAU,GAAV,EAAe,WAAW4F,MAAMF,OAAhC,CAAP;AACH;AA1ByB;AA2B7B;AA56B+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\cart.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst pinyin = require(\"pinyin\");\nmodule.exports = class extends Base {\n    async getCart(type) {\n\t\tconst userId = await this.getLoginUserId();\n        let cartList = [];\n        if(type == 0){\n            cartList = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0,\n                is_fast: 0,\n            }).select();\n        }\n        else{\n            cartList = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0,\n                is_fast: 1\n            }).select();\n        }\n        // 获取购物车统计信息\n        let goodsCount = 0;\n        let goodsAmount = 0;\n        let checkedGoodsCount = 0;\n        let checkedGoodsAmount = 0;\n        let numberChange = 0;\n        for (const cartItem of cartList) {\n            let product = await this.model('product').where({\n                id: cartItem.product_id,\n                is_delete: 0\n            }).find();\n            if (think.isEmpty(product)) {\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    is_delete: 0,\n                }).update({\n                    is_delete: 1\n                });\n            } else {\n                let retail_price = product.retail_price;\n                let productNum = product.goods_number;\n\n                // 检查是否为秒杀商品，如果是则使用秒杀价格\n                if (cartItem.seckill_round_id) {\n                    const seckillGoods = await this.model('flash_sale_round_goods').where({\n                        round_id: cartItem.seckill_round_id,\n                        goods_id: cartItem.goods_id\n                    }).find();\n\n                    if (!think.isEmpty(seckillGoods)) {\n                        // 计算当前规格的秒杀价格\n                        if (seckillGoods.original_price > 0) {\n                            const discountRate = seckillGoods.flash_price / seckillGoods.original_price;\n                            retail_price = Math.round(product.retail_price * discountRate * 100) / 100;\n                            retail_price = Math.max(0.01, retail_price);\n                        } else {\n                            retail_price = seckillGoods.flash_price;\n                        }\n                        console.log(`购物车商品 ${cartItem.goods_id} 使用秒杀价格: ${retail_price}`);\n                    }\n                }\n\t\t\t\t// 4.14 更新\n                if (productNum <= 0 || product.is_on_sale == 0) {\n                    await this.model('cart').where({\n                        product_id: cartItem.product_id,\n                        user_id: userId,\n                        checked: 1,\n                        is_delete: 0,\n                    }).update({\n                        checked: 0\n                    });\n                    cartItem.number = 0;\n                } else if (productNum > 0 && productNum < cartItem.number) {\n                    cartItem.number = productNum;\n                    numberChange = 1;\n                } else if (productNum > 0 && cartItem.number == 0) {\n                    cartItem.number = 1;\n                    numberChange = 1;\n                }\n                goodsCount += cartItem.number;\n                goodsAmount += cartItem.number * retail_price;\n                cartItem.retail_price = retail_price;\n                if (!think.isEmpty(cartItem.checked && productNum > 0)) {\n                    checkedGoodsCount += cartItem.number;\n                    checkedGoodsAmount += cartItem.number * Number(retail_price);\n                }\n                // 查找商品的图片\n                let info = await this.model('goods').where({\n                    id: cartItem.goods_id\n                }).field('list_pic_url').find();\n                cartItem.list_pic_url = info.list_pic_url;\n                cartItem.weight_count = cartItem.number * Number(cartItem.goods_weight);\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    is_delete: 0,\n                }).update({\n                    number: cartItem.number,\n                    add_price:retail_price\n                })\n            }\n        }\n        let cAmount = checkedGoodsAmount.toFixed(2);\n        let aAmount = checkedGoodsAmount;\n        return {\n            cartList: cartList,\n            cartTotal: {\n                goodsCount: goodsCount,\n                goodsAmount: goodsAmount.toFixed(2),\n                checkedGoodsCount: checkedGoodsCount,\n                checkedGoodsAmount: cAmount,\n                user_id: userId,\n                numberChange: numberChange\n            }\n        };\n    }\n    /**\n     * 获取购物车信息，所有对购物车的增删改操作，都要重新返回购物车的信息\n     * @return {Promise} []\n     */\n    async indexAction() {\n        return this.success(await this.getCart(0));\n    }\n    async addAgain(goodsId, productId, number) {\n\t\tconst userId = await this.getLoginUserId();\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const goodsInfo = await this.model('goods').where({\n            id: goodsId\n        }).find();\n        if (think.isEmpty(goodsInfo) || goodsInfo.is_on_sale == 0) {\n            return this.fail(400, '商品已下架');\n        }\n        // 取得规格的信息,判断规格库存\n        // const productInfo = await this.model('product').where({goods_id: goodsId, id: productId}).find();\n        const productInfo = await this.model('product').where({\n            id: productId\n        }).find();\n        // let productId = productInfo.id;\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n        // 判断购物车中是否存在此规格商品\n        const cartInfo = await this.model('cart').where({\n            user_id: userId,\n            product_id: productId,\n            is_delete: 0\n        }).find();\n        let retail_price = productInfo.retail_price;\n        if (think.isEmpty(cartInfo)) {\n            // 添加操作\n            // 添加规格名和值\n            let goodsSepcifitionValue = [];\n            if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                goodsSepcifitionValue = await this.model('goods_specification').where({\n                    goods_id: productInfo.goods_id,\n                    is_delete: 0,\n                    id: {\n                        'in': productInfo.goods_specification_ids.split('_')\n                    }\n                }).getField('value');\n            }\n            // 添加到购物车\n            const cartData = {\n                goods_id: productInfo.goods_id,\n                product_id: productId,\n                goods_sn: productInfo.goods_sn,\n                goods_name: goodsInfo.name,\n                goods_aka: productInfo.goods_name,\n                // goods_weight: 移除重量字段\n                freight_template_id: goodsInfo.freight_template_id,\n                list_pic_url: goodsInfo.list_pic_url,\n                number: number,\n                user_id: userId,\n                retail_price: retail_price,\n                add_price: retail_price,\n                goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                goods_specifition_ids: productInfo.goods_specification_ids,\n                checked: 1,\n                add_time: currentTime\n            };\n            await this.model('cart').add(cartData);\n        } else {\n            // 如果已经存在购物车中，则数量增加\n            if (productInfo.goods_number < (number + cartInfo.number)) {\n                return this.fail(400, '库存都不够啦');\n            }\n            await this.model('cart').where({\n                user_id: userId,\n                product_id: productId,\n                is_delete: 0,\n                id: cartInfo.id\n            }).update({\n                retail_price: retail_price,\n                checked: 1,\n                number: number\n            });\n        }\n    }\n    /**\n     * 添加商品到购物车\n     * @returns {Promise.<*>}\n     */\n    async addAction() {\n        const goodsId = this.post('goodsId');\n\t\tconst userId = await this.getLoginUserId();\n        const productId = this.post('productId');\n        const number = this.post('number');\n        const addType = this.post('addType');\n        const seckillRoundId = this.post('seckill_round_id');\n        const seckillPrice = this.post('seckill_price');\n        const currentTime = parseInt(new Date().getTime() / 1000);\n\n        console.log('购物车添加参数:', { goodsId, productId, number, addType, seckillRoundId, seckillPrice });\n        // 判断商品是否可以购买\n        const goodsInfo = await this.model('goods').where({\n            id: goodsId\n        }).find();\n        if (think.isEmpty(goodsInfo) || goodsInfo.is_on_sale == 0) {\n            return this.fail(400, '商品已下架');\n        }\n        // 取得规格的信息,判断规格库存\n        // const productInfo = await this.model('product').where({goods_id: goodsId, id: productId}).find();\n        const productInfo = await this.model('product').where({\n            id: productId\n        }).find();\n        // let productId = productInfo.id;\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n\n        // 秒杀商品验证\n        let retail_price = productInfo.retail_price;\n        if (seckillRoundId) {\n            console.log('验证秒杀商品，轮次ID:', seckillRoundId);\n\n            // 验证秒杀轮次是否存在且有效\n            const seckillRound = await this.model('flash_sale_rounds').where({\n                id: seckillRoundId,\n                status: 'active'\n            }).find();\n\n            if (think.isEmpty(seckillRound)) {\n                return this.fail(400, '秒杀活动不存在或已结束');\n            }\n\n            // 验证商品是否在该秒杀轮次中\n            const seckillGoods = await this.model('flash_sale_round_goods').where({\n                round_id: seckillRoundId,\n                goods_id: goodsId\n            }).find();\n\n            if (think.isEmpty(seckillGoods)) {\n                return this.fail(400, '该商品不在此秒杀轮次中');\n            }\n\n            // 验证秒杀价格\n            if (parseFloat(seckillPrice) !== parseFloat(seckillGoods.flash_price)) {\n                return this.fail(400, '秒杀价格不正确');\n            }\n\n            // 验证秒杀库存\n            if (seckillGoods.stock < number) {\n                return this.fail(400, '秒杀库存不足');\n            }\n\n            // 验证限购数量\n            if (number > seckillGoods.limit_quantity) {\n                return this.fail(400, `每人限购${seckillGoods.limit_quantity}件`);\n            }\n\n            // 检查用户已购买数量\n            const userBoughtCount = await this.model('cart').where({\n                user_id: userId,\n                goods_id: goodsId,\n                seckill_round_id: seckillRoundId,\n                is_delete: 0\n            }).sum('number');\n\n            if ((userBoughtCount || 0) + number > seckillGoods.limit_quantity) {\n                return this.fail(400, `您已购买${userBoughtCount || 0}件，超出限购数量`);\n            }\n\n            // 使用秒杀价格\n            retail_price = seckillGoods.flash_price;\n            console.log('秒杀验证通过，使用秒杀价格:', retail_price);\n        }\n        // 判断购物车中是否存在此规格商品\n        const cartInfo = await this.model('cart').where({\n            user_id: userId,\n            product_id: productId,\n            is_delete: 0\n        }).find();\n        if (addType == 1) {\n            await this.model('cart').where({\n                is_delete: 0,\n                user_id: userId\n            }).update({\n                checked: 0\n            });\n            let goodsSepcifitionValue = [];\n            if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                goodsSepcifitionValue = await this.model('goods_specification').where({\n                    goods_id: productInfo.goods_id,\n                    is_delete: 0,\n                    id: {\n                        'in': productInfo.goods_specification_ids.split('_')\n                    }\n                }).getField('value');\n            }\n            // 添加到购物车\n            const cartData = {\n                goods_id: productInfo.goods_id,\n                product_id: productId,\n                goods_sn: productInfo.goods_sn,\n                goods_name: goodsInfo.name,\n                goods_aka: productInfo.goods_name,\n                // goods_weight: 移除重量字段\n                freight_template_id: goodsInfo.freight_template_id,\n                list_pic_url: goodsInfo.list_pic_url,\n                number: number,\n                user_id: userId,\n                retail_price: retail_price,\n                add_price: retail_price,\n                goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                goods_specifition_ids: productInfo.goods_specification_ids,\n                checked: 1,\n                add_time: currentTime,\n                is_fast: 1,\n                seckill_round_id: seckillRoundId || null,\n                is_seckill: seckillRoundId ? 1 : 0\n            };\n            await this.model('cart').add(cartData);\n            return this.success(await this.getCart(1));\n        } else {\n            if (think.isEmpty(cartInfo)) {\n                // 添加操作\n                // 添加规格名和值\n                let goodsSepcifitionValue = [];\n                if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                    goodsSepcifitionValue = await this.model('goods_specification').where({\n                        goods_id: productInfo.goods_id,\n                        is_delete: 0,\n                        id: {\n                            'in': productInfo.goods_specification_ids.split('_')\n                        }\n                    }).getField('value');\n                }\n                // 添加到购物车\n                const cartData = {\n                    goods_id: productInfo.goods_id,\n                    product_id: productId,\n                    goods_sn: productInfo.goods_sn,\n                    goods_name: goodsInfo.name,\n                    goods_aka: productInfo.goods_name,\n                    // goods_weight: 移除重量字段\n                    freight_template_id: goodsInfo.freight_template_id,\n                    list_pic_url: goodsInfo.list_pic_url,\n                    number: number,\n                    user_id: userId,\n                    retail_price: retail_price,\n                    add_price: retail_price,\n                    goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                    goods_specifition_ids: productInfo.goods_specification_ids,\n                    checked: 1,\n                    add_time: currentTime,\n                    seckill_round_id: seckillRoundId || null,\n                    is_seckill: seckillRoundId ? 1 : 0\n                };\n                await this.model('cart').add(cartData);\n            } else {\n                // 如果已经存在购物车中，则数量增加\n                if (productInfo.goods_number < (number + cartInfo.number)) {\n                    return this.fail(400, '库存都不够啦');\n                }\n                await this.model('cart').where({\n                    user_id: userId,\n                    product_id: productId,\n                    is_delete: 0,\n                    id: cartInfo.id\n                }).update({\n                    retail_price: retail_price\n                });\n                await this.model('cart').where({\n                    user_id: userId,\n                    product_id: productId,\n                    is_delete: 0,\n                    id: cartInfo.id\n                }).increment('number', number);\n            }\n            return this.success(await this.getCart(0));\n        }\n    }\n    // 更新指定的购物车信息\n    async updateAction() {\n        const productId = this.post('productId'); // 新的product_id\n        const id = this.post('id'); // cart.id\n        const number = parseInt(this.post('number')); // 不是\n        // 取得规格的信息,判断规格库存\n        const productInfo = await this.model('product').where({\n            id: productId,\n            is_delete: 0,\n        }).find();\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n        // 判断是否已经存在product_id购物车商品\n        const cartInfo = await this.model('cart').where({\n            id: id,\n            is_delete: 0\n        }).find();\n        // 只是更新number\n        if (cartInfo.product_id === productId) {\n            await this.model('cart').where({\n                id: id,\n                is_delete: 0\n            }).update({\n                number: number\n            });\n            return this.success(await this.getCart(0));\n        }\n    }\n    // 是否选择商品，如果已经选择，则取消选择，批量操作\n    async checkedAction() {\n\t\tconst userId = await this.getLoginUserId();\n        let productId = this.post('productIds').toString();\n        const isChecked = this.post('isChecked');\n        if (think.isEmpty(productId)) {\n            return this.fail('删除出错');\n        }\n        productId = productId.split(',');\n        await this.model('cart').where({\n            product_id: {\n                'in': productId\n            },\n            user_id: userId,\n            is_delete: 0\n        }).update({\n            checked: parseInt(isChecked)\n        });\n        return this.success(await this.getCart(0));\n    }\n    // 删除选中的购物车商品，批量删除\n    async deleteAction() {\n        let productId = this.post('productIds');\n\t\tconst userId = await this.getLoginUserId();\n        if (think.isEmpty(productId)) {\n            return this.fail('删除出错');\n        }\n        await this.model('cart').where({\n            product_id: productId,\n            user_id: userId,\n            is_delete: 0\n        }).update({\n            is_delete: 1\n        });\n        return this.success(await this.getCart(0));\n        // return this.success(productId);\n    }\n    // 获取购物车商品的总件件数\n    async goodsCountAction() {\n        const cartData = await this.getCart(0);\n\t\tconst userId = await this.getLoginUserId();\n        await this.model('cart').where({\n            user_id: userId,\n            is_delete: 0,\n            is_fast: 1\n        }).update({\n            is_delete: 1\n        });\n        return this.success({\n            cartTotal: {\n                goodsCount: cartData.cartTotal.goodsCount\n            }\n        });\n    }\n    /**\n     * 订单提交前的检验和填写相关订单信息\n     * @returns {Promise.<void>}\n     */\n    async checkoutAction() {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n\t\tconst userId = await this.getLoginUserId();\n        let orderFrom = this.get('orderFrom');\n        const type = this.get('type'); // 是否团购\n        const addressId = this.get('addressId'); // 收货地址id\n        const addType = this.get('addType');\n        let goodsCount = 0; // 购物车的数量\n        let goodsMoney = 0; // 购物车的总价\n        let freightPrice = 0;\n        let outStock = 0;\n        let cartData = '';\n        // 获取要购买的商品\n        if (type == 0) {\n            if (addType == 0) {\n                cartData = await this.getCart(0);\n            } else if (addType == 1) {\n                cartData = await this.getCart(1);\n            } else if (addType == 2) {\n                cartData = await this.getAgainCart(orderFrom);\n            }\n        }\n        const checkedGoodsList = cartData.cartList.filter(function(v) {\n            return v.checked === 1;\n        });\n        for (const item of checkedGoodsList) {\n            goodsCount = goodsCount + item.number;\n            goodsMoney = goodsMoney + item.number * item.retail_price;\n            if (item.goods_number <= 0 || item.is_on_sale == 0) {\n                outStock = Number(outStock) + 1;\n            }\n        }\n        if (addType == 2) {\n            let againGoods = await this.model('order_goods').where({\n                order_id: orderFrom\n            }).select();\n            let againGoodsCount = 0;\n            for (const item of againGoods) {\n                againGoodsCount = againGoodsCount + item.number;\n            }\n            if (goodsCount != againGoodsCount) {\n                outStock = 1;\n            }\n        }\n        // 选择的收货地址\n        let checkedAddress = null;\n        if (addressId == '' || addressId == 0) {\n            // 没有指定地址ID，需要自动选择地址\n\n            // 首先获取用户的所有地址\n            const allAddresses = await this.model('address').where({\n                user_id: userId,\n                is_delete: 0\n            }).order('id desc').select();\n\n            if (allAddresses.length === 0) {\n                // 没有任何地址\n                checkedAddress = null;\n            } else if (allAddresses.length === 1) {\n                // 只有一个地址，自动选择这个地址\n                checkedAddress = allAddresses[0];\n            } else {\n                // 有多个地址，优先选择默认地址\n                checkedAddress = allAddresses.find(addr => addr.is_default === 1);\n\n                // 如果没有默认地址，选择最新添加的地址（第一个，因为按id desc排序）\n                if (!checkedAddress) {\n                    checkedAddress = allAddresses[0];\n                }\n            }\n        } else {\n            // 指定了地址ID，直接查找该地址\n            checkedAddress = await this.model('address').where({\n                id: addressId,\n                user_id: userId,\n\t\t\t\tis_delete:0\n            }).find();\n        }\n        if (!think.isEmpty(checkedAddress)) {\n            // 运费开始\n            // 先将促销规则中符合满件包邮或者满金额包邮的规则找到；\n            // 先看看是不是属于偏远地区。\n            let province_id = checkedAddress.province_id;\n            // 得到数组了，然后去判断这两个商品符不符合要求\n            // 先用这个goods数组去遍历\n            let cartGoods = checkedGoodsList;\n            let freightTempArray = await this.model('freight_template').where({\n                is_delete: 0\n            }).select();\n            let freightData = [];\n            for (const item in freightTempArray) {\n                freightData[item] = {\n                    id: freightTempArray[item].id,\n                    number: 0,\n                    money: 0,\n                    goods_weight: 0,\n                    freight_type: freightTempArray[item].freight_type\n                }\n            }\n            // 按件计算和按重量计算的区别是：按件，只要算goods_number就可以了，按重量要goods_number*goods_weight\n            // checkedGoodsList = [{goods_id:1,number5},{goods_id:2,number:3},{goods_id:3,number:2}]\n            for (const item of freightData) {\n                for (const cartItem of cartGoods) {\n                    if (item.id == cartItem.freight_template_id) {\n                        // 这个在判断，购物车中的商品是否属于这个运费模版，如果是，则加一，但是，这里要先判断下，这个商品是否符合满件包邮或满金额包邮，如果是包邮的，那么要去掉\n                        item.number = item.number + cartItem.number;\n                        item.money = item.money + cartItem.number * cartItem.retail_price;\n                        item.goods_weight = item.goods_weight + cartItem.number * cartItem.goods_weight;\n                    }\n                }\n            }\n            checkedAddress.province_name = await this.model('region').getRegionName(checkedAddress.province_id);\n            checkedAddress.city_name = await this.model('region').getRegionName(checkedAddress.city_id);\n            checkedAddress.district_name = await this.model('region').getRegionName(checkedAddress.district_id);\n            checkedAddress.full_region = checkedAddress.province_name + checkedAddress.city_name + checkedAddress.district_name;\n            for (const item of freightData) {\n                if (item.number == 0) {\n                    continue;\n                }\n                let ex = await this.model('freight_template_detail').where({\n                    template_id: item.id,\n                    area: province_id,\n                    is_delete: 0,\n                }).find();\n                let freight_price = 0;\n                if (!think.isEmpty(ex)) {\n                    // console.log('第一层：非默认邮费算法');\n                    let groupData = await this.model('freight_template_group').where({\n                        id: ex.group_id,\n                        is_delete:0\n                    }).find();\n                    // 不为空，说明有模板，那么应用模板，先去判断是否符合指定的包邮条件，不满足，那么根据type 是按件还是按重量\n                    let free_by_number = groupData.free_by_number;\n                    let free_by_money = groupData.free_by_money;\n                    // 4种情况，1、free_by_number > 0  2,free_by_money > 0  3,free_by_number free_by_money > 0,4都等于0\n                    let templateInfo = await this.model('freight_template').where({\n                        id: item.id,\n                        is_delete: 0,\n                    }).find();\n                    let freight_type = templateInfo.freight_type;\n                    if (freight_type == 0) {\n                        if (item.number > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.number - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    } else if (freight_type == 1) {\n                        if (item.goods_weight > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.goods_weight - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    }\n                    if (free_by_number > 0) {\n                        if (item.number >= free_by_number) {\n                            freight_price = 0;\n                        }\n                    }\n                    if (free_by_money > 0) {\n                        if (item.money >= free_by_money) {\n                            freight_price = 0;\n                        }\n                    }\n                } else {\n                    // console.log('第二层：使用默认的邮费算法');\n                    let groupData = await this.model('freight_template_group').where({\n                        template_id: item.id,\n                        area: 0,\n                        is_delete:0,\n                    }).find();\n                    let free_by_number = groupData.free_by_number;\n                    let free_by_money = groupData.free_by_money;\n                    let templateInfo = await this.model('freight_template').where({\n                        id: item.id,\n                        is_delete: 0,\n                    }).find();\n                    let freight_type = templateInfo.freight_type;\n                    if (freight_type == 0) {\n                        if (item.number > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.number - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    } else if (freight_type == 1) {\n                        if (item.goods_weight > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.goods_weight - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    }\n                    if (free_by_number > 0) {\n                        if (item.number >= free_by_number) {\n                            freight_price = 0;\n                        }\n                    }\n                    if (free_by_money > 0) {\n                        if (item.money >= free_by_money) {\n                            freight_price = 0;\n                        }\n                    }\n                }\n\t\t\t\tfreightPrice = freightPrice > freight_price?freightPrice:freight_price\n                // freightPrice = freightPrice + freight_price;\n                // 会得到 几个数组，然后用省id去遍历在哪个数组\n            }\n        } else {\n            checkedAddress = 0;\n        }\n        // 计算订单的费用\n        let goodsTotalPrice = cartData.cartTotal.checkedGoodsAmount; // 商品总价\n        // 获取是否有可用红包\n        let money = cartData.cartTotal.checkedGoodsAmount;\n        let orderTotalPrice = 0;\n        let def = await this.model('settings').where({\n            id: 1\n        }).find();\n        orderTotalPrice = Number(money) + Number(freightPrice) // 订单的总价\n        const actualPrice = orderTotalPrice; // 减去其它支付的金额后，要实际支付的金额\n        let numberChange = cartData.cartTotal.numberChange;\n        return this.success({\n            checkedAddress: checkedAddress,\n            freightPrice: freightPrice,\n            checkedGoodsList: checkedGoodsList,\n            goodsTotalPrice: goodsTotalPrice,\n            orderTotalPrice: orderTotalPrice.toFixed(2),\n            actualPrice: actualPrice.toFixed(2),\n            goodsCount: goodsCount,\n            outStock: outStock,\n            numberChange: numberChange,\n        });\n    }\n    async getAgainCart(orderFrom) {\n\t\tconst userId = await this.getLoginUserId();\n        const againGoods = await this.model('order_goods').where({\n            order_id: orderFrom\n        }).select();\n        await this.model('cart').where({\n            is_delete: 0,\n            user_id: userId\n        }).update({\n            checked: 0\n        });\n        for (const item of againGoods) {\n            await this.addAgain(item.goods_id, item.product_id, item.number);\n        }\n        const cartList = await this.model('cart').where({\n            user_id: userId,\n            is_fast: 0,\n            is_delete: 0\n        }).select();\n        // 获取购物车统计信息\n        let goodsCount = 0;\n        let goodsAmount = 0;\n        let checkedGoodsCount = 0;\n        let checkedGoodsAmount = 0;\n        for (const cartItem of cartList) {\n            goodsCount += cartItem.number;\n            goodsAmount += cartItem.number * cartItem.retail_price;\n            if (!think.isEmpty(cartItem.checked)) {\n                checkedGoodsCount += cartItem.number;\n                checkedGoodsAmount += cartItem.number * Number(cartItem.retail_price);\n            }\n            // 查找商品的图片\n            let info = await this.model('goods').where({\n                id: cartItem.goods_id\n            }).field('list_pic_url,goods_number,goods_unit').find();\n            // cartItem.list_pic_url = await this.model('goods').where({id: cartItem.goods_id}).getField('list_pic_url', true);\n            let num = info.goods_number;\n            if (num <= 0) {\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    checked: 1,\n                    is_delete: 0,\n                }).update({\n                    checked: 0\n                });\n            }\n            cartItem.list_pic_url = info.list_pic_url;\n            cartItem.goods_number = info.goods_number;\n            cartItem.weight_count = cartItem.number * Number(cartItem.goods_weight);\n        }\n        let cAmount = checkedGoodsAmount.toFixed(2);\n        let aAmount = checkedGoodsAmount;\n        return {\n            cartList: cartList,\n            cartTotal: {\n                goodsCount: goodsCount,\n                goodsAmount: goodsAmount.toFixed(2),\n                checkedGoodsCount: checkedGoodsCount,\n                checkedGoodsAmount: cAmount,\n                user_id: userId\n            }\n        };\n    }\n\n    /**\n     * 测试秒杀购物车添加功能\n     * POST /api/cart/testSeckillAdd\n     */\n    async testSeckillAddAction() {\n        try {\n            const goodsId = this.post('goodsId') || 1181002;\n            const productId = this.post('productId') || 1181002;\n            const number = this.post('number') || 1;\n            const addType = this.post('addType') || 1;\n            const seckillRoundId = this.post('seckill_round_id') || 32;\n            const seckillPrice = this.post('seckill_price') || 5.19;\n            const userId = 1; // 测试用户ID\n            const currentTime = parseInt(new Date().getTime() / 1000);\n\n            console.log('测试秒杀购物车添加参数:', { goodsId, productId, number, addType, seckillRoundId, seckillPrice, userId });\n\n            // 判断商品是否可以购买\n            const goodsInfo = await this.model('goods').where({\n                id: goodsId\n            }).find();\n            if (think.isEmpty(goodsInfo) || goodsInfo.is_on_sale == 0) {\n                return this.fail(400, '商品已下架');\n            }\n\n            // 取得规格的信息,判断规格库存\n            const productInfo = await this.model('product').where({\n                id: productId\n            }).find();\n\n            if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n                return this.fail(400, '库存不足');\n            }\n\n            // 秒杀商品验证\n            let retail_price = productInfo.retail_price;\n            if (seckillRoundId) {\n                console.log('验证秒杀商品，轮次ID:', seckillRoundId);\n\n                // 验证秒杀轮次是否存在且有效\n                const seckillRound = await this.model('flash_sale_rounds').where({\n                    id: seckillRoundId,\n                    status: 'active'\n                }).find();\n\n                if (think.isEmpty(seckillRound)) {\n                    return this.fail(400, '秒杀活动不存在或已结束');\n                }\n\n                // 验证商品是否在该秒杀轮次中\n                const seckillGoods = await this.model('flash_sale_round_goods').where({\n                    round_id: seckillRoundId,\n                    goods_id: goodsId\n                }).find();\n\n                if (think.isEmpty(seckillGoods)) {\n                    return this.fail(400, '该商品不在此秒杀轮次中');\n                }\n\n                // 验证秒杀价格\n                if (parseFloat(seckillPrice) !== parseFloat(seckillGoods.flash_price)) {\n                    return this.fail(400, '秒杀价格不正确');\n                }\n\n                // 验证秒杀库存\n                if (seckillGoods.stock < number) {\n                    return this.fail(400, '秒杀库存不足');\n                }\n\n                // 验证限购数量\n                if (number > seckillGoods.limit_quantity) {\n                    return this.fail(400, `每人限购${seckillGoods.limit_quantity}件`);\n                }\n\n                // 检查用户已购买数量\n                const userBoughtCount = await this.model('cart').where({\n                    user_id: userId,\n                    goods_id: goodsId,\n                    seckill_round_id: seckillRoundId,\n                    is_delete: 0\n                }).sum('number');\n\n                if ((userBoughtCount || 0) + number > seckillGoods.limit_quantity) {\n                    return this.fail(400, `您已购买${userBoughtCount || 0}件，超出限购数量`);\n                }\n\n                // 使用秒杀价格\n                retail_price = seckillGoods.flash_price;\n                console.log('秒杀验证通过，使用秒杀价格:', retail_price);\n            }\n\n            // 清空其他购物车商品\n            await this.model('cart').where({\n                is_delete: 0,\n                user_id: userId\n            }).update({\n                checked: 0\n            });\n\n            // 添加到购物车\n            const cartData = {\n                goods_id: goodsId,\n                product_id: productId,\n                goods_sn: productInfo.goods_sn,\n                goods_name: goodsInfo.name,\n                goods_aka: productInfo.goods_name,\n                freight_template_id: goodsInfo.freight_template_id,\n                list_pic_url: goodsInfo.list_pic_url,\n                number: number,\n                user_id: userId,\n                retail_price: retail_price,\n                add_price: retail_price,\n                goods_specifition_name_value: '',\n                goods_specifition_ids: productInfo.goods_specification_ids,\n                checked: 1,\n                add_time: currentTime,\n                is_fast: 1,\n                seckill_round_id: seckillRoundId || null,\n                is_seckill: seckillRoundId ? 1 : 0\n            };\n\n            console.log('添加购物车数据:', cartData);\n            await this.model('cart').add(cartData);\n\n            return this.success({\n                message: '秒杀商品添加到购物车成功',\n                cart_data: cartData\n            });\n\n        } catch (error) {\n            console.error('测试秒杀购物车添加失败:', error);\n            return this.fail(500, '添加失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 更新购物车表结构，添加秒杀字段\n     * GET /api/cart/updateCartTable\n     */\n    async updateCartTableAction() {\n        try {\n            const sql1 = `ALTER TABLE hiolabs_cart ADD COLUMN seckill_round_id INT DEFAULT NULL COMMENT '秒杀轮次ID'`;\n            const sql2 = `ALTER TABLE hiolabs_cart ADD COLUMN is_seckill TINYINT(1) DEFAULT 0 COMMENT '是否秒杀商品'`;\n\n            try {\n                await this.model().execute(sql1);\n                console.log('添加 seckill_round_id 字段成功');\n            } catch (e) {\n                console.log('seckill_round_id 字段可能已存在:', e.message);\n            }\n\n            try {\n                await this.model().execute(sql2);\n                console.log('添加 is_seckill 字段成功');\n            } catch (e) {\n                console.log('is_seckill 字段可能已存在:', e.message);\n            }\n\n            return this.success({\n                message: '购物车表结构更新成功',\n                sql: [sql1, sql2]\n            });\n        } catch (error) {\n            console.error('更新购物车表结构失败:', error);\n            return this.fail(500, '更新失败: ' + error.message);\n        }\n    }\n};"]}