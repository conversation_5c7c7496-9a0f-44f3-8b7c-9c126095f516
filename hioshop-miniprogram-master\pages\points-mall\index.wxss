/* 积分商城页面样式 */
.points-mall-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  background: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input-wrapper {
  position: relative;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 0 100rpx 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  position: absolute;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
}

.clear-btn {
  position: absolute;
  right: 90rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
}

/* 分类导航 */
.category-nav {
  background: #fff;
  border-bottom: 1rpx solid #eee;
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s;
}

.category-item.active {
  color: #ff4757;
  border-bottom-color: #ff4757;
  font-weight: 600;
}

/* 排序栏 */
.sort-bar {
  background: #fff;
  padding: 24rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.sort-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.sort-btn .iconfont {
  margin-left: 8rpx;
  font-size: 24rpx;
  transition: transform 0.3s;
}

.goods-count {
  font-size: 24rpx;
  color: #999;
}

/* 排序菜单 */
.sort-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.sort-menu.show {
  opacity: 1;
  visibility: visible;
}

.sort-menu-content {
  background: #fff;
  margin-top: 200rpx;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 0;
}

.sort-option {
  padding: 32rpx 40rpx;
  font-size: 32rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sort-option.active {
  color: #ff4757;
  font-weight: 600;
}

.sort-option .icon-check {
  font-size: 28rpx;
}

/* 商品列表 */
.goods-list {
  padding: 20rpx;
}

.goods-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  display: flex;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.goods-image-wrapper {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.goods-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.hot-tag {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx 0 12rpx 0;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.points-price {
  display: flex;
  align-items: baseline;
  margin-right: 20rpx;
}

.price-num {
  font-size: 36rpx;
  color: #ff4757;
  font-weight: 700;
  font-family: 'DIN Alternate', 'Arial', sans-serif;
}

.price-unit {
  font-size: 24rpx;
  color: #ff4757;
  margin-left: 4rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.sales-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-wrapper {
  text-align: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 全局加载状态 */
.global-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff4757;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
