function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');
const generate = require('nanoid/generate');
const Jushuitan = require('jushuitan');
const WangDianSync = require('../../common/wangdian_sync.js');
module.exports = class extends Base {
    /**
     * 获取支付的请求参数
     * @returns {Promise<PreventPromise|void|Promise>}
     */
    // 测试时付款，将真实接口注释。 在小程序的services/pay.js中按照提示注释和打开
    preWeixinPayaAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            const orderId = _this.get('orderId');
            const orderInfo = yield _this.model('order').where({
                id: orderId
            }).find();
            let userId = orderInfo.user_id;
            let result = {
                transaction_id: 123123123123,
                time_end: parseInt(new Date().getTime() / 1000)
            };
            const orderModel = _this.model('order');
            yield orderModel.updatePayData(orderInfo.id, result);
            yield _this.afterPay(orderInfo);
            return _this.success();
        })();
    }
    // 真实的付款接口
    preWeixinPayAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this2.get('orderId');
            const orderInfo = yield _this2.model('order').where({
                id: orderId
            }).find();
            // 再次确认库存和价格
            let orderGoods = yield _this2.model('order_goods').where({
                order_id: orderId,
                is_delete: 0
            }).select();
            let checkPrice = 0;
            let checkStock = 0;
            for (const item of orderGoods) {
                let product = yield _this2.model('product').where({
                    id: item.product_id
                }).find();
                if (item.number > product.goods_number) {
                    checkStock++;
                }

                // 检查是否为秒杀商品（通过秒杀订单记录表查询）
                const seckillOrder = yield _this2.model('flash_sale_orders').where({
                    order_id: orderId,
                    goods_id: item.goods_id
                }).find();

                if (!think.isEmpty(seckillOrder)) {
                    // 这是秒杀商品，查询秒杀商品信息
                    const seckillGoods = yield _this2.model('flash_sale_round_goods').where({
                        round_id: seckillOrder.round_id,
                        goods_id: item.goods_id
                    }).find();

                    if (!think.isEmpty(seckillGoods)) {
                        // 计算当前规格的秒杀价格
                        let expectedPrice = product.retail_price;
                        if (seckillGoods.original_price > 0) {
                            const discountRate = seckillGoods.flash_price / seckillGoods.original_price;
                            expectedPrice = Math.round(product.retail_price * discountRate * 100) / 100;
                            expectedPrice = Math.max(0.01, expectedPrice);
                        } else {
                            expectedPrice = seckillGoods.flash_price;
                        }

                        if (Math.abs(item.retail_price - expectedPrice) > 0.01) {
                            checkPrice++;
                        }
                    }
                } else {
                    // 普通商品验证原价
                    if (item.retail_price != product.retail_price) {
                        checkPrice++;
                    }
                }
            }
            if (checkStock > 0) {
                return _this2.fail(400, '库存不足，请重新下单');
            }
            if (checkPrice > 0) {
                return _this2.fail(400, '价格发生变化，请重新下单');
            }
            if (think.isEmpty(orderInfo)) {
                return _this2.fail(400, '订单已取消');
            }
            if (parseInt(orderInfo.pay_status) !== 0) {
                return _this2.fail(400, '订单已支付，请不要重复操作');
            }
            const openid = yield _this2.model('user').where({
                id: orderInfo.user_id
            }).getField('weixin_openid', true);
            if (think.isEmpty(openid)) {
                return _this2.fail(400, '微信支付失败，没有openid');
            }
            const WeixinSerivce = _this2.service('weixin', 'api');
            try {
                const returnParams = yield WeixinSerivce.createUnifiedOrder({
                    openid: openid,
                    body: '[海风小店]：' + orderInfo.order_sn,
                    out_trade_no: orderInfo.order_sn,
                    total_fee: parseInt(orderInfo.actual_price * 100),
                    spbill_create_ip: ''
                });
                return _this2.success(returnParams);
            } catch (err) {
                return _this2.fail(400, '微信支付失败?');
            }
        })();
    }
    notifyAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            console.log('=== 微信支付异步通知开始 ===');
            console.log('请求IP:', _this3.ip);
            console.log('请求时间:', new Date().toISOString());

            const WeixinSerivce = _this3.service('weixin', 'api');
            const data = _this3.post('xml');
            console.log('接收到的通知数据:', data);

            const result = yield WeixinSerivce.payNotify(_this3.post('xml'));
            console.log('签名验证结果:', result);

            if (!result) {
                console.log('签名验证失败，返回FAIL');
                let echo = 'FAIL';
                return _this3.json(echo);
            }
            const orderModel = _this3.model('order');
            const orderInfo = yield orderModel.getOrderByOrderSn(result.out_trade_no);
            console.log('查询到的订单信息:', orderInfo);

            if (think.isEmpty(orderInfo)) {
                console.log('订单不存在，订单号:', result.out_trade_no);
                let echo = 'FAIL';
                return _this3.json(echo);
            }

            let bool = yield orderModel.checkPayStatus(orderInfo.id);
            console.log('订单支付状态检查结果:', bool, '订单ID:', orderInfo.id);

            if (bool == true) {
                if (orderInfo.order_type == 0) {
                    //普通订单和秒杀订单
                    console.log('开始更新订单支付状态...');
                    yield orderModel.updatePayData(orderInfo.id, result);
                    console.log('订单支付状态更新完成');

                    console.log('开始执行afterPay处理...');
                    yield _this3.afterPay(orderInfo);
                    console.log('afterPay处理完成');
                }
            } else {
                console.log('订单已支付，不重复处理');
                return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[订单已支付]]></return_msg></xml>';
            }

            console.log('=== 微信支付异步通知处理完成 ===');
            let echo = 'SUCCESS';
            return _this3.json(echo);
        })();
    }
    afterPay(orderInfo) {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            if (orderInfo.order_type == 0) {
                let orderGoodsList = yield _this4.model('order_goods').where({
                    order_id: orderInfo.id
                }).select();

                // 更新库存和销量
                for (const cartItem of orderGoodsList) {
                    let goods_id = cartItem.goods_id;
                    let product_id = cartItem.product_id;
                    let number = cartItem.number;
                    let specification = cartItem.goods_specifition_name_value;
                    yield _this4.model('goods').where({
                        id: goods_id
                    }).decrement('goods_number', number);
                    yield _this4.model('goods').where({
                        id: goods_id
                    }).increment('sell_volume', number);
                    yield _this4.model('product').where({
                        id: product_id
                    }).decrement('goods_number', number);
                }

                // 获取更新后的订单信息（确保order_status是201）
                const updatedOrderInfo = yield _this4.model('order').where({
                    id: orderInfo.id
                }).find();

                // 检查订单状态是否为201（已付款），如果是则推送到旺店通ERP
                if (updatedOrderInfo.order_status === 201) {
                    try {
                        console.log(`[旺店通同步] 订单状态为201（已付款），开始推送订单到旺店通ERP: ${updatedOrderInfo.order_sn}`);
                        const wangdianSync = new WangDianSync(_this4);
                        const result = yield wangdianSync.pushOrder(updatedOrderInfo, orderGoodsList);

                        if (result.success) {
                            console.log(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送成功`);
                        } else {
                            console.error(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送失败: ${result.message}`);
                        }
                    } catch (error) {
                        console.error('[旺店通同步] 推送订单时发生异常:', error);
                        // 不抛出异常，避免影响主要的支付流程
                    }
                } else {
                    console.log(`[旺店通同步] 订单状态不是201，跳过推送。当前状态: ${updatedOrderInfo.order_status}`);
                }

                // version 1.01
            }
        })();
    }

    /**
     * 微信退款通知处理
     */
    refundNotifyAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            console.log('=== 微信退款异步通知开始 ===');
            console.log('请求IP:', _this5.ip);
            console.log('请求时间:', new Date().toISOString());

            let info = _this5.post();
            console.log('接收到的退款通知数据:', info);

            try {
                // 微信退款通知的数据格式与支付通知不同
                // 这里简化处理，主要是确认收到通知

                if (info.return_code === 'SUCCESS') {
                    console.log('✅ 微信退款通知处理成功');

                    // 可以在这里添加额外的退款后处理逻辑
                    // 比如发送退款成功通知给用户、更新订单状态等

                    // 返回成功响应给微信
                    _this5.ctx.type = 'text/xml';
                    return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';
                } else {
                    console.error('❌ 微信退款通知失败:', info.return_msg);
                    _this5.ctx.type = 'text/xml';
                    return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>';
                }
            } catch (error) {
                console.error('❌ 处理微信退款通知异常:', error);
                _this5.ctx.type = 'text/xml';
                return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>';
            }
        })();
    }
};
//# sourceMappingURL=pay.js.map