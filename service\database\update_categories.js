const mysql = require('mysql2/promise');

async function updateCategories() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    // 更新分类
    await connection.execute("UPDATE `hiolabs_points_goods` SET `category_id` = 1 WHERE `id` IN (2, 4, 5, 6)");
    console.log('✅ 数码配件分类更新成功');
    
    await connection.execute("UPDATE `hiolabs_points_goods` SET `category_id` = 2 WHERE `id` IN (1, 3)");
    console.log('✅ 生活用品分类更新成功');

    // 验证更新结果
    const [goods] = await connection.execute('SELECT id, goods_name, category_id FROM hiolabs_points_goods ORDER BY id');
    console.log('\n=== 积分商品分类情况 ===');
    goods.forEach(item => {
      console.log(`ID: ${item.id}, 商品: ${item.goods_name}, 分类ID: ${item.category_id}`);
    });

    await connection.end();
    console.log('\n📝 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 执行失败:', error);
  }
}

updateCategories();
