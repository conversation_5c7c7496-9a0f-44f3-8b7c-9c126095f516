// 积分兑好礼组件 - CRMEB风格
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 积分商品列表
    pointsGoodsList: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 跳转到积分商城
     */
    goPointsMall() {
      wx.navigateTo({
        url: '/pages/points-mall/index'
      });
    },

    /**
     * 跳转到积分商品详情
     */
    goGoodsDetails(e) {
      const id = e.currentTarget.dataset.id;
      console.log('跳转到积分商品详情页:', id);
      wx.navigateTo({
        url: `/pages/points-goods-detail/index?id=${id}`
      });
    }
  }
});
