{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\flashsalemulti.js"], "names": ["Base", "require", "FlashSaleMultiScheduler", "module", "exports", "formatLocalDateTime", "date", "d", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "formatLocalDate", "statisticsAction", "roundModel", "model", "orderModel", "totalRounds", "count", "activeRounds", "where", "status", "upcomingRounds", "endedRounds", "totalOrders", "today", "todayOrders", "created_at", "totalSales", "sum", "success", "error", "console", "fail", "testAction", "log", "rounds", "select", "length", "message", "data", "listAction", "page", "get", "limit", "roundGoodsModel", "order", "countSelect", "round", "id", "round_name", "goods", "getRoundGoodsList", "goods_list", "goods_count", "total_stock", "reduce", "g", "stock", "total_sold", "sold_count", "roundError", "currentAction", "currentRounds", "now", "startTime", "start_time", "endTime", "end_time", "countdown", "Math", "max", "floor", "current", "upcoming", "total", "createAction", "post", "JSON", "stringify", "Array", "isArray", "isNaN", "getTime", "goodsModel", "i", "goods_id", "flash_price", "is_hourly_flash", "goodsIds", "map", "conflictGoods", "checkGoodsInActiveRounds", "conflictNames", "round_number", "join", "formattedStartTime", "formattedEndTime", "checkGoodsInOverlappingRounds", "nextRoundNumber", "maxRetries", "retryCount", "maxRoundResult", "field", "find", "max_round", "parseInt", "Promise", "setTimeout", "resolve", "roundData", "created_by", "think", "userId", "roundId", "add", "Error", "code", "retrySuccess", "retryAttempts", "retryMaxResult", "retryNextNumber", "retryError", "goodsList", "goodsItem", "isEmpty", "originalPrice", "parseFloat", "retail_price", "flashPrice", "discountRate", "push", "goods_name", "name", "goods_image", "list_pic_url", "original_price", "discount_rate", "limit_quantity", "addRoundGoods", "result", "goodsAction", "productModel", "allGoods", "is_on_sale", "is_delete", "activeGoodsIds", "alias", "table", "as", "on", "activeIds", "item", "all", "products", "priceRange", "totalStock", "prices", "p", "filter", "goods_number", "minPrice", "min", "maxPrice", "toFixed", "actualStock", "isInFlashSale", "includes", "price_range", "actual_stock", "is_in_flash_sale", "can_select", "warning", "configAction", "configModel", "isGet", "config", "update", "closeAction", "closed_manually", "closed_at", "updated_at", "updateStatusAction", "scheduler", "updateRoundStatus", "bind", "restartAction", "round_id", "new_start_time", "new_end_time", "updateData", "extendAction", "extend_minutes", "currentEndTime", "newEndTime", "copyAction", "new_round_name", "sourceRound", "lastRound", "newRoundData", "newRoundId", "sourceGoods", "newGoodsData", "sort_order", "addMany", "deleteAction", "delete", "batchDeleteAction", "roundIds", "successCount", "failCount", "results", "timeDebugAction", "currentTime", "getCurrentLocalTimeString", "debugInfo", "current_local_time", "current_js_time", "toLocaleString", "current_utc_time", "toISOString", "upcoming_rounds", "r", "should_be_active", "should_be_ended", "stats", "getActiveRoundsStats", "updated"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,0BAA0BD,QAAQ,iDAAR,CAAhC;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;;AAElC;;;;;AAKAK,sBAAoBC,IAApB,EAA0B;AACxB,UAAMC,IAAI,IAAIC,IAAJ,CAASF,IAAT,CAAV;AACA,UAAMG,OAAOF,EAAEG,WAAF,EAAb;AACA,UAAMC,QAAQC,OAAOL,EAAEM,QAAF,KAAe,CAAtB,EAAyBC,QAAzB,CAAkC,CAAlC,EAAqC,GAArC,CAAd;AACA,UAAMC,MAAMH,OAAOL,EAAES,OAAF,EAAP,EAAoBF,QAApB,CAA6B,CAA7B,EAAgC,GAAhC,CAAZ;AACA,UAAMG,QAAQL,OAAOL,EAAEW,QAAF,EAAP,EAAqBJ,QAArB,CAA8B,CAA9B,EAAiC,GAAjC,CAAd;AACA,UAAMK,UAAUP,OAAOL,EAAEa,UAAF,EAAP,EAAuBN,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAhB;AACA,UAAMO,UAAUT,OAAOL,EAAEe,UAAF,EAAP,EAAuBR,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAhB;AACA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,GAAI,IAAGE,KAAM,IAAGE,OAAQ,IAAGE,OAAQ,EAA9D;AACD;;AAED;;;;;AAKAE,kBAAgBjB,IAAhB,EAAsB;AACpB,UAAMC,IAAI,IAAIC,IAAJ,CAASF,IAAT,CAAV;AACA,UAAMG,OAAOF,EAAEG,WAAF,EAAb;AACA,UAAMC,QAAQC,OAAOL,EAAEM,QAAF,KAAe,CAAtB,EAAyBC,QAAzB,CAAkC,CAAlC,EAAqC,GAArC,CAAd;AACA,UAAMC,MAAMH,OAAOL,EAAES,OAAF,EAAP,EAAoBF,QAApB,CAA6B,CAA7B,EAAgC,GAAhC,CAAZ;AACA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,GAAI,EAA/B;AACD;;AAED;;;;AAIMS,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACF,cAAMC,aAAa,MAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMC,aAAa,MAAKD,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAME,cAAc,MAAMH,WAAWI,KAAX,EAA1B;AACA,cAAMC,eAAe,MAAML,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuCH,KAAvC,EAA3B;AACA,cAAMI,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,UAAV,EAAjB,EAAyCH,KAAzC,EAA7B;AACA,cAAMK,cAAc,MAAMT,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,OAAV,EAAjB,EAAsCH,KAAtC,EAA1B;;AAEA;AACA,cAAMM,cAAc,MAAMR,WAAWE,KAAX,EAA1B;AACA,cAAMO,QAAQ,MAAKb,eAAL,CAAqB,IAAIf,IAAJ,EAArB,CAAd;AACA,cAAM6B,cAAc,MAAMV,WAAWI,KAAX,CAAiB;AACzCO,sBAAY,CAAC,IAAD,EAAOF,QAAQ,WAAf;AAD6B,SAAjB,EAEvBP,KAFuB,EAA1B;;AAIA;AACA,cAAMU,aAAa,OAAMZ,WAAWa,GAAX,CAAe,cAAf,CAAN,KAAwC,CAA3D;;AAEA,eAAO,MAAKC,OAAL,CAAa;AAClBb,qBADkB;AAElBE,sBAFkB;AAGlBG,wBAHkB;AAIlBC,qBAJkB;AAKlBC,qBALkB;AAMlBE,qBANkB;AAOlBE;AAPkB,SAAb,CAAP;AAUD,OA9BD,CA8BE,OAAOG,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAlCsB;AAmCxB;;AAED;;;;AAIMC,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACFF,gBAAQG,GAAR,CAAY,iBAAZ;AACA,cAAMrB,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMqB,SAAS,MAAMtB,WAAWuB,MAAX,EAArB;AACAL,gBAAQG,GAAR,CAAY,SAAZ,EAAuBC,OAAOE,MAA9B;;AAEA,eAAO,OAAKR,OAAL,CAAa;AAClBS,mBAAS,MADS;AAElBrB,iBAAOkB,OAAOE,MAFI;AAGlBE,gBAAMJ;AAHY,SAAb,CAAP;AAKD,OAbD,CAaE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,UAAd,EAA0BA,KAA1B;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAjBgB;AAkBlB;;AAED;;;;AAIME,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACFT,gBAAQG,GAAR,CAAY,gBAAZ;AACA,cAAMO,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAMtB,SAAS,OAAKsB,GAAL,CAAS,QAAT,CAAf;;AAEAX,gBAAQG,GAAR,CAAY,OAAZ,EAAqB,EAAEO,IAAF,EAAQE,KAAR,EAAevB,MAAf,EAArB;;AAEA,cAAMP,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,OAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA,YAAIK,QAAQ,EAAZ;AACA,YAAIC,MAAJ,EAAY;AACVD,gBAAMC,MAAN,GAAeA,MAAf;AACD;;AAED;AACAW,gBAAQG,GAAR,CAAY,OAAZ,EAAqBf,KAArB;AACA,cAAMgB,SAAS,MAAMtB,WAAWM,KAAX,CAAiBA,KAAjB,EAClB0B,KADkB,CACZ,mBADY,EAElBJ,IAFkB,CAEbA,IAFa,EAEPE,KAFO,EAGlBG,WAHkB,EAArB;;AAKAf,gBAAQG,GAAR,CAAY,WAAZ,EAAyBC,OAAOI,IAAP,GAAcJ,OAAOI,IAAP,CAAYF,MAA1B,GAAmC,CAA5D;AACAN,gBAAQG,GAAR,CAAY,SAAZ,EAAuBC,MAAvB;;AAEA;AACA,YAAIA,OAAOI,IAAP,IAAeJ,OAAOI,IAAP,CAAYF,MAAZ,GAAqB,CAAxC,EAA2C;AACzC,eAAK,IAAIU,KAAT,IAAkBZ,OAAOI,IAAzB,EAA+B;AAC7B,gBAAI;AACFR,sBAAQG,GAAR,CAAY,OAAZ,EAAqBa,MAAMC,EAA3B,EAA+BD,MAAME,UAArC;AACA,oBAAMC,QAAQ,MAAMN,gBAAgBO,iBAAhB,CAAkCJ,MAAMC,EAAxC,CAApB;AACAjB,sBAAQG,GAAR,CAAY,SAAZ,EAAuBgB,MAAMb,MAA7B;;AAEAU,oBAAMK,UAAN,GAAmBF,SAAS,EAA5B;AACAH,oBAAMM,WAAN,GAAoBH,QAAQA,MAAMb,MAAd,GAAuB,CAA3C;;AAEA;AACAU,oBAAMO,WAAN,GAAoBJ,QAAQA,MAAMK,MAAN,CAAa,UAAC3B,GAAD,EAAM4B,CAAN;AAAA,uBAAY5B,OAAO4B,EAAEC,KAAF,IAAW,CAAlB,CAAZ;AAAA,eAAb,EAA+C,CAA/C,CAAR,GAA4D,CAAhF;AACAV,oBAAMW,UAAN,GAAmBR,QAAQA,MAAMK,MAAN,CAAa,UAAC3B,GAAD,EAAM4B,CAAN;AAAA,uBAAY5B,OAAO4B,EAAEG,UAAF,IAAgB,CAAvB,CAAZ;AAAA,eAAb,EAAoD,CAApD,CAAR,GAAiE,CAApF;AACD,aAXD,CAWE,OAAOC,UAAP,EAAmB;AACnB7B,sBAAQD,KAAR,CAAc,SAAd,EAAyBiB,MAAMC,EAA/B,EAAmCY,UAAnC;AACA;AACAb,oBAAMK,UAAN,GAAmB,EAAnB;AACAL,oBAAMM,WAAN,GAAoB,CAApB;AACAN,oBAAMO,WAAN,GAAoB,CAApB;AACAP,oBAAMW,UAAN,GAAmB,CAAnB;AACD;AACF;AACF;;AAED3B,gBAAQG,GAAR,CAAY,YAAZ,EAA0BC,OAAOI,IAAP,CAAYF,MAAtC;AACA,eAAO,OAAKR,OAAL,CAAaM,MAAb,CAAP;AAED,OAtDD,CAsDE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AA1DgB;AA2DlB;;AAED;;;;AAIM6B,eAAN,GAAsB;AAAA;;AAAA;AACpB,UAAI;AACF,cAAMhD,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,OAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMgD,gBAAgB,MAAMjD,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuCgB,MAAvC,EAA5B;;AAEA;AACA,cAAMf,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,UAAV,EAAjB,EAC1ByB,KAD0B,CACpB,gBADoB,EAE1BF,KAF0B,CAEpB,CAFoB,EAG1BP,MAH0B,EAA7B;;AAKA;AACA,aAAK,IAAIW,KAAT,IAAkB,CAAC,GAAGe,aAAJ,EAAmB,GAAGzC,cAAtB,CAAlB,EAAyD;AACvD,gBAAM6B,QAAQ,MAAMN,gBAAgBO,iBAAhB,CAAkCJ,MAAMC,EAAxC,CAApB;AACAD,gBAAMK,UAAN,GAAmBF,KAAnB;AACAH,gBAAMM,WAAN,GAAoBH,MAAMb,MAA1B;;AAEA;AACA,gBAAM0B,MAAM,IAAInE,IAAJ,EAAZ;AACA,gBAAMoE,YAAY,IAAIpE,IAAJ,CAASmD,MAAMkB,UAAf,CAAlB;AACA,gBAAMC,UAAU,IAAItE,IAAJ,CAASmD,MAAMoB,QAAf,CAAhB;;AAEA,cAAIpB,MAAM3B,MAAN,KAAiB,QAArB,EAA+B;AAC7B2B,kBAAMqB,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACL,UAAUH,GAAX,IAAkB,IAA7B,CAAZ,CAAlB;AACD,WAFD,MAEO,IAAIhB,MAAM3B,MAAN,KAAiB,UAArB,EAAiC;AACtC2B,kBAAMqB,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACP,YAAYD,GAAb,IAAoB,IAA/B,CAAZ,CAAlB;AACD;AACF;;AAED,eAAO,OAAKlC,OAAL,CAAa;AAClB2C,mBAASV,aADS;AAElBW,oBAAUpD,cAFQ;AAGlBqD,iBAAOZ,cAAczB,MAAd,GAAuBhB,eAAegB;AAH3B,SAAb,CAAP;AAMD,OArCD,CAqCE,OAAOP,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAzCmB;AA0CrB;;AAED;;;;AAIM2C,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF5C,gBAAQG,GAAR,CAAY,oBAAZ;;AAEA,cAAMK,OAAO,OAAKqC,IAAL,EAAb;AACA7C,gBAAQG,GAAR,CAAY,SAAZ,EAAuB2C,KAAKC,SAAL,CAAevC,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAAvB;;AAEA;AACA,YAAI,CAACA,KAAKU,UAAV,EAAsB;AACpB,iBAAO,OAAKjB,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAK0B,UAAV,EAAsB;AACpB,iBAAO,OAAKjC,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAK4B,QAAV,EAAoB;AAClB,iBAAO,OAAKnC,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAKa,UAAN,IAAoB,CAAC2B,MAAMC,OAAN,CAAczC,KAAKa,UAAnB,CAArB,IAAuDb,KAAKa,UAAL,CAAgBf,MAAhB,KAA2B,CAAtF,EAAyF;AACvF,iBAAO,OAAKL,IAAL,CAAU,YAAV,CAAP;AACD;;AAED;AACA,cAAMgC,YAAY,IAAIpE,IAAJ,CAAS2C,KAAK0B,UAAd,CAAlB;AACA,cAAMC,UAAU,IAAItE,IAAJ,CAAS2C,KAAK4B,QAAd,CAAhB;AACA,cAAMJ,MAAM,IAAInE,IAAJ,EAAZ;;AAEA,YAAIqF,MAAMjB,UAAUkB,OAAV,EAAN,KAA8BD,MAAMf,QAAQgB,OAAR,EAAN,CAAlC,EAA4D;AAC1D,iBAAO,OAAKlD,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAIgC,aAAaD,GAAjB,EAAsB;AACpB,iBAAO,OAAK/B,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,YAAIkC,WAAWF,SAAf,EAA0B;AACxB,iBAAO,OAAKhC,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,cAAMnB,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,OAAK9B,KAAL,CAAW,wBAAX,CAAxB;AACA,cAAMqE,aAAa,OAAKrE,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,aAAK,IAAIsE,IAAI,CAAb,EAAgBA,IAAI7C,KAAKa,UAAL,CAAgBf,MAApC,EAA4C+C,GAA5C,EAAiD;AAC/C,gBAAMlC,QAAQX,KAAKa,UAAL,CAAgBgC,CAAhB,CAAd;AACA,cAAI,CAAClC,MAAMmC,QAAP,IAAmB,CAACnC,MAAMoC,WAA1B,IAAyC,CAACpC,MAAMO,KAApD,EAA2D;AACzD,mBAAO,OAAKzB,IAAL,CAAW,IAAGoD,IAAE,CAAE,UAAlB,CAAP;AACD;AACF;;AAED;AACA;AACA,YAAI,CAAC7C,KAAKgD,eAAV,EAA2B;AACzB,gBAAMC,WAAWjD,KAAKa,UAAL,CAAgBqC,GAAhB,CAAoB;AAAA,mBAAKjC,EAAE6B,QAAP;AAAA,WAApB,CAAjB;AACA,gBAAMK,gBAAgB,MAAM9C,gBAAgB+C,wBAAhB,CAAyCH,QAAzC,CAA5B;AACA,cAAIE,cAAcrD,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,kBAAMuD,gBAAgBF,cAAcD,GAAd,CAAkB;AAAA,qBAAM,QAAOjC,EAAE6B,QAAS,MAAK7B,EAAEqC,YAAa,GAA5C;AAAA,aAAlB,EAAkEC,IAAlE,CAAuE,IAAvE,CAAtB;AACA,mBAAO,OAAK9D,IAAL,CAAW,kBAAiB4D,aAAc,EAA1C,CAAP;AACD;AACF,SAPD,MAOO;AACL;AACA,gBAAMJ,WAAWjD,KAAKa,UAAL,CAAgBqC,GAAhB,CAAoB;AAAA,mBAAKjC,EAAE6B,QAAP;AAAA,WAApB,CAAjB;AACA;AACA,gBAAMU,qBAAqB,OAAKtG,mBAAL,CAAyBuE,SAAzB,CAA3B;AACA,gBAAMgC,mBAAmB,OAAKvG,mBAAL,CAAyByE,OAAzB,CAAzB;AACA,gBAAMwB,gBAAgB,MAAM9C,gBAAgBqD,6BAAhB,CAA8CT,QAA9C,EAAwDO,kBAAxD,EAA4EC,gBAA5E,CAA5B;AACA,cAAIN,cAAcrD,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,kBAAMuD,gBAAgBF,cAAcD,GAAd,CAAkB;AAAA,qBAAM,QAAOjC,EAAE6B,QAAS,IAAG7B,EAAES,UAAW,IAAGT,EAAEW,QAAS,GAAtD;AAAA,aAAlB,EAA4E2B,IAA5E,CAAiF,IAAjF,CAAtB;AACA,mBAAO,OAAK9D,IAAL,CAAW,iBAAgB4D,aAAc,EAAzC,CAAP;AACD;AACF;;AAED;;AAEA;AACA,YAAIM,kBAAkB,CAAtB;AACA,YAAIC,aAAa,CAAjB;AACA,YAAIC,aAAa,CAAjB;;AAEA,eAAOA,aAAaD,UAApB,EAAgC;AAC9B,cAAI;AACF;AACA,kBAAME,iBAAiB,MAAMxF,WAAWyF,KAAX,CAAiB,gCAAjB,EAAmDC,IAAnD,EAA7B;AACA,gBAAIF,kBAAkBA,eAAeG,SAArC,EAAgD;AAC9CN,gCAAkBO,SAASJ,eAAeG,SAAxB,IAAqC,CAAvD;AACD;AACDzE,oBAAQG,GAAR,CAAa,gBAAegE,eAAgB,QAAOE,aAAa,CAAE,IAAGD,UAAW,GAAhF;AACA;AACD,WARD,CAQE,OAAOrE,KAAP,EAAc;AACdsE;AACArE,oBAAQD,KAAR,CAAe,gBAAesE,UAAW,IAAGD,UAAW,IAAvD,EAA4DrE,KAA5D;AACA,gBAAIsE,cAAcD,UAAlB,EAA8B;AAC5B;AACAD,gCAAkBtG,KAAKmE,GAAL,KAAa,OAA/B,CAF4B,CAEY;AACxChC,sBAAQG,GAAR,CAAY,cAAZ,EAA4BgE,eAA5B;AACD,aAJD,MAIO;AACL;AACA,oBAAM,IAAIQ,OAAJ,CAAY;AAAA,uBAAWC,WAAWC,OAAX,EAAoB,MAAMR,UAA1B,CAAX;AAAA,eAAZ,CAAN;AACD;AACF;AACF;;AAED;AACA,cAAMS,YAAY;AAChBhB,wBAAcK,eADE;AAEhBjD,sBAAYV,KAAKU,UAAL,IAAoB,IAAGiD,eAAgB,KAFnC;AAGhBjC,sBAAY,OAAKxE,mBAAL,CAAyBuE,SAAzB,CAHI;AAIhBG,oBAAU,OAAK1E,mBAAL,CAAyByE,OAAzB,CAJM;AAKhB9C,kBAAQ,UALQ;AAMhB0F,sBAAYC,MAAMC,MAAN,IAAgB;AANZ,SAAlB;;AASAjF,gBAAQG,GAAR,CAAY,SAAZ,EAAuB2E,SAAvB;;AAEA;AACA,YAAII,OAAJ;AACA,YAAI;AACFA,oBAAU,MAAMpG,WAAWqG,GAAX,CAAeL,SAAf,CAAhB;AACA,cAAI,CAACI,OAAL,EAAc;AACZ,kBAAM,IAAIE,KAAJ,CAAU,QAAV,CAAN;AACD;AACDpF,kBAAQG,GAAR,CAAY,YAAZ,EAA0B+E,OAA1B;AACD,SAND,CAME,OAAOnF,KAAP,EAAc;AACdC,kBAAQD,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACAC,kBAAQD,KAAR,CAAc,OAAd,EAAuBA,MAAMQ,OAA7B;AACAP,kBAAQD,KAAR,CAAc,OAAd,EAAuB+E,SAAvB;;AAEA;AACA,cAAI/E,MAAMsF,IAAN,KAAe,cAAnB,EAAmC;AACjCrF,oBAAQG,GAAR,CAAY,uBAAZ;;AAEA,gBAAImF,eAAe,KAAnB;AACA,gBAAIC,gBAAgB,CAApB;;AAEA,iBAAK,IAAIlC,IAAI,CAAb,EAAgBA,IAAIkC,aAApB,EAAmClC,GAAnC,EAAwC;AACtC,kBAAI;AACF;AACA,sBAAM,IAAIsB,OAAJ,CAAY;AAAA,yBAAWC,WAAWC,OAAX,EAAoB,OAAOxB,IAAI,CAAX,CAApB,CAAX;AAAA,iBAAZ,CAAN;;AAEA;AACA,sBAAMmC,iBAAiB,MAAM1G,WAAWyF,KAAX,CAAiB,gCAAjB,EAAmDC,IAAnD,EAA7B;AACA,sBAAMiB,kBAAkBD,kBAAkBA,eAAef,SAAjC,GACtBC,SAASc,eAAef,SAAxB,IAAqC,CADf,GAEtB5G,KAAKmE,GAAL,KAAa,OAFf,CANE,CAQsB;;AAExB;AACA8C,0BAAUhB,YAAV,GAAyB2B,eAAzB;AACAX,0BAAU5D,UAAV,GAAuBV,KAAKU,UAAL,IAAoB,IAAGuE,eAAgB,KAA9D;;AAEAzF,wBAAQG,GAAR,CAAa,YAAWkD,IAAI,CAAE,KAA9B,EAAoCyB,SAApC;AACAI,0BAAU,MAAMpG,WAAWqG,GAAX,CAAeL,SAAf,CAAhB;;AAEA,oBAAII,OAAJ,EAAa;AACXlF,0BAAQG,GAAR,CAAY,cAAZ,EAA4B+E,OAA5B;AACAf,oCAAkBsB,eAAlB,CAFW,CAEwB;AACnCH,iCAAe,IAAf;AACA;AACD;AACF,eAvBD,CAuBE,OAAOI,UAAP,EAAmB;AACnB1F,wBAAQD,KAAR,CAAe,cAAasD,IAAI,CAAE,KAAlC,EAAwCqC,UAAxC;AACA,oBAAIrC,MAAMkC,gBAAgB,CAA1B,EAA6B;AAC3B,wBAAM,IAAIH,KAAJ,CAAW,aAAYG,aAAc,MAAKG,WAAWnF,OAAQ,EAA7D,CAAN;AACD;AACF;AACF;;AAED,gBAAI,CAAC+E,YAAL,EAAmB;AACjB,oBAAM,IAAIF,KAAJ,CAAU,iBAAV,CAAN;AACD;AACF,WAzCD,MAyCO;AACL,kBAAM,IAAIA,KAAJ,CAAW,WAAUrF,MAAMQ,OAAQ,EAAnC,CAAN;AACD;AACF;;AAED;AACA,cAAMoF,YAAY,EAAlB;AACA,aAAK,MAAMC,SAAX,IAAwBpF,KAAKa,UAA7B,EAAyC;AACvCrB,kBAAQG,GAAR,CAAY,OAAZ,EAAqByF,UAAUtC,QAA/B;AACA,gBAAMnC,QAAQ,MAAMiC,WAAWhE,KAAX,CAAiB,EAAE6B,IAAI2E,UAAUtC,QAAhB,EAAjB,EAA6CkB,IAA7C,EAApB;AACA,cAAIQ,MAAMa,OAAN,CAAc1E,KAAd,CAAJ,EAA0B;AACxB,kBAAM,IAAIiE,KAAJ,CAAW,QAAOQ,UAAUtC,QAAS,MAArC,CAAN;AACD;;AAED;AACA,gBAAMwC,gBAAgBC,WAAW5E,MAAM6E,YAAjB,KAAkC,CAAxD;AACA,gBAAMC,aAAaF,WAAWH,UAAUrC,WAArB,KAAqC,CAAxD;AACA,gBAAM2C,eAAeJ,gBAAgB,CAAhB,GAAoBxD,KAAKtB,KAAL,CAAW,CAAC,IAAIiF,aAAaH,aAAlB,IAAmC,GAA9C,CAApB,GAAyE,CAA9F;;AAEAH,oBAAUQ,IAAV,CAAe;AACb7C,sBAAUsC,UAAUtC,QADP;AAEb8C,wBAAYjF,MAAMkF,IAFL;AAGbC,yBAAanF,MAAMoF,YAAN,IAAsB,EAHtB;AAIbC,4BAAgBV,aAJH;AAKbvC,yBAAa0C,UALA;AAMbQ,2BAAeP,YANF;AAObxE,mBAAOkE,UAAUlE,KAPJ;AAQbgF,4BAAgBd,UAAUc,cAAV,IAA4B;AAR/B,WAAf;AAUD;;AAED1G,gBAAQG,GAAR,CAAY,cAAZ,EAA4BwF,UAAUrF,MAAtC;AACA;AACA,cAAMO,gBAAgB8F,aAAhB,CAA8BzB,OAA9B,EAAuCS,SAAvC,CAAN;;AAEA,cAAMiB,SAAS;AACb3F,cAAIiE,OADS;AAEbpB,wBAAcK,eAFD;AAGbjC,sBAAY4C,UAAU5C,UAHT;AAIbE,oBAAU0C,UAAU1C,QAJP;AAKbd,uBAAaqE,UAAUrF;AALV,SAAf;;AAQAN,gBAAQG,GAAR,CAAY,WAAZ,EAAyByG,MAAzB;AACA,eAAO,OAAK9G,OAAL,CAAa;AAClBS,mBAAS,QADS;AAElBC,gBAAMoG;AAFY,SAAb,CAAP;AAKD,OA5ND,CA4NE,OAAO7G,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAhOkB;AAiOpB;;AAED;;;;AAIMsG,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAMzD,aAAa,OAAKrE,KAAL,CAAW,OAAX,CAAnB;AACA,cAAM+H,eAAe,OAAK/H,KAAL,CAAW,SAAX,CAArB;AACA,cAAM8B,kBAAkB,OAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMgI,WAAW,MAAM3D,WAAWhE,KAAX,CAAiB;AACtC4H,sBAAY,CAD0B;AAEtCC,qBAAW;AAF2B,SAAjB,EAGpB1C,KAHoB,CAGd,oDAHc,EAGwClE,MAHxC,EAAvB;;AAKA;AACA,cAAM6G,iBAAiB,MAAMrG,gBAAgBsG,KAAhB,CAAsB,IAAtB,EAC1BpD,IAD0B,CACrB;AACJqD,iBAAO,mBADH;AAEJrD,gBAAM,OAFF;AAGJsD,cAAI,GAHA;AAIJC,cAAI,CAAC,aAAD,EAAgB,MAAhB;AAJA,SADqB,EAO1BlI,KAP0B,CAOpB;AACL,sBAAY,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AADP,SAPoB,EAU1BmF,KAV0B,CAUpB,aAVoB,EAW1BlE,MAX0B,EAA7B;;AAaA,cAAMkH,YAAYL,eAAexD,GAAf,CAAmB;AAAA,iBAAQ8D,KAAKlE,QAAb;AAAA,SAAnB,CAAlB;;AAEA;AACA,cAAMqC,YAAY,MAAMhB,QAAQ8C,GAAR,CAAYV,SAASrD,GAAT;AAAA,uCAAa,WAAOvC,KAAP,EAAiB;AAChE;AACA,kBAAMuG,WAAW,MAAMZ,aACpB1H,KADoB,CACd;AACLkE,wBAAUnC,MAAMF,EADX;AAELgG,yBAAW;AAFN,aADc,EAKpB1C,KALoB,CAKd,4BALc,EAMpBlE,MANoB,EAAvB;;AAQA,gBAAIsH,aAAa,IAAjB;AACA,gBAAIC,aAAa,CAAjB;;AAEA,gBAAIF,YAAYA,SAASpH,MAAT,GAAkB,CAAlC,EAAqC;AACnC,oBAAMuH,SAASH,SAAShE,GAAT,CAAa;AAAA,uBAAKqC,WAAW+B,EAAE9B,YAAb,CAAL;AAAA,eAAb,EAA8C+B,MAA9C,CAAqD;AAAA,uBAAKD,IAAI,CAAT;AAAA,eAArD,CAAf;AACAF,2BAAaF,SAASlG,MAAT,CAAgB,UAAC3B,GAAD,EAAMiI,CAAN;AAAA,uBAAYjI,OAAOiI,EAAEE,YAAF,IAAkB,CAAzB,CAAZ;AAAA,eAAhB,EAAyD,CAAzD,CAAb;;AAEA,kBAAIH,OAAOvH,MAAP,GAAgB,CAApB,EAAuB;AACrB,sBAAM2H,WAAW3F,KAAK4F,GAAL,CAAS,GAAGL,MAAZ,CAAjB;AACA,sBAAMM,WAAW7F,KAAKC,GAAL,CAAS,GAAGsF,MAAZ,CAAjB;AACAF,6BAAc,IAAGM,SAASG,OAAT,CAAiB,CAAjB,CAAoB,OAAMD,SAASC,OAAT,CAAiB,CAAjB,CAAoB,EAA/D;AACD;AACF;;AAED;AACA,kBAAMC,cAAcT,aAAa,CAAb,GAAiBA,UAAjB,GAA+BzG,MAAM6G,YAAN,IAAsB,CAAzE;AACA,kBAAMM,gBAAgBf,UAAUgB,QAAV,CAAmBpH,MAAMF,EAAzB,CAAtB;;AAEA,qCACKE,KADL;AAEEqH,2BAAab,UAFf;AAGEc,4BAAcJ,WAHhB;AAIEK,gCAAkBJ,aAJpB;AAKEK,0BAAY,CAACL,aAAD,IAAkBD,cAAc,CAL9C;AAMEO,uBAASP,eAAe,CAAf,GAAmB,MAAnB,GAA6BC,gBAAgB,SAAhB,GAA4B;AANpE;AAQD,WApCmC;;AAAA;AAAA;AAAA;AAAA,aAAZ,CAAxB;;AAsCA,eAAO,OAAKxI,OAAL,CAAa6F,SAAb,CAAP;AAED,OApED,CAoEE,OAAO5F,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAxEiB;AAyEnB;;AAED;;;AAGM4I,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAMC,cAAc,OAAK/J,KAAL,CAAW,mBAAX,CAApB;;AAEA,YAAI,OAAKgK,KAAT,EAAgB;AACd;AACA,gBAAMC,SAAS,MAAMF,YAAY1J,KAAZ,CAAkB,EAAE6B,IAAI,CAAN,EAAlB,EAA6BuD,IAA7B,EAArB;AACA,iBAAO,OAAK1E,OAAL,CAAakJ,UAAU,EAAvB,CAAP;AACD,SAJD,MAIO;AACL;AACA,gBAAMxI,OAAO,OAAKqC,IAAL,EAAb;AACA,gBAAMiG,YAAY1J,KAAZ,CAAkB,EAAE6B,IAAI,CAAN,EAAlB,EAA6BgI,MAA7B,CAAoCzI,IAApC,CAAN;AACA,iBAAO,OAAKV,OAAL,CAAa,QAAb,CAAP;AACD;AAEF,OAdD,CAcE,OAAOC,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,QAAV,CAAP;AACD;AAlBkB;AAmBpB;;AAED;;;;AAIMiJ,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAMhE,UAAU,OAAKrC,IAAL,CAAU,UAAV,CAAhB;AACA,YAAI,CAACqC,OAAL,EAAc;AACZ,iBAAO,OAAKjF,IAAL,CAAU,SAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,gBAAZ;AACAH,gBAAQG,GAAR,CAAY,OAAZ,EAAqB+E,OAArB;;AAEA,cAAMpG,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMiC,QAAQ,MAAMlC,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkCV,IAAlC,EAApB;AACA,YAAIQ,MAAMa,OAAN,CAAc7E,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKf,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIe,MAAM3B,MAAN,KAAiB,OAArB,EAA8B;AAC5B,iBAAO,OAAKY,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,cAAMnB,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkC+D,MAAlC,CAAyC;AAC7C5J,kBAAQ,OADqC;AAE7C8J,2BAAiB,CAF4B;AAG7CC,qBAAW,OAAK1L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB,CAHkC;AAI7CwL,sBAAY,OAAK3L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB;AAJiC,SAAzC,CAAN;;AAOAmC,gBAAQG,GAAR,CAAY,WAAZ,EAAyB+E,OAAzB;AACA,eAAO,OAAKpF,OAAL,CAAa;AAClBS,mBAAS,OADS;AAElBC,gBAAM,EAAES,IAAIiE,OAAN,EAAe7F,QAAQ,OAAvB,EAAgC8J,iBAAiB,IAAjD;AAFY,SAAb,CAAP;AAKD,OApCD,CAoCE,OAAOpJ,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAxCiB;AAyCnB;;AAED;;;;AAIM+I,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACFtJ,gBAAQG,GAAR,CAAY,kBAAZ;;AAEA,cAAMoJ,YAAY,IAAIhM,uBAAJ,EAAlB;AACA,cAAMqJ,SAAS,MAAM2C,UAAUC,iBAAV,CAA4B,OAAKzK,KAAL,CAAW0K,IAAX,QAA5B,CAArB;;AAEA,eAAO,OAAK3J,OAAL,CAAa;AAClBS,mBAAS,QADS;AAElBqG,kBAAQA;AAFU,SAAb,CAAP;AAKD,OAXD,CAWE,OAAO7G,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAfwB;AAgB1B;;AAED;;;;AAIMmJ,eAAN,GAAsB;AAAA;;AAAA;AACpB,UAAI;AACF,cAAM,EAAEC,QAAF,EAAYC,cAAZ,EAA4BC,YAA5B,KAA6C,QAAKhH,IAAL,EAAnD;AACA,YAAI,CAAC8G,QAAL,EAAe;AACb,iBAAO,QAAK1J,IAAL,CAAU,SAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,gBAAZ;AACAH,gBAAQG,GAAR,CAAY,OAAZ,EAAqBwJ,QAArB;;AAEA,cAAM7K,aAAa,QAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMiC,QAAQ,MAAMlC,WAAWM,KAAX,CAAiB,EAAE6B,IAAI0I,QAAN,EAAjB,EAAmCnF,IAAnC,EAApB;AACA,YAAIQ,MAAMa,OAAN,CAAc7E,KAAd,CAAJ,EAA0B;AACxB,iBAAO,QAAKf,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIe,MAAM3B,MAAN,KAAiB,OAArB,EAA8B;AAC5B,iBAAO,QAAKY,IAAL,CAAU,gBAAV,CAAP;AACD;;AAED,cAAM6J,aAAa;AACjBzK,kBAAQ,UADS;AAEjB8J,2BAAiB,CAFA;AAGjBC,qBAAW,IAHM;AAIjBC,sBAAY,QAAK3L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB;AAJK,SAAnB;;AAOA;AACA,YAAI+L,cAAJ,EAAoB;AAClBE,qBAAW5H,UAAX,GAAwB,QAAKxE,mBAAL,CAAyB,IAAIG,IAAJ,CAAS+L,cAAT,CAAzB,CAAxB;AACD;AACD,YAAIC,YAAJ,EAAkB;AAChBC,qBAAW1H,QAAX,GAAsB,QAAK1E,mBAAL,CAAyB,IAAIG,IAAJ,CAASgM,YAAT,CAAzB,CAAtB;AACD;;AAED,cAAM/K,WAAWM,KAAX,CAAiB,EAAE6B,IAAI0I,QAAN,EAAjB,EAAmCV,MAAnC,CAA0Ca,UAA1C,CAAN;;AAEA9J,gBAAQG,GAAR,CAAY,WAAZ,EAAyBwJ,QAAzB;AACA,eAAO,QAAK7J,OAAL,CAAa;AAClBS,mBAAS,SADS;AAElBC,gBAAM,EAAES,IAAI0I,QAAN,EAAgBtK,QAAQ,UAAxB;AAFY,SAAb,CAAP;AAKD,OA7CD,CA6CE,OAAOU,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,QAAKE,IAAL,CAAU,aAAaF,MAAMQ,OAA7B,CAAP;AACD;AAjDmB;AAkDrB;;AAED;;;;AAIMwJ,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM,EAAEJ,QAAF,EAAYK,cAAZ,KAA+B,QAAKnH,IAAL,EAArC;AACA,YAAI,CAAC8G,QAAD,IAAa,CAACK,cAAlB,EAAkC;AAChC,iBAAO,QAAK/J,IAAL,CAAU,cAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,cAAZ;AACAH,gBAAQG,GAAR,CAAY,OAAZ,EAAqBwJ,QAArB,EAA+B,OAA/B,EAAwCK,cAAxC;;AAEA,cAAMlL,aAAa,QAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMiC,QAAQ,MAAMlC,WAAWM,KAAX,CAAiB,EAAE6B,IAAI0I,QAAN,EAAjB,EAAmCnF,IAAnC,EAApB;AACA,YAAIQ,MAAMa,OAAN,CAAc7E,KAAd,CAAJ,EAA0B;AACxB,iBAAO,QAAKf,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIe,MAAM3B,MAAN,KAAiB,QAArB,EAA+B;AAC7B,iBAAO,QAAKY,IAAL,CAAU,cAAV,CAAP;AACD;;AAED;AACA,cAAMgK,iBAAiB,IAAIpM,IAAJ,CAASmD,MAAMoB,QAAf,CAAvB;AACA,cAAM8H,aAAa,IAAIrM,IAAJ,CAASoM,eAAe9G,OAAf,KAA2B6G,iBAAiB,EAAjB,GAAsB,IAA1D,CAAnB;;AAEA,cAAMlL,WAAWM,KAAX,CAAiB,EAAE6B,IAAI0I,QAAN,EAAjB,EAAmCV,MAAnC,CAA0C;AAC9C7G,oBAAU,QAAK1E,mBAAL,CAAyBwM,UAAzB,CADoC;AAE9Cb,sBAAY,QAAK3L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB;AAFkC,SAA1C,CAAN;;AAKAmC,gBAAQG,GAAR,CAAY,SAAZ,EAAuBwJ,QAAvB;AACA,eAAO,QAAK7J,OAAL,CAAa;AAClBS,mBAAU,QAAOyJ,cAAe,IADd;AAElBxJ,gBAAM,EAAES,IAAI0I,QAAN,EAAgBE,cAAcK,UAA9B;AAFY,SAAb,CAAP;AAKD,OArCD,CAqCE,OAAOnK,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,QAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAzCkB;AA0CpB;;AAED;;;;AAIM4J,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACF,cAAM,EAAER,QAAF,EAAYC,cAAZ,EAA4BC,YAA5B,EAA0CO,cAA1C,KAA6D,QAAKvH,IAAL,EAAnE;AACA,YAAI,CAAC8G,QAAD,IAAa,CAACC,cAAd,IAAgC,CAACC,YAArC,EAAmD;AACjD,iBAAO,QAAK5J,IAAL,CAAU,gBAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,cAAZ;AACAH,gBAAQG,GAAR,CAAY,QAAZ,EAAsBwJ,QAAtB;;AAEA,cAAM7K,aAAa,QAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,QAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMsL,cAAc,MAAMvL,WAAWM,KAAX,CAAiB,EAAE6B,IAAI0I,QAAN,EAAjB,EAAmCnF,IAAnC,EAA1B;AACA,YAAIQ,MAAMa,OAAN,CAAcwE,WAAd,CAAJ,EAAgC;AAC9B,iBAAO,QAAKpK,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,cAAMqK,YAAY,MAAMxL,WAAWgC,KAAX,CAAiB,mBAAjB,EAAsC0D,IAAtC,EAAxB;AACA,cAAML,kBAAmBmG,aAAaA,UAAUxG,YAAxB,GAAwCwG,UAAUxG,YAAV,GAAyB,CAAjE,GAAqE,CAA7F;;AAEA;AACA,cAAMyG,eAAe;AACnBzG,wBAAcK,eADK;AAEnBjD,sBAAYkJ,kBAAmB,GAAEC,YAAYnJ,UAAW,OAFrC;AAGnBgB,sBAAY,QAAKxE,mBAAL,CAAyB,IAAIG,IAAJ,CAAS+L,cAAT,CAAzB,CAHO;AAInBxH,oBAAU,QAAK1E,mBAAL,CAAyB,IAAIG,IAAJ,CAASgM,YAAT,CAAzB,CAJS;AAKnBxK,kBAAQ,UALW;AAMnB0F,sBAAYC,MAAMC,MAAN,IAAgB;AANT,SAArB;;AASA,cAAMuF,aAAa,MAAM1L,WAAWqG,GAAX,CAAeoF,YAAf,CAAzB;AACA,YAAI,CAACC,UAAL,EAAiB;AACf,gBAAM,IAAIpF,KAAJ,CAAU,SAAV,CAAN;AACD;;AAED;AACA,cAAMqF,cAAc,MAAM5J,gBAAgBzB,KAAhB,CAAsB,EAAEuK,UAAUA,QAAZ,EAAtB,EAA8CtJ,MAA9C,EAA1B;AACA,YAAIoK,YAAYnK,MAAZ,GAAqB,CAAzB,EAA4B;AAC1B,gBAAMoK,eAAeD,YAAY/G,GAAZ,CAAgB;AAAA,mBAAU;AAC7CiG,wBAAUa,UADmC;AAE7ClH,wBAAUnC,MAAMmC,QAF6B;AAG7C8C,0BAAYjF,MAAMiF,UAH2B;AAI7CE,2BAAanF,MAAMmF,WAJ0B;AAK7CE,8BAAgBrF,MAAMqF,cALuB;AAM7CjD,2BAAapC,MAAMoC,WAN0B;AAO7CkD,6BAAetF,MAAMsF,aAPwB;AAQ7C/E,qBAAOP,MAAMO,KARgC;AAS7CE,0BAAY,CATiC,EAS9B;AACf8E,8BAAgBvF,MAAMuF,cAVuB;AAW7CiE,0BAAYxJ,MAAMwJ;AAX2B,aAAV;AAAA,WAAhB,CAArB;;AAcA,gBAAM9J,gBAAgB+J,OAAhB,CAAwBF,YAAxB,CAAN;AACD;;AAED1K,gBAAQG,GAAR,CAAY,SAAZ,EAAuBqK,UAAvB;AACA,eAAO,QAAK1K,OAAL,CAAa;AAClBS,mBAAS,QADS;AAElBC,gBAAM,EAAES,IAAIuJ,UAAN,EAAkB1G,cAAcK,eAAhC;AAFY,SAAb,CAAP;AAKD,OA/DD,CA+DE,OAAOpE,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,QAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAnEgB;AAoElB;;AAED;;;;AAIMsK,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM3F,UAAU,QAAKrC,IAAL,CAAU,UAAV,CAAhB;AACA,YAAI,CAACqC,OAAL,EAAc;AACZ,iBAAO,QAAKjF,IAAL,CAAU,SAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,cAAZ;AACAH,gBAAQG,GAAR,CAAY,OAAZ,EAAqB+E,OAArB;;AAEA,cAAMpG,aAAa,QAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,QAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMiC,QAAQ,MAAMlC,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkCV,IAAlC,EAApB;AACA,YAAIQ,MAAMa,OAAN,CAAc7E,KAAd,CAAJ,EAA0B;AACxB,iBAAO,QAAKf,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIe,MAAM3B,MAAN,KAAiB,QAAjB,IAA6B2B,MAAM3B,MAAN,KAAiB,UAAlD,EAA8D;AAC5DW,kBAAQG,GAAR,CAAY,QAAZ,EAAsBa,MAAM3B,MAA5B,EAAoC,OAApC;AACA,gBAAMP,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkC+D,MAAlC,CAAyC;AAC7C5J,oBAAQ,OADqC;AAE7C8J,6BAAiB,CAF4B;AAG7CC,uBAAW,QAAK1L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB,CAHkC;AAI7CwL,wBAAY,QAAK3L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB;AAJiC,WAAzC,CAAN;AAMD;;AAED;AACA,cAAMgD,gBAAgBzB,KAAhB,CAAsB,EAAEuK,UAAUzE,OAAZ,EAAtB,EAA6C4F,MAA7C,EAAN;AACA9K,gBAAQG,GAAR,CAAY,WAAZ;;AAEA;AACA,cAAMrB,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkC4F,MAAlC,EAAN;AACA9K,gBAAQG,GAAR,CAAY,SAAZ;;AAEA,eAAO,QAAKL,OAAL,CAAa;AAClBS,mBAAS,QADS;AAElBC,gBAAM,EAAES,IAAIiE,OAAN;AAFY,SAAb,CAAP;AAKD,OA1CD,CA0CE,OAAOnF,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,QAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AA9CkB;AA+CpB;;AAED;;;;AAIMwK,mBAAN,GAA0B;AAAA;;AAAA;AACxB,UAAI;AACF,cAAMC,WAAW,QAAKnI,IAAL,CAAU,WAAV,CAAjB;AACA,YAAI,CAACmI,QAAD,IAAa,CAAChI,MAAMC,OAAN,CAAc+H,QAAd,CAAd,IAAyCA,SAAS1K,MAAT,KAAoB,CAAjE,EAAoE;AAClE,iBAAO,QAAKL,IAAL,CAAU,eAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,gBAAZ;AACAH,gBAAQG,GAAR,CAAY,QAAZ,EAAsB6K,QAAtB;;AAEA,cAAMlM,aAAa,QAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM8B,kBAAkB,QAAK9B,KAAL,CAAW,wBAAX,CAAxB;;AAEA,YAAIkM,eAAe,CAAnB;AACA,YAAIC,YAAY,CAAhB;AACA,cAAMC,UAAU,EAAhB;;AAEA,aAAK,MAAMjG,OAAX,IAAsB8F,QAAtB,EAAgC;AAC9B,cAAI;AACF;AACA,kBAAMhK,QAAQ,MAAMlC,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkCV,IAAlC,EAApB;AACA,gBAAIQ,MAAMa,OAAN,CAAc7E,KAAd,CAAJ,EAA0B;AACxBkK;AACAC,sBAAQhF,IAAR,CAAa,EAAElF,IAAIiE,OAAN,EAAepF,SAAS,KAAxB,EAA+BS,SAAS,OAAxC,EAAb;AACA;AACD;;AAED;AACA,gBAAIS,MAAM3B,MAAN,KAAiB,QAAjB,IAA6B2B,MAAM3B,MAAN,KAAiB,UAAlD,EAA8D;AAC5D,oBAAMP,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkC+D,MAAlC,CAAyC;AAC7C5J,wBAAQ,OADqC;AAE7C8J,iCAAiB,CAF4B;AAG7CC,2BAAW,QAAK1L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB,CAHkC;AAI7CwL,4BAAY,QAAK3L,mBAAL,CAAyB,IAAIG,IAAJ,EAAzB;AAJiC,eAAzC,CAAN;AAMD;;AAED;AACA,kBAAMgD,gBAAgBzB,KAAhB,CAAsB,EAAEuK,UAAUzE,OAAZ,EAAtB,EAA6C4F,MAA7C,EAAN;;AAEA;AACA,kBAAMhM,WAAWM,KAAX,CAAiB,EAAE6B,IAAIiE,OAAN,EAAjB,EAAkC4F,MAAlC,EAAN;;AAEAG;AACAE,oBAAQhF,IAAR,CAAa,EAAElF,IAAIiE,OAAN,EAAepF,SAAS,IAAxB,EAA8BS,SAAS,MAAvC,EAAb;AAED,WA5BD,CA4BE,OAAOR,KAAP,EAAc;AACdmL;AACAC,oBAAQhF,IAAR,CAAa,EAAElF,IAAIiE,OAAN,EAAepF,SAAS,KAAxB,EAA+BS,SAASR,MAAMQ,OAA9C,EAAb;AACD;AACF;;AAED,eAAO,QAAKT,OAAL,CAAa;AAClBS,mBAAU,aAAY0K,YAAa,SAAQC,SAAU,IADnC;AAElB1K,gBAAM;AACJyK,wBADI;AAEJC,qBAFI;AAGJC;AAHI;AAFY,SAAb,CAAP;AASD,OA5DD,CA4DE,OAAOpL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,QAAKE,IAAL,CAAU,aAAaF,MAAMQ,OAA7B,CAAP;AACD;AAhEuB;AAiEzB;;AAED;;;;AAIM6K,iBAAN,GAAwB;AAAA;;AAAA;AACtB,UAAI;AACF,cAAM7B,YAAY,IAAIhM,uBAAJ,EAAlB;AACA,cAAM8N,cAAc9B,UAAU+B,yBAAV,EAApB;AACA,cAAMtJ,MAAM,IAAInE,IAAJ,EAAZ;;AAEA;AACA,cAAMyB,iBAAiB,MAAM,QAAKP,KAAL,CAAW,mBAAX,EAAgCK,KAAhC,CAAsC;AACjEC,kBAAQ;AADyD,SAAtC,EAE1BgB,MAF0B,EAA7B;;AAIA,cAAMkL,YAAY;AAChBC,8BAAoBH,WADJ;AAEhBI,2BAAiBzJ,IAAI0J,cAAJ,CAAmB,OAAnB,CAFD;AAGhBC,4BAAkB3J,IAAI4J,WAAJ,EAHF;AAIhBC,2BAAiBvM,eAAeoE,GAAf,CAAmB;AAAA,mBAAM;AACxCzC,kBAAI6K,EAAE7K,EADkC;AAExC6C,4BAAcgI,EAAEhI,YAFwB;AAGxC5C,0BAAY4K,EAAE5K,UAH0B;AAIxCgB,0BAAY4J,EAAE5J,UAJ0B;AAKxCE,wBAAU0J,EAAE1J,QAL4B;AAMxC/C,sBAAQyM,EAAEzM,MAN8B;AAOxC0M,gCAAkBD,EAAE5J,UAAF,IAAgBmJ,WAPM;AAQxCW,+BAAiBF,EAAE1J,QAAF,GAAaiJ;AARU,aAAN;AAAA,WAAnB;AAJD,SAAlB;;AAgBA,eAAO,QAAKvL,OAAL,CAAayL,SAAb,CAAP;AAED,OA5BD,CA4BE,OAAOxL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,QAAKE,IAAL,CAAU,eAAeF,MAAMQ,OAA/B,CAAP;AACD;AAhCqB;AAiCvB;;AAED;;;AAGM+I,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACFtJ,gBAAQG,GAAR,CAAY,kBAAZ;;AAEA,cAAM5C,0BAA0BD,QAAQ,iDAAR,CAAhC;AACA,cAAMiM,YAAY,IAAIhM,uBAAJ,EAAlB;;AAEA;AACA,cAAMqJ,SAAS,MAAM2C,UAAUC,iBAAV,CAA4B,QAAKzK,KAAL,CAAW0K,IAAX,SAA5B,CAArB;;AAEA;AACA,cAAMwC,QAAQ,MAAM1C,UAAU2C,oBAAV,CAA+B,QAAKnN,KAAL,CAAW0K,IAAX,SAA/B,CAApB;;AAEA,eAAO,QAAK3J,OAAL,CAAa;AAClBS,mBAAS,UADS;AAElBC,gBAAM;AACJ2L,qBAASvF,MADL;AAEJqF,mBAAOA;AAFH;AAFY,SAAb,CAAP;AAQD,OApBD,CAoBE,OAAOlM,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,QAAKE,IAAL,CAAU,aAAaF,MAAMQ,OAA7B,CAAP;AACD;AAxBwB;AAyB1B;;AAr9BiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\flashsalemulti.js", "sourcesContent": ["const Base = require('./base.js');\nconst FlashSaleMultiScheduler = require('../../common/service/flash_sale_multi_scheduler');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 格式化本地时间为数据库格式（避免时区问题）\n   * @param {Date} date 日期对象\n   * @returns {string} 格式化的时间字符串 YYYY-MM-DD HH:mm:ss\n   */\n  formatLocalDateTime(date) {\n    const d = new Date(date);\n    const year = d.getFullYear();\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    const hours = String(d.getHours()).padStart(2, '0');\n    const minutes = String(d.getMinutes()).padStart(2, '0');\n    const seconds = String(d.getSeconds()).padStart(2, '0');\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n  }\n\n  /**\n   * 格式化本地日期为数据库格式（避免时区问题）\n   * @param {Date} date 日期对象\n   * @returns {string} 格式化的日期字符串 YYYY-MM-DD\n   */\n  formatLocalDate(date) {\n    const d = new Date(date);\n    const year = d.getFullYear();\n    const month = String(d.getMonth() + 1).padStart(2, '0');\n    const day = String(d.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  /**\n   * 获取统计数据\n   * GET /admin/flashsalemulti/statistics\n   */\n  async statisticsAction() {\n    try {\n      const roundModel = this.model('flash_sale_rounds');\n      const orderModel = this.model('flash_sale_orders');\n\n      // 统计轮次数据\n      const totalRounds = await roundModel.count();\n      const activeRounds = await roundModel.where({ status: 'active' }).count();\n      const upcomingRounds = await roundModel.where({ status: 'upcoming' }).count();\n      const endedRounds = await roundModel.where({ status: 'ended' }).count();\n\n      // 统计订单数据\n      const totalOrders = await orderModel.count();\n      const today = this.formatLocalDate(new Date());\n      const todayOrders = await orderModel.where({\n        created_at: ['>=', today + ' 00:00:00']\n      }).count();\n\n      // 统计销售额\n      const totalSales = await orderModel.sum('total_amount') || 0;\n\n      return this.success({\n        totalRounds,\n        activeRounds,\n        upcomingRounds,\n        endedRounds,\n        totalOrders,\n        todayOrders,\n        totalSales\n      });\n\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail('获取统计数据失败');\n    }\n  }\n\n  /**\n   * 测试API - 简单查询轮次\n   * GET /admin/flashsalemulti/test\n   */\n  async testAction() {\n    try {\n      console.log('=== 测试API调用 ===');\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 简单查询所有轮次\n      const rounds = await roundModel.select();\n      console.log('测试查询结果:', rounds.length);\n\n      return this.success({\n        message: '测试成功',\n        count: rounds.length,\n        data: rounds\n      });\n    } catch (error) {\n      console.error('测试API失败:', error);\n      return this.fail('测试失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取轮次列表\n   * GET /admin/flashsalemulti/list\n   */\n  async listAction() {\n    try {\n      console.log('=== 获取轮次列表 ===');\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const status = this.get('status');\n\n      console.log('查询参数:', { page, limit, status });\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      let where = {};\n      if (status) {\n        where.status = status;\n      }\n\n      // 获取轮次列表\n      console.log('查询条件:', where);\n      const rounds = await roundModel.where(where)\n        .order('round_number DESC')\n        .page(page, limit)\n        .countSelect();\n\n      console.log('查询到的轮次数量:', rounds.data ? rounds.data.length : 0);\n      console.log('轮次数据结构:', rounds);\n\n      // 为每个轮次获取商品信息\n      if (rounds.data && rounds.data.length > 0) {\n        for (let round of rounds.data) {\n          try {\n            console.log('处理轮次:', round.id, round.round_name);\n            const goods = await roundGoodsModel.getRoundGoodsList(round.id);\n            console.log('轮次商品数量:', goods.length);\n\n            round.goods_list = goods || [];\n            round.goods_count = goods ? goods.length : 0;\n\n            // 计算总库存和总销量\n            round.total_stock = goods ? goods.reduce((sum, g) => sum + (g.stock || 0), 0) : 0;\n            round.total_sold = goods ? goods.reduce((sum, g) => sum + (g.sold_count || 0), 0) : 0;\n          } catch (roundError) {\n            console.error('处理轮次失败:', round.id, roundError);\n            // 即使单个轮次处理失败，也继续处理其他轮次\n            round.goods_list = [];\n            round.goods_count = 0;\n            round.total_stock = 0;\n            round.total_sold = 0;\n          }\n        }\n      }\n\n      console.log('返回轮次列表，总数:', rounds.data.length);\n      return this.success(rounds);\n\n    } catch (error) {\n      console.error('获取轮次列表失败:', error);\n      return this.fail('获取轮次列表失败');\n    }\n  }\n\n  /**\n   * 获取当前轮次\n   * GET /admin/flashsalemulti/current\n   */\n  async currentAction() {\n    try {\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 获取当前活跃轮次\n      const currentRounds = await roundModel.where({ status: 'active' }).select();\n      \n      // 获取即将开始的轮次\n      const upcomingRounds = await roundModel.where({ status: 'upcoming' })\n        .order('start_time ASC')\n        .limit(5)\n        .select();\n\n      // 为每个轮次添加商品信息\n      for (let round of [...currentRounds, ...upcomingRounds]) {\n        const goods = await roundGoodsModel.getRoundGoodsList(round.id);\n        round.goods_list = goods;\n        round.goods_count = goods.length;\n        \n        // 计算倒计时\n        const now = new Date();\n        const startTime = new Date(round.start_time);\n        const endTime = new Date(round.end_time);\n        \n        if (round.status === 'active') {\n          round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));\n        } else if (round.status === 'upcoming') {\n          round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n        }\n      }\n\n      return this.success({\n        current: currentRounds,\n        upcoming: upcomingRounds,\n        total: currentRounds.length + upcomingRounds.length\n      });\n\n    } catch (error) {\n      console.error('获取当前轮次失败:', error);\n      return this.fail('获取当前轮次失败');\n    }\n  }\n\n  /**\n   * 创建新轮次（支持多商品）\n   * POST /admin/flashsalemulti/create\n   */\n  async createAction() {\n    try {\n      console.log('=== 创建新轮次（多商品） ===');\n\n      const data = this.post();\n      console.log('接收到的数据:', JSON.stringify(data, null, 2));\n\n      // 验证必填字段\n      if (!data.round_name) {\n        return this.fail('请填写轮次名称');\n      }\n\n      if (!data.start_time) {\n        return this.fail('请选择开始时间');\n      }\n\n      if (!data.end_time) {\n        return this.fail('请选择结束时间');\n      }\n\n      if (!data.goods_list || !Array.isArray(data.goods_list) || data.goods_list.length === 0) {\n        return this.fail('请选择参与秒杀的商品');\n      }\n\n      // 验证时间\n      const startTime = new Date(data.start_time);\n      const endTime = new Date(data.end_time);\n      const now = new Date();\n\n      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {\n        return this.fail('时间格式不正确');\n      }\n\n      if (startTime <= now) {\n        return this.fail('开始时间必须晚于当前时间');\n      }\n\n      if (endTime <= startTime) {\n        return this.fail('结束时间必须晚于开始时间');\n      }\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n      const goodsModel = this.model('goods');\n      \n      // 验证商品数据\n      for (let i = 0; i < data.goods_list.length; i++) {\n        const goods = data.goods_list[i];\n        if (!goods.goods_id || !goods.flash_price || !goods.stock) {\n          return this.fail(`第${i+1}个商品信息不完整`);\n        }\n      }\n\n      // 检查商品是否已参与其他活跃轮次\n      // 对于整点秒杀，允许商品在不同时段重复使用\n      if (!data.is_hourly_flash) {\n        const goodsIds = data.goods_list.map(g => g.goods_id);\n        const conflictGoods = await roundGoodsModel.checkGoodsInActiveRounds(goodsIds);\n        if (conflictGoods.length > 0) {\n          const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(轮次${g.round_number})`).join(', ');\n          return this.fail(`以下商品已参与其他活跃轮次: ${conflictNames}`);\n        }\n      } else {\n        // 整点秒杀：只检查时间重叠的轮次\n        const goodsIds = data.goods_list.map(g => g.goods_id);\n        // 确保时间格式正确\n        const formattedStartTime = this.formatLocalDateTime(startTime);\n        const formattedEndTime = this.formatLocalDateTime(endTime);\n        const conflictGoods = await roundGoodsModel.checkGoodsInOverlappingRounds(goodsIds, formattedStartTime, formattedEndTime);\n        if (conflictGoods.length > 0) {\n          const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(${g.start_time}-${g.end_time})`).join(', ');\n          return this.fail(`以下商品在该时段已有冲突: ${conflictNames}`);\n        }\n      }\n\n      // 移除系统配置依赖，直接使用用户提供的时间\n\n      // 获取下一个轮次编号 - 使用更安全的方法（多次重试）\n      let nextRoundNumber = 1;\n      let maxRetries = 5;\n      let retryCount = 0;\n\n      while (retryCount < maxRetries) {\n        try {\n          // 使用MAX函数获取最大轮次编号\n          const maxRoundResult = await roundModel.field('MAX(round_number) as max_round').find();\n          if (maxRoundResult && maxRoundResult.max_round) {\n            nextRoundNumber = parseInt(maxRoundResult.max_round) + 1;\n          }\n          console.log(`获取到的下一个轮次编号: ${nextRoundNumber} (尝试 ${retryCount + 1}/${maxRetries})`);\n          break;\n        } catch (error) {\n          retryCount++;\n          console.error(`获取轮次编号失败 (尝试 ${retryCount}/${maxRetries}):`, error);\n          if (retryCount >= maxRetries) {\n            // 如果所有重试都失败，使用时间戳作为后备方案\n            nextRoundNumber = Date.now() % 1000000; // 使用时间戳的后6位\n            console.log('使用时间戳作为轮次编号:', nextRoundNumber);\n          } else {\n            // 等待一小段时间后重试\n            await new Promise(resolve => setTimeout(resolve, 100 * retryCount));\n          }\n        }\n      }\n\n      // 创建轮次数据（只使用数据库中存在的字段）\n      const roundData = {\n        round_number: nextRoundNumber,\n        round_name: data.round_name || `第${nextRoundNumber}场秒杀`,\n        start_time: this.formatLocalDateTime(startTime),\n        end_time: this.formatLocalDateTime(endTime),\n        status: 'upcoming',\n        created_by: think.userId || 0\n      };\n\n      console.log('准备创建轮次:', roundData);\n\n      // 声明roundId变量在外层作用域\n      let roundId;\n      try {\n        roundId = await roundModel.add(roundData);\n        if (!roundId) {\n          throw new Error('创建轮次失败');\n        }\n        console.log('轮次创建成功，ID:', roundId);\n      } catch (error) {\n        console.error('创建轮次时发生错误:', error);\n        console.error('错误详情:', error.message);\n        console.error('轮次数据:', roundData);\n\n        // 如果是重复键错误，尝试多次重新获取轮次编号\n        if (error.code === 'ER_DUP_ENTRY') {\n          console.log('检测到轮次编号重复，尝试重新获取编号...');\n\n          let retrySuccess = false;\n          let retryAttempts = 3;\n\n          for (let i = 0; i < retryAttempts; i++) {\n            try {\n              // 等待一小段时间，避免并发冲突\n              await new Promise(resolve => setTimeout(resolve, 200 * (i + 1)));\n\n              // 重新获取最大轮次编号\n              const retryMaxResult = await roundModel.field('MAX(round_number) as max_round').find();\n              const retryNextNumber = retryMaxResult && retryMaxResult.max_round ?\n                parseInt(retryMaxResult.max_round) + 1 :\n                Date.now() % 1000000; // 使用时间戳作为后备\n\n              // 更新轮次数据\n              roundData.round_number = retryNextNumber;\n              roundData.round_name = data.round_name || `第${retryNextNumber}场秒杀`;\n\n              console.log(`重试创建轮次 (第${i + 1}次):`, roundData);\n              roundId = await roundModel.add(roundData);\n\n              if (roundId) {\n                console.log('重试轮次创建成功，ID:', roundId);\n                nextRoundNumber = retryNextNumber; // 更新轮次编号用于后续返回\n                retrySuccess = true;\n                break;\n              }\n            } catch (retryError) {\n              console.error(`重试创建轮次失败 (第${i + 1}次):`, retryError);\n              if (i === retryAttempts - 1) {\n                throw new Error(`创建轮次失败，已重试${retryAttempts}次: ${retryError.message}`);\n              }\n            }\n          }\n\n          if (!retrySuccess) {\n            throw new Error('创建轮次失败，所有重试都失败了');\n          }\n        } else {\n          throw new Error(`创建轮次失败: ${error.message}`);\n        }\n      }\n\n      // 获取商品详细信息并创建轮次商品\n      const goodsList = [];\n      for (const goodsItem of data.goods_list) {\n        console.log('处理商品:', goodsItem.goods_id);\n        const goods = await goodsModel.where({ id: goodsItem.goods_id }).find();\n        if (think.isEmpty(goods)) {\n          throw new Error(`商品ID ${goodsItem.goods_id} 不存在`);\n        }\n\n        // 计算折扣率\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const flashPrice = parseFloat(goodsItem.flash_price) || 0;\n        const discountRate = originalPrice > 0 ? Math.round((1 - flashPrice / originalPrice) * 100) : 0;\n\n        goodsList.push({\n          goods_id: goodsItem.goods_id,\n          goods_name: goods.name,\n          goods_image: goods.list_pic_url || '',\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: discountRate,\n          stock: goodsItem.stock,\n          limit_quantity: goodsItem.limit_quantity || 1\n        });\n      }\n\n      console.log('准备添加轮次商品，数量:', goodsList.length);\n      // 批量添加轮次商品\n      await roundGoodsModel.addRoundGoods(roundId, goodsList);\n\n      const result = {\n        id: roundId,\n        round_number: nextRoundNumber,\n        start_time: roundData.start_time,\n        end_time: roundData.end_time,\n        goods_count: goodsList.length\n      };\n\n      console.log('准备返回成功响应:', result);\n      return this.success({\n        message: '轮次创建成功',\n        data: result\n      });\n\n    } catch (error) {\n      console.error('创建轮次失败:', error);\n      return this.fail('创建失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取可选商品列表（排除已参与活跃轮次的商品）\n   * GET /admin/flashsalemulti/goods\n   */\n  async goodsAction() {\n    try {\n      const goodsModel = this.model('goods');\n      const productModel = this.model('product');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 获取所有上架商品\n      const allGoods = await goodsModel.where({\n        is_on_sale: 1,\n        is_delete: 0\n      }).field('id, name, retail_price, list_pic_url, goods_number').select();\n\n      // 获取已参与活跃轮次的商品ID\n      const activeGoodsIds = await roundGoodsModel.alias('rg')\n        .join({\n          table: 'flash_sale_rounds',\n          join: 'inner',\n          as: 'r',\n          on: ['rg.round_id', 'r.id']\n        })\n        .where({\n          'r.status': ['IN', ['upcoming', 'active']]\n        })\n        .field('rg.goods_id')\n        .select();\n\n      const activeIds = activeGoodsIds.map(item => item.goods_id);\n\n      // 为每个商品获取规格信息和价格区间\n      const goodsList = await Promise.all(allGoods.map(async (goods) => {\n        // 获取商品的所有规格产品\n        const products = await productModel\n          .where({\n            goods_id: goods.id,\n            is_delete: 0\n          })\n          .field('retail_price, goods_number')\n          .select();\n\n        let priceRange = null;\n        let totalStock = 0;\n\n        if (products && products.length > 0) {\n          const prices = products.map(p => parseFloat(p.retail_price)).filter(p => p > 0);\n          totalStock = products.reduce((sum, p) => sum + (p.goods_number || 0), 0);\n\n          if (prices.length > 1) {\n            const minPrice = Math.min(...prices);\n            const maxPrice = Math.max(...prices);\n            priceRange = `¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)}`;\n          }\n        }\n\n        // 使用实际库存或商品表中的库存\n        const actualStock = totalStock > 0 ? totalStock : (goods.goods_number || 0);\n        const isInFlashSale = activeIds.includes(goods.id);\n\n        return {\n          ...goods,\n          price_range: priceRange,\n          actual_stock: actualStock,\n          is_in_flash_sale: isInFlashSale,\n          can_select: !isInFlashSale && actualStock > 0,\n          warning: actualStock <= 0 ? '库存不足' : (isInFlashSale ? '已参与其他轮次' : null)\n        };\n      }));\n\n      return this.success(goodsList);\n\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      return this.fail('获取商品列表失败');\n    }\n  }\n\n  /**\n   * 获取/保存系统配置\n   */\n  async configAction() {\n    try {\n      const configModel = this.model('flash_sale_config');\n\n      if (this.isGet) {\n        // 获取配置\n        const config = await configModel.where({ id: 1 }).find();\n        return this.success(config || {});\n      } else {\n        // 保存配置\n        const data = this.post();\n        await configModel.where({ id: 1 }).update(data);\n        return this.success('配置保存成功');\n      }\n\n    } catch (error) {\n      console.error('配置操作失败:', error);\n      return this.fail('配置操作失败');\n    }\n  }\n\n  /**\n   * 手动关闭轮次\n   * POST /admin/flashsalemulti/close\n   */\n  async closeAction() {\n    try {\n      const roundId = this.post('round_id');\n      if (!roundId) {\n        return this.fail('请提供轮次ID');\n      }\n\n      console.log('=== 手动关闭轮次 ===');\n      console.log('轮次ID:', roundId);\n\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 检查轮次是否存在\n      const round = await roundModel.where({ id: roundId }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('轮次不存在');\n      }\n\n      // 检查轮次状态\n      if (round.status === 'ended') {\n        return this.fail('轮次已经结束');\n      }\n\n      // 更新轮次状态为已结束，并标记为手动关闭\n      await roundModel.where({ id: roundId }).update({\n        status: 'ended',\n        closed_manually: 1,\n        closed_at: this.formatLocalDateTime(new Date()),\n        updated_at: this.formatLocalDateTime(new Date())\n      });\n\n      console.log('轮次手动关闭成功:', roundId);\n      return this.success({\n        message: '轮次已关闭',\n        data: { id: roundId, status: 'ended', closed_manually: true }\n      });\n\n    } catch (error) {\n      console.error('关闭轮次失败:', error);\n      return this.fail('关闭失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 手动触发状态更新（用于调试）\n   * POST /admin/flashsalemulti/updateStatus\n   */\n  async updateStatusAction() {\n    try {\n      console.log('🔧 手动触发轮次状态更新...');\n\n      const scheduler = new FlashSaleMultiScheduler();\n      const result = await scheduler.updateRoundStatus(this.model.bind(this));\n\n      return this.success({\n        message: '状态更新完成',\n        result: result\n      });\n\n    } catch (error) {\n      console.error('手动更新状态失败:', error);\n      return this.fail('更新失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 重新启动轮次\n   * POST /admin/flashsalemulti/restart\n   */\n  async restartAction() {\n    try {\n      const { round_id, new_start_time, new_end_time } = this.post();\n      if (!round_id) {\n        return this.fail('请提供轮次ID');\n      }\n\n      console.log('=== 重新启动轮次 ===');\n      console.log('轮次ID:', round_id);\n\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 检查轮次是否存在\n      const round = await roundModel.where({ id: round_id }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('轮次不存在');\n      }\n\n      // 只有已结束的轮次才能重新启动\n      if (round.status !== 'ended') {\n        return this.fail('只有已结束的轮次才能重新启动');\n      }\n\n      const updateData = {\n        status: 'upcoming',\n        closed_manually: 0,\n        closed_at: null,\n        updated_at: this.formatLocalDateTime(new Date())\n      };\n\n      // 如果提供了新的时间，则更新时间\n      if (new_start_time) {\n        updateData.start_time = this.formatLocalDateTime(new Date(new_start_time));\n      }\n      if (new_end_time) {\n        updateData.end_time = this.formatLocalDateTime(new Date(new_end_time));\n      }\n\n      await roundModel.where({ id: round_id }).update(updateData);\n\n      console.log('轮次重新启动成功:', round_id);\n      return this.success({\n        message: '轮次已重新启动',\n        data: { id: round_id, status: 'upcoming' }\n      });\n\n    } catch (error) {\n      console.error('重新启动轮次失败:', error);\n      return this.fail('重新启动失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 延期轮次\n   * POST /admin/flashsalemulti/extend\n   */\n  async extendAction() {\n    try {\n      const { round_id, extend_minutes } = this.post();\n      if (!round_id || !extend_minutes) {\n        return this.fail('请提供轮次ID和延期时间');\n      }\n\n      console.log('=== 延期轮次 ===');\n      console.log('轮次ID:', round_id, '延期分钟:', extend_minutes);\n\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 检查轮次是否存在\n      const round = await roundModel.where({ id: round_id }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('轮次不存在');\n      }\n\n      // 只有进行中的轮次才能延期\n      if (round.status !== 'active') {\n        return this.fail('只有进行中的轮次才能延期');\n      }\n\n      // 计算新的结束时间\n      const currentEndTime = new Date(round.end_time);\n      const newEndTime = new Date(currentEndTime.getTime() + extend_minutes * 60 * 1000);\n\n      await roundModel.where({ id: round_id }).update({\n        end_time: this.formatLocalDateTime(newEndTime),\n        updated_at: this.formatLocalDateTime(new Date())\n      });\n\n      console.log('轮次延期成功:', round_id);\n      return this.success({\n        message: `轮次已延期${extend_minutes}分钟`,\n        data: { id: round_id, new_end_time: newEndTime }\n      });\n\n    } catch (error) {\n      console.error('延期轮次失败:', error);\n      return this.fail('延期失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 复制轮次\n   * POST /admin/flashsalemulti/copy\n   */\n  async copyAction() {\n    try {\n      const { round_id, new_start_time, new_end_time, new_round_name } = this.post();\n      if (!round_id || !new_start_time || !new_end_time) {\n        return this.fail('请提供轮次ID和新的时间信息');\n      }\n\n      console.log('=== 复制轮次 ===');\n      console.log('源轮次ID:', round_id);\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 获取源轮次信息\n      const sourceRound = await roundModel.where({ id: round_id }).find();\n      if (think.isEmpty(sourceRound)) {\n        return this.fail('源轮次不存在');\n      }\n\n      // 获取下一个轮次编号\n      const lastRound = await roundModel.order('round_number DESC').find();\n      const nextRoundNumber = (lastRound && lastRound.round_number) ? lastRound.round_number + 1 : 1;\n\n      // 创建新轮次（只使用数据库中存在的字段）\n      const newRoundData = {\n        round_number: nextRoundNumber,\n        round_name: new_round_name || `${sourceRound.round_name} (复制)`,\n        start_time: this.formatLocalDateTime(new Date(new_start_time)),\n        end_time: this.formatLocalDateTime(new Date(new_end_time)),\n        status: 'upcoming',\n        created_by: think.userId || 0\n      };\n\n      const newRoundId = await roundModel.add(newRoundData);\n      if (!newRoundId) {\n        throw new Error('创建新轮次失败');\n      }\n\n      // 复制轮次商品\n      const sourceGoods = await roundGoodsModel.where({ round_id: round_id }).select();\n      if (sourceGoods.length > 0) {\n        const newGoodsData = sourceGoods.map(goods => ({\n          round_id: newRoundId,\n          goods_id: goods.goods_id,\n          goods_name: goods.goods_name,\n          goods_image: goods.goods_image,\n          original_price: goods.original_price,\n          flash_price: goods.flash_price,\n          discount_rate: goods.discount_rate,\n          stock: goods.stock,\n          sold_count: 0, // 重置销售数量\n          limit_quantity: goods.limit_quantity,\n          sort_order: goods.sort_order\n        }));\n\n        await roundGoodsModel.addMany(newGoodsData);\n      }\n\n      console.log('轮次复制成功:', newRoundId);\n      return this.success({\n        message: '轮次复制成功',\n        data: { id: newRoundId, round_number: nextRoundNumber }\n      });\n\n    } catch (error) {\n      console.error('复制轮次失败:', error);\n      return this.fail('复制失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 删除轮次\n   * POST /admin/flashsalemulti/delete\n   */\n  async deleteAction() {\n    try {\n      const roundId = this.post('round_id');\n      if (!roundId) {\n        return this.fail('请提供轮次ID');\n      }\n\n      console.log('=== 删除轮次 ===');\n      console.log('轮次ID:', roundId);\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 检查轮次是否存在\n      const round = await roundModel.where({ id: roundId }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('轮次不存在');\n      }\n\n      // 如果轮次还在进行中或即将开始，先关闭它\n      if (round.status === 'active' || round.status === 'upcoming') {\n        console.log('轮次状态为:', round.status, '先关闭轮次');\n        await roundModel.where({ id: roundId }).update({\n          status: 'ended',\n          closed_manually: 1,\n          closed_at: this.formatLocalDateTime(new Date()),\n          updated_at: this.formatLocalDateTime(new Date())\n        });\n      }\n\n      // 删除轮次相关的商品\n      await roundGoodsModel.where({ round_id: roundId }).delete();\n      console.log('已删除轮次商品数据');\n\n      // 删除轮次\n      await roundModel.where({ id: roundId }).delete();\n      console.log('已删除轮次数据');\n\n      return this.success({\n        message: '轮次删除成功',\n        data: { id: roundId }\n      });\n\n    } catch (error) {\n      console.error('删除轮次失败:', error);\n      return this.fail('删除失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 批量删除轮次\n   * POST /admin/flashsalemulti/batchDelete\n   */\n  async batchDeleteAction() {\n    try {\n      const roundIds = this.post('round_ids');\n      if (!roundIds || !Array.isArray(roundIds) || roundIds.length === 0) {\n        return this.fail('请提供要删除的轮次ID列表');\n      }\n\n      console.log('=== 批量删除轮次 ===');\n      console.log('轮次IDs:', roundIds);\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      let successCount = 0;\n      let failCount = 0;\n      const results = [];\n\n      for (const roundId of roundIds) {\n        try {\n          // 检查轮次是否存在\n          const round = await roundModel.where({ id: roundId }).find();\n          if (think.isEmpty(round)) {\n            failCount++;\n            results.push({ id: roundId, success: false, message: '轮次不存在' });\n            continue;\n          }\n\n          // 如果轮次还在进行中或即将开始，先关闭它\n          if (round.status === 'active' || round.status === 'upcoming') {\n            await roundModel.where({ id: roundId }).update({\n              status: 'ended',\n              closed_manually: 1,\n              closed_at: this.formatLocalDateTime(new Date()),\n              updated_at: this.formatLocalDateTime(new Date())\n            });\n          }\n\n          // 删除轮次相关的商品\n          await roundGoodsModel.where({ round_id: roundId }).delete();\n\n          // 删除轮次\n          await roundModel.where({ id: roundId }).delete();\n\n          successCount++;\n          results.push({ id: roundId, success: true, message: '删除成功' });\n\n        } catch (error) {\n          failCount++;\n          results.push({ id: roundId, success: false, message: error.message });\n        }\n      }\n\n      return this.success({\n        message: `批量删除完成：成功 ${successCount} 个，失败 ${failCount} 个`,\n        data: {\n          successCount,\n          failCount,\n          results\n        }\n      });\n\n    } catch (error) {\n      console.error('批量删除轮次失败:', error);\n      return this.fail('批量删除失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取时间调试信息\n   * GET /admin/flashsalemulti/timeDebug\n   */\n  async timeDebugAction() {\n    try {\n      const scheduler = new FlashSaleMultiScheduler();\n      const currentTime = scheduler.getCurrentLocalTimeString();\n      const now = new Date();\n\n      // 获取所有upcoming轮次\n      const upcomingRounds = await this.model('flash_sale_rounds').where({\n        status: 'upcoming'\n      }).select();\n\n      const debugInfo = {\n        current_local_time: currentTime,\n        current_js_time: now.toLocaleString('zh-CN'),\n        current_utc_time: now.toISOString(),\n        upcoming_rounds: upcomingRounds.map(r => ({\n          id: r.id,\n          round_number: r.round_number,\n          round_name: r.round_name,\n          start_time: r.start_time,\n          end_time: r.end_time,\n          status: r.status,\n          should_be_active: r.start_time <= currentTime,\n          should_be_ended: r.end_time < currentTime\n        }))\n      };\n\n      return this.success(debugInfo);\n\n    } catch (error) {\n      console.error('获取调试信息失败:', error);\n      return this.fail('获取调试信息失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 手动更新轮次状态\n   */\n  async updateStatusAction() {\n    try {\n      console.log('🔄 手动触发轮次状态更新...');\n\n      const FlashSaleMultiScheduler = require('../../common/service/flash_sale_multi_scheduler');\n      const scheduler = new FlashSaleMultiScheduler();\n\n      // 更新轮次状态\n      const result = await scheduler.updateRoundStatus(this.model.bind(this));\n\n      // 获取统计信息\n      const stats = await scheduler.getActiveRoundsStats(this.model.bind(this));\n\n      return this.success({\n        message: '轮次状态更新完成',\n        data: {\n          updated: result,\n          stats: stats\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ 更新轮次状态失败:', error);\n      return this.fail('更新状态失败: ' + error.message);\n    }\n  }\n\n};\n"]}