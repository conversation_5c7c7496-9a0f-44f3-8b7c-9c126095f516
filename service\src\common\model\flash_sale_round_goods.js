module.exports = class extends think.Model {
  
  /**
   * 批量添加轮次商品
   */
  async addRoundGoods(roundId, goodsList) {
    const goodsData = goodsList.map((goods, index) => ({
      round_id: roundId,
      goods_id: goods.goods_id,
      goods_name: goods.goods_name,
      goods_image: goods.goods_image || '',
      original_price: goods.original_price,
      flash_price: goods.flash_price,
      discount_rate: goods.discount_rate || this.calculateDiscountRate(goods.original_price, goods.flash_price),
      stock: goods.stock,
      sold_count: 0,
      limit_quantity: goods.limit_quantity || 1,
      sort_order: index
    }));
    
    return await this.addMany(goodsData);
  }
  
  /**
   * 计算折扣率
   */
  calculateDiscountRate(originalPrice, flashPrice) {
    if (!originalPrice || originalPrice <= 0) return 0;
    return Math.round((1 - flashPrice / originalPrice) * 100 * 100) / 100; // 保留2位小数
  }
  
  /**
   * 获取轮次商品列表
   */
  async getRoundGoodsList(roundId) {
    return await this.where({ round_id: roundId })
      .order('sort_order ASC')
      .select();
  }
  
  /**
   * 检查商品是否已参与其他活跃轮次
   */
  async checkGoodsInActiveRounds(goodsIds) {
    const roundModel = this.model('flash_sale_rounds');

    // 查询活跃轮次中的商品
    const activeGoods = await this.alias('rg')
      .join({
        table: 'flash_sale_rounds',
        join: 'inner',
        as: 'r',
        on: ['rg.round_id', 'r.id']
      })
      .where({
        'rg.goods_id': ['IN', goodsIds],
        'r.status': ['IN', ['upcoming', 'active']]
      })
      .field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status')
      .select();

    return activeGoods;
  }

  /**
   * 检查商品在指定时间段是否有重叠的轮次
   */
  async checkGoodsInOverlappingRounds(goodsIds, startTime, endTime) {
    try {
      // 使用原生SQL查询，避免复杂的ORM语法问题
      const sql = `
        SELECT rg.goods_id, r.round_number, r.start_time, r.end_time, r.status
        FROM ${this.tablePrefix}flash_sale_round_goods rg
        INNER JOIN ${this.tablePrefix}flash_sale_rounds r ON rg.round_id = r.id
        WHERE rg.goods_id IN (${goodsIds.map(() => '?').join(',')})
          AND r.status IN ('upcoming', 'active')
          AND (
            (r.start_time <= ? AND r.end_time > ?) OR
            (r.start_time < ? AND r.end_time >= ?) OR
            (r.start_time >= ? AND r.end_time <= ?) OR
            (r.start_time <= ? AND r.end_time >= ?)
          )
      `;

      const params = [
        ...goodsIds,
        startTime, startTime,  // 新轮次开始时间在现有轮次范围内
        endTime, endTime,      // 新轮次结束时间在现有轮次范围内
        startTime, endTime,    // 新轮次完全包含现有轮次
        startTime, endTime     // 现有轮次完全包含新轮次
      ];

      const overlappingGoods = await this.query(sql, params);
      return overlappingGoods || [];

    } catch (error) {
      console.error('检查商品时间重叠失败:', error);
      // 如果查询失败，返回空数组，允许创建轮次
      return [];
    }
  }
  
  /**
   * 更新商品销售数量
   */
  async updateSoldCount(roundGoodsId, quantity) {
    return await this.where({ id: roundGoodsId })
      .increment('sold_count', quantity);
  }
  
  /**
   * 获取商品在轮次中的剩余库存
   */
  async getAvailableStock(roundGoodsId) {
    const roundGoods = await this.where({ id: roundGoodsId }).find();
    if (think.isEmpty(roundGoods)) {
      return 0;
    }
    return Math.max(0, roundGoods.stock - roundGoods.sold_count);
  }
  
  /**
   * 获取用户在某轮次某商品的购买数量
   */
  async getUserPurchaseCount(roundId, goodsId, userId) {
    const orderModel = this.model('flash_sale_orders');
    const result = await orderModel.where({
      round_id: roundId,
      goods_id: goodsId,
      user_id: userId
    }).sum('quantity');
    
    return result || 0;
  }
};
