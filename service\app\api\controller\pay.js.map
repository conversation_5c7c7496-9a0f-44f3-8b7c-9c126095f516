{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\pay.js"], "names": ["Base", "require", "moment", "generate", "Jushuitan", "WangDianSync", "module", "exports", "preWeixinPayaAction", "orderId", "get", "orderInfo", "model", "where", "id", "find", "userId", "user_id", "result", "transaction_id", "time_end", "parseInt", "Date", "getTime", "orderModel", "updatePayData", "afterPay", "success", "preWeixinPayAction", "orderGoods", "order_id", "is_delete", "select", "checkPrice", "checkStock", "item", "product", "product_id", "number", "goods_number", "seckillOrder", "goods_id", "think", "isEmpty", "seckillGoods", "round_id", "expectedPrice", "retail_price", "original_price", "discountRate", "flash_price", "Math", "round", "max", "abs", "fail", "pay_status", "openid", "getField", "WeixinSerivce", "service", "returnParams", "createUnifiedOrder", "body", "order_sn", "out_trade_no", "total_fee", "actual_price", "spbill_create_ip", "err", "notifyAction", "console", "log", "ip", "toISOString", "data", "post", "payNotify", "echo", "json", "getOrderByOrderSn", "bool", "checkPayStatus", "order_type", "orderGoodsList", "cartItem", "specification", "goods_specifition_name_value", "decrement", "increment", "updatedOrderInfo", "order_status", "wangdianSync", "pushOrder", "error", "message", "refundNotifyAction", "info", "return_code", "ctx", "type", "return_msg"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,WAAWF,QAAQ,iBAAR,CAAjB;AACA,MAAMG,YAAYH,QAAQ,WAAR,CAAlB;AACA,MAAMI,eAAeJ,QAAQ,+BAAR,CAArB;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;AAChC;;;;AAIA;AACMQ,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMC,UAAU,MAAKC,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAMC,YAAY,MAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CC,oBAAIL;AAD0C,aAA1B,EAErBM,IAFqB,EAAxB;AAGA,gBAAIC,SAASL,UAAUM,OAAvB;AACA,gBAAIC,SAAS;AACZC,gCAAgB,YADJ;AAEZC,0BAAUC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAFE,aAAb;AAIA,kBAAMC,aAAa,MAAKZ,KAAL,CAAW,OAAX,CAAnB;AACA,kBAAMY,WAAWC,aAAX,CAAyBd,UAAUG,EAAnC,EAAuCI,MAAvC,CAAN;AACA,kBAAM,MAAKQ,QAAL,CAAcf,SAAd,CAAN;AACN,mBAAO,MAAKgB,OAAL,EAAP;AAb8B;AAc3B;AACD;AACMC,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMnB,UAAU,OAAKC,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAMC,YAAY,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CC,oBAAIL;AAD0C,aAA1B,EAErBM,IAFqB,EAAxB;AAGA;AACA,gBAAIc,aAAa,MAAM,OAAKjB,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACnDiB,0BAASrB,OAD0C;AAEnDsB,2BAAU;AAFyC,aAAhC,EAGpBC,MAHoB,EAAvB;AAIA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,aAAa,CAAjB;AACA,iBAAI,MAAMC,IAAV,IAAkBN,UAAlB,EAA6B;AACzB,oBAAIO,UAAU,MAAM,OAAKxB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,wBAAGqB,KAAKE;AADoC,iBAA5B,EAEjBtB,IAFiB,EAApB;AAGA,oBAAGoB,KAAKG,MAAL,GAAcF,QAAQG,YAAzB,EAAsC;AAClCL;AACH;;AAED;AACA,sBAAMM,eAAe,MAAM,OAAK5B,KAAL,CAAW,mBAAX,EAAgCC,KAAhC,CAAsC;AAC7DiB,8BAAUrB,OADmD;AAE7DgC,8BAAUN,KAAKM;AAF8C,iBAAtC,EAGxB1B,IAHwB,EAA3B;;AAKA,oBAAG,CAAC2B,MAAMC,OAAN,CAAcH,YAAd,CAAJ,EAAgC;AAC5B;AACA,0BAAMI,eAAe,MAAM,OAAKhC,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAClEgC,kCAAUL,aAAaK,QAD2C;AAElEJ,kCAAUN,KAAKM;AAFmD,qBAA3C,EAGxB1B,IAHwB,EAA3B;;AAKA,wBAAG,CAAC2B,MAAMC,OAAN,CAAcC,YAAd,CAAJ,EAAgC;AAC5B;AACA,4BAAIE,gBAAgBV,QAAQW,YAA5B;AACA,4BAAIH,aAAaI,cAAb,GAA8B,CAAlC,EAAqC;AACjC,kCAAMC,eAAeL,aAAaM,WAAb,GAA2BN,aAAaI,cAA7D;AACAF,4CAAgBK,KAAKC,KAAL,CAAWhB,QAAQW,YAAR,GAAuBE,YAAvB,GAAsC,GAAjD,IAAwD,GAAxE;AACAH,4CAAgBK,KAAKE,GAAL,CAAS,IAAT,EAAeP,aAAf,CAAhB;AACH,yBAJD,MAIO;AACHA,4CAAgBF,aAAaM,WAA7B;AACH;;AAED,4BAAGC,KAAKG,GAAL,CAASnB,KAAKY,YAAL,GAAoBD,aAA7B,IAA8C,IAAjD,EAAsD;AAClDb;AACH;AACJ;AACJ,iBAtBD,MAsBO;AACH;AACA,wBAAGE,KAAKY,YAAL,IAAqBX,QAAQW,YAAhC,EAA6C;AACzCd;AACH;AACJ;AACJ;AACD,gBAAGC,aAAa,CAAhB,EAAkB;AACd,uBAAO,OAAKqB,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;AACD,gBAAGtB,aAAa,CAAhB,EAAkB;AACd,uBAAO,OAAKsB,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACH;AACD,gBAAIb,MAAMC,OAAN,CAAchC,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAK4C,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD,gBAAIlC,SAASV,UAAU6C,UAAnB,MAAmC,CAAvC,EAA0C;AACtC,uBAAO,OAAKD,IAAL,CAAU,GAAV,EAAe,eAAf,CAAP;AACH;AACD,kBAAME,SAAS,MAAM,OAAK7C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,oBAAIH,UAAUM;AAD4B,aAAzB,EAElByC,QAFkB,CAET,eAFS,EAEQ,IAFR,CAArB;AAGA,gBAAIhB,MAAMC,OAAN,CAAcc,MAAd,CAAJ,EAA2B;AACvB,uBAAO,OAAKF,IAAL,CAAU,GAAV,EAAe,iBAAf,CAAP;AACH;AACD,kBAAMI,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;AACA,gBAAI;AACA,sBAAMC,eAAe,MAAMF,cAAcG,kBAAd,CAAiC;AACxDL,4BAAQA,MADgD;AAExDM,0BAAM,YAAYpD,UAAUqD,QAF4B;AAGxDC,kCAActD,UAAUqD,QAHgC;AAIxDE,+BAAW7C,SAASV,UAAUwD,YAAV,GAAyB,GAAlC,CAJ6C;AAKxDC,sCAAkB;AALsC,iBAAjC,CAA3B;AAOA,uBAAO,OAAKzC,OAAL,CAAakC,YAAb,CAAP;AACH,aATD,CASE,OAAOQ,GAAP,EAAY;AACV,uBAAO,OAAKd,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;AArFsB;AAsF1B;AACKe,gBAAN,GAAqB;AAAA;;AAAA;AACjBC,oBAAQC,GAAR,CAAY,oBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,OAAKC,EAA1B;AACAF,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,IAAIlD,IAAJ,GAAWoD,WAAX,EAArB;;AAEA,kBAAMf,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;AACA,kBAAMe,OAAO,OAAKC,IAAL,CAAU,KAAV,CAAb;AACAL,oBAAQC,GAAR,CAAY,WAAZ,EAAyBG,IAAzB;;AAEA,kBAAMzD,SAAS,MAAMyC,cAAckB,SAAd,CAAwB,OAAKD,IAAL,CAAU,KAAV,CAAxB,CAArB;AACAL,oBAAQC,GAAR,CAAY,SAAZ,EAAuBtD,MAAvB;;AAEA,gBAAI,CAACA,MAAL,EAAa;AACTqD,wBAAQC,GAAR,CAAY,eAAZ;AACA,oBAAIM,OAAO,MAAX;AACA,uBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AACH;AACD,kBAAMtD,aAAa,OAAKZ,KAAL,CAAW,OAAX,CAAnB;AACA,kBAAMD,YAAY,MAAMa,WAAWwD,iBAAX,CAA6B9D,OAAO+C,YAApC,CAAxB;AACAM,oBAAQC,GAAR,CAAY,WAAZ,EAAyB7D,SAAzB;;AAEA,gBAAI+B,MAAMC,OAAN,CAAchC,SAAd,CAAJ,EAA8B;AAC1B4D,wBAAQC,GAAR,CAAY,YAAZ,EAA0BtD,OAAO+C,YAAjC;AACA,oBAAIa,OAAO,MAAX;AACA,uBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AACH;;AAED,gBAAIG,OAAO,MAAMzD,WAAW0D,cAAX,CAA0BvE,UAAUG,EAApC,CAAjB;AACAyD,oBAAQC,GAAR,CAAY,aAAZ,EAA2BS,IAA3B,EAAiC,OAAjC,EAA0CtE,UAAUG,EAApD;;AAEA,gBAAImE,QAAQ,IAAZ,EAAkB;AACd,oBAAItE,UAAUwE,UAAV,IAAwB,CAA5B,EAA+B;AAAE;AAC7BZ,4BAAQC,GAAR,CAAY,eAAZ;AACA,0BAAMhD,WAAWC,aAAX,CAAyBd,UAAUG,EAAnC,EAAuCI,MAAvC,CAAN;AACAqD,4BAAQC,GAAR,CAAY,YAAZ;;AAEAD,4BAAQC,GAAR,CAAY,mBAAZ;AACA,0BAAM,OAAK9C,QAAL,CAAcf,SAAd,CAAN;AACA4D,4BAAQC,GAAR,CAAY,cAAZ;AACH;AACJ,aAVD,MAUO;AACHD,wBAAQC,GAAR,CAAY,aAAZ;AACA,uBAAO,kGAAP;AACH;;AAEDD,oBAAQC,GAAR,CAAY,sBAAZ;AACA,gBAAIM,OAAO,SAAX;AACA,mBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AA/CiB;AAgDpB;AACKpD,YAAN,CAAef,SAAf,EAA0B;AAAA;;AAAA;AACtB,gBAAIA,UAAUwE,UAAV,IAAwB,CAA5B,EAA+B;AAC3B,oBAAIC,iBAAiB,MAAM,OAAKxE,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACvDiB,8BAAUnB,UAAUG;AADmC,iBAAhC,EAExBkB,MAFwB,EAA3B;;AAIA;AACA,qBAAK,MAAMqD,QAAX,IAAuBD,cAAvB,EAAuC;AACnC,wBAAI3C,WAAW4C,SAAS5C,QAAxB;AACA,wBAAIJ,aAAagD,SAAShD,UAA1B;AACA,wBAAIC,SAAS+C,SAAS/C,MAAtB;AACA,wBAAIgD,gBAAgBD,SAASE,4BAA7B;AACA,0BAAM,OAAK3E,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BC,4BAAI2B;AADwB,qBAA1B,EAEH+C,SAFG,CAEO,cAFP,EAEuBlD,MAFvB,CAAN;AAGA,0BAAM,OAAK1B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BC,4BAAI2B;AADwB,qBAA1B,EAEHgD,SAFG,CAEO,aAFP,EAEsBnD,MAFtB,CAAN;AAGA,0BAAM,OAAK1B,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9BC,4BAAIuB;AAD0B,qBAA5B,EAEHmD,SAFG,CAEO,cAFP,EAEuBlD,MAFvB,CAAN;AAGH;;AAED;AACA,sBAAMoD,mBAAmB,MAAM,OAAK9E,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACrDC,wBAAIH,UAAUG;AADuC,iBAA1B,EAE5BC,IAF4B,EAA/B;;AAIA;AACA,oBAAI2E,iBAAiBC,YAAjB,KAAkC,GAAtC,EAA2C;AACvC,wBAAI;AACApB,gCAAQC,GAAR,CAAa,wCAAuCkB,iBAAiB1B,QAAS,EAA9E;AACA,8BAAM4B,eAAe,IAAIvF,YAAJ,QAArB;AACA,8BAAMa,SAAS,MAAM0E,aAAaC,SAAb,CAAuBH,gBAAvB,EAAyCN,cAAzC,CAArB;;AAEA,4BAAIlE,OAAOS,OAAX,EAAoB;AAChB4C,oCAAQC,GAAR,CAAa,cAAakB,iBAAiB1B,QAAS,OAApD;AACH,yBAFD,MAEO;AACHO,oCAAQuB,KAAR,CAAe,cAAaJ,iBAAiB1B,QAAS,UAAS9C,OAAO6E,OAAQ,EAA9E;AACH;AACJ,qBAVD,CAUE,OAAOD,KAAP,EAAc;AACZvB,gCAAQuB,KAAR,CAAc,oBAAd,EAAoCA,KAApC;AACA;AACH;AACJ,iBAfD,MAeO;AACHvB,4BAAQC,GAAR,CAAa,gCAA+BkB,iBAAiBC,YAAa,EAA1E;AACH;;AAED;AACH;AAjDqB;AAkDzB;;AAED;;;AAGMK,sBAAN,GAA2B;AAAA;;AAAA;AACvBzB,oBAAQC,GAAR,CAAY,oBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,OAAKC,EAA1B;AACAF,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,IAAIlD,IAAJ,GAAWoD,WAAX,EAArB;;AAEA,gBAAIuB,OAAO,OAAKrB,IAAL,EAAX;AACAL,oBAAQC,GAAR,CAAY,aAAZ,EAA2ByB,IAA3B;;AAEA,gBAAI;AACA;AACA;;AAEA,oBAAIA,KAAKC,WAAL,KAAqB,SAAzB,EAAoC;AAChC3B,4BAAQC,GAAR,CAAY,cAAZ;;AAEA;AACA;;AAEA;AACA,2BAAK2B,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,2BAAO,kGAAP;AACH,iBATD,MASO;AACH7B,4BAAQuB,KAAR,CAAc,aAAd,EAA6BG,KAAKI,UAAlC;AACA,2BAAKF,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,2BAAO,iGAAP;AACH;AAEJ,aAnBD,CAmBE,OAAON,KAAP,EAAc;AACZvB,wBAAQuB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,uBAAKK,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,uBAAO,iGAAP;AACH;AA/BsB;AAgC1B;AArP+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\pay.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst generate = require('nanoid/generate');\nconst Jushuitan = require('jushuitan');\nconst WangDianSync = require('../../common/wangdian_sync.js');\nmodule.exports = class extends Base {\n    /**\n     * 获取支付的请求参数\n     * @returns {Promise<PreventPromise|void|Promise>}\n     */\n    // 测试时付款，将真实接口注释。 在小程序的services/pay.js中按照提示注释和打开\n    async preWeixinPayaAction() {\n        const orderId = this.get('orderId');\n        const orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        let userId = orderInfo.user_id;\n        let result = {\n        \ttransaction_id: 123123123123,\n        \ttime_end: parseInt(new Date().getTime() / 1000),\n        }\n        const orderModel = this.model('order');\n        await orderModel.updatePayData(orderInfo.id, result);\n        await this.afterPay(orderInfo);\n\t\treturn this.success();\n    }\n    // 真实的付款接口\n    async preWeixinPayAction() {\n        const orderId = this.get('orderId');\n        const orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        // 再次确认库存和价格\n        let orderGoods = await this.model('order_goods').where({\n            order_id:orderId,\n            is_delete:0\n        }).select();\n        let checkPrice = 0;\n        let checkStock = 0;\n        for(const item of orderGoods){\n            let product = await this.model('product').where({\n                id:item.product_id\n            }).find();\n            if(item.number > product.goods_number){\n                checkStock++;\n            }\n\n            // 检查是否为秒杀商品（通过秒杀订单记录表查询）\n            const seckillOrder = await this.model('flash_sale_orders').where({\n                order_id: orderId,\n                goods_id: item.goods_id\n            }).find();\n\n            if(!think.isEmpty(seckillOrder)){\n                // 这是秒杀商品，查询秒杀商品信息\n                const seckillGoods = await this.model('flash_sale_round_goods').where({\n                    round_id: seckillOrder.round_id,\n                    goods_id: item.goods_id\n                }).find();\n\n                if(!think.isEmpty(seckillGoods)){\n                    // 计算当前规格的秒杀价格\n                    let expectedPrice = product.retail_price;\n                    if (seckillGoods.original_price > 0) {\n                        const discountRate = seckillGoods.flash_price / seckillGoods.original_price;\n                        expectedPrice = Math.round(product.retail_price * discountRate * 100) / 100;\n                        expectedPrice = Math.max(0.01, expectedPrice);\n                    } else {\n                        expectedPrice = seckillGoods.flash_price;\n                    }\n\n                    if(Math.abs(item.retail_price - expectedPrice) > 0.01){\n                        checkPrice++;\n                    }\n                }\n            } else {\n                // 普通商品验证原价\n                if(item.retail_price != product.retail_price){\n                    checkPrice++;\n                }\n            }\n        }\n        if(checkStock > 0){\n            return this.fail(400, '库存不足，请重新下单');\n        }\n        if(checkPrice > 0){\n            return this.fail(400, '价格发生变化，请重新下单');\n        }\n        if (think.isEmpty(orderInfo)) {\n            return this.fail(400, '订单已取消');\n        }\n        if (parseInt(orderInfo.pay_status) !== 0) {\n            return this.fail(400, '订单已支付，请不要重复操作');\n        }\n        const openid = await this.model('user').where({\n            id: orderInfo.user_id\n        }).getField('weixin_openid', true);\n        if (think.isEmpty(openid)) {\n            return this.fail(400, '微信支付失败，没有openid');\n        }\n        const WeixinSerivce = this.service('weixin', 'api');\n        try {\n            const returnParams = await WeixinSerivce.createUnifiedOrder({\n                openid: openid,\n                body: '[海风小店]：' + orderInfo.order_sn,\n                out_trade_no: orderInfo.order_sn,\n                total_fee: parseInt(orderInfo.actual_price * 100),\n                spbill_create_ip: ''\n            });\n            return this.success(returnParams);\n        } catch (err) {\n            return this.fail(400, '微信支付失败?');\n        }\n    }\n    async notifyAction() {\n        console.log('=== 微信支付异步通知开始 ===');\n        console.log('请求IP:', this.ip);\n        console.log('请求时间:', new Date().toISOString());\n\n        const WeixinSerivce = this.service('weixin', 'api');\n        const data = this.post('xml');\n        console.log('接收到的通知数据:', data);\n\n        const result = await WeixinSerivce.payNotify(this.post('xml'));\n        console.log('签名验证结果:', result);\n\n        if (!result) {\n            console.log('签名验证失败，返回FAIL');\n            let echo = 'FAIL';\n            return this.json(echo);\n        }\n        const orderModel = this.model('order');\n        const orderInfo = await orderModel.getOrderByOrderSn(result.out_trade_no);\n        console.log('查询到的订单信息:', orderInfo);\n\n        if (think.isEmpty(orderInfo)) {\n            console.log('订单不存在，订单号:', result.out_trade_no);\n            let echo = 'FAIL';\n            return this.json(echo);\n        }\n\n        let bool = await orderModel.checkPayStatus(orderInfo.id);\n        console.log('订单支付状态检查结果:', bool, '订单ID:', orderInfo.id);\n\n        if (bool == true) {\n            if (orderInfo.order_type == 0) { //普通订单和秒杀订单\n                console.log('开始更新订单支付状态...');\n                await orderModel.updatePayData(orderInfo.id, result);\n                console.log('订单支付状态更新完成');\n\n                console.log('开始执行afterPay处理...');\n                await this.afterPay(orderInfo);\n                console.log('afterPay处理完成');\n            }\n        } else {\n            console.log('订单已支付，不重复处理');\n            return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[订单已支付]]></return_msg></xml>';\n        }\n\n        console.log('=== 微信支付异步通知处理完成 ===');\n        let echo = 'SUCCESS'\n        return this.json(echo);\n    }\n    async afterPay(orderInfo) {\n        if (orderInfo.order_type == 0) {\n            let orderGoodsList = await this.model('order_goods').where({\n                order_id: orderInfo.id\n            }).select();\n\n            // 更新库存和销量\n            for (const cartItem of orderGoodsList) {\n                let goods_id = cartItem.goods_id;\n                let product_id = cartItem.product_id;\n                let number = cartItem.number;\n                let specification = cartItem.goods_specifition_name_value;\n                await this.model('goods').where({\n                    id: goods_id\n                }).decrement('goods_number', number);\n                await this.model('goods').where({\n                    id: goods_id\n                }).increment('sell_volume', number);\n                await this.model('product').where({\n                    id: product_id\n                }).decrement('goods_number', number);\n            }\n\n            // 获取更新后的订单信息（确保order_status是201）\n            const updatedOrderInfo = await this.model('order').where({\n                id: orderInfo.id\n            }).find();\n\n            // 检查订单状态是否为201（已付款），如果是则推送到旺店通ERP\n            if (updatedOrderInfo.order_status === 201) {\n                try {\n                    console.log(`[旺店通同步] 订单状态为201（已付款），开始推送订单到旺店通ERP: ${updatedOrderInfo.order_sn}`);\n                    const wangdianSync = new WangDianSync(this);\n                    const result = await wangdianSync.pushOrder(updatedOrderInfo, orderGoodsList);\n\n                    if (result.success) {\n                        console.log(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送成功`);\n                    } else {\n                        console.error(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送失败: ${result.message}`);\n                    }\n                } catch (error) {\n                    console.error('[旺店通同步] 推送订单时发生异常:', error);\n                    // 不抛出异常，避免影响主要的支付流程\n                }\n            } else {\n                console.log(`[旺店通同步] 订单状态不是201，跳过推送。当前状态: ${updatedOrderInfo.order_status}`);\n            }\n\n            // version 1.01\n        }\n    }\n\n    /**\n     * 微信退款通知处理\n     */\n    async refundNotifyAction() {\n        console.log('=== 微信退款异步通知开始 ===');\n        console.log('请求IP:', this.ip);\n        console.log('请求时间:', new Date().toISOString());\n\n        let info = this.post();\n        console.log('接收到的退款通知数据:', info);\n\n        try {\n            // 微信退款通知的数据格式与支付通知不同\n            // 这里简化处理，主要是确认收到通知\n\n            if (info.return_code === 'SUCCESS') {\n                console.log('✅ 微信退款通知处理成功');\n\n                // 可以在这里添加额外的退款后处理逻辑\n                // 比如发送退款成功通知给用户、更新订单状态等\n\n                // 返回成功响应给微信\n                this.ctx.type = 'text/xml';\n                return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';\n            } else {\n                console.error('❌ 微信退款通知失败:', info.return_msg);\n                this.ctx.type = 'text/xml';\n                return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>';\n            }\n\n        } catch (error) {\n            console.error('❌ 处理微信退款通知异常:', error);\n            this.ctx.type = 'text/xml';\n            return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>';\n        }\n    }\n};"]}