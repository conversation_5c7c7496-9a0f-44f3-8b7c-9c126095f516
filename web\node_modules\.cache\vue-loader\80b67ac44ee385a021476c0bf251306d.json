{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=template&id=c8ad4aac&scoped=true&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1754251484081}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImZsYXNoLXNhbGUtbXVsdGktcGFnZSI+CiAgPCEtLSDpobXpnaLmoIfpopggLS0+CiAgPGRpdiBjbGFzcz0icGFnZS1oZWFkZXIiPgogICAgPGgyPumZkOaXtuenkuadgOeuoeeQhu+8iOWkmuWVhuWTgei9ruasoe+8iTwvaDI+CiAgICA8YnV0dG9uIEBjbGljaz0ib3BlbkNyZWF0ZU1vZGFsIiBjbGFzcz0iYnRuIGJ0bi1wcmltYXJ5Ij4KICAgICAg5Yib5bu65paw6L2u5qyhCiAgICA8L2J1dHRvbj4KICA8L2Rpdj4KCiAgPCEtLSDnu5/orqHljaHniYcgLS0+CiAgPGRpdiBjbGFzcz0ic3RhdHMtY2FyZHMiPgogICAgPGRpdiBjbGFzcz0ic3RhdC1jYXJkIj4KICAgICAgPGgzPuaAu+i9ruasoTwvaDM+CiAgICAgIDxwIGNsYXNzPSJzdGF0LW51bWJlciI+e3sgc3RhdGlzdGljcy50b3RhbFJvdW5kcyB8fCAwIH19PC9wPgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJzdGF0LWNhcmQiPgogICAgICA8aDM+6L+b6KGM5LitPC9oMz4KICAgICAgPHAgY2xhc3M9InN0YXQtbnVtYmVyIGFjdGl2ZSI+e3sgc3RhdGlzdGljcy5hY3RpdmVSb3VuZHMgfHwgMCB9fTwvcD4KICAgIDwvZGl2PgogICAgPGRpdiBjbGFzcz0ic3RhdC1jYXJkIj4KICAgICAgPGgzPuWNs+WwhuW8gOWnizwvaDM+CiAgICAgIDxwIGNsYXNzPSJzdGF0LW51bWJlciB1cGNvbWluZyI+e3sgc3RhdGlzdGljcy51cGNvbWluZ1JvdW5kcyB8fCAwIH19PC9wPgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJzdGF0LWNhcmQiPgogICAgICA8aDM+5oC76ZSA5ZSu6aKdPC9oMz4KICAgICAgPHAgY2xhc3M9InN0YXQtbnVtYmVyIj7CpXt7IChzdGF0aXN0aWNzLnRvdGFsU2FsZXMgfHwgMCkudG9GaXhlZCgyKSB9fTwvcD4KICAgIDwvZGl2PgogIDwvZGl2PgoKICA8IS0tIOW9k+WJjei9ruasoSAtLT4KICA8ZGl2IGNsYXNzPSJjdXJyZW50LXJvdW5kcyIgdi1pZj0iY3VycmVudFJvdW5kcy5jdXJyZW50ICYmIGN1cnJlbnRSb3VuZHMuY3VycmVudC5sZW5ndGggPiAwIj4KICAgIDxoMz7lvZPliY3ov5vooYzkuK3nmoTova7mrKE8L2gzPgogICAgPGRpdiBjbGFzcz0icm91bmQtbGlzdCI+CiAgICAgIDxkaXYgdi1mb3I9InJvdW5kIGluIGN1cnJlbnRSb3VuZHMuY3VycmVudCIgOmtleT0icm91bmQuaWQiIGNsYXNzPSJyb3VuZC1pdGVtIGFjdGl2ZSI+CiAgICAgICAgPGRpdiBjbGFzcz0icm91bmQtaW5mbyI+CiAgICAgICAgICA8aDQ+e3sgcm91bmQucm91bmRfbmFtZSB9fSAo6L2u5qyhICN7eyByb3VuZC5yb3VuZF9udW1iZXIgfX0pPC9oND4KICAgICAgICAgIDxwPuWVhuWTgeaVsOmHjzoge3sgcm91bmQuZ29vZHNfY291bnQgfX08L3A+CiAgICAgICAgICA8cD7liankvZnml7bpl7Q6IHt7IGZvcm1hdFRpbWUocm91bmQuY291bnRkb3duKSB9fTwvcD4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJnb29kcy1wcmV2aWV3Ij4KICAgICAgICAgIDxkaXYgdi1mb3I9Imdvb2RzIGluIHJvdW5kLmdvb2RzX2xpc3Quc2xpY2UoMCwgMykiIDprZXk9Imdvb2RzLmlkIiBjbGFzcz0iZ29vZHMtaXRlbSI+CiAgICAgICAgICAgIDxpbWcgOnNyYz0iZ29vZHMuZ29vZHNfaW1hZ2UiIDphbHQ9Imdvb2RzLmdvb2RzX25hbWUiIC8+CiAgICAgICAgICAgIDxzcGFuPnt7IGdvb2RzLmdvb2RzX25hbWUgfX08L3NwYW4+CiAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJwcmljZSI+wqV7eyBnb29kcy5mbGFzaF9wcmljZSB9fTwvc3Bhbj4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPHNwYW4gdi1pZj0icm91bmQuZ29vZHNfbGlzdC5sZW5ndGggPiAzIiBjbGFzcz0ibW9yZS1nb29kcyI+CiAgICAgICAgICAgICt7eyByb3VuZC5nb29kc19saXN0Lmxlbmd0aCAtIDMgfX3kuKrllYblk4EKICAgICAgICAgIDwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4KCiAgPCEtLSDova7mrKHliJfooaggLS0+CiAgPGRpdiBjbGFzcz0icm91bmRzLXRhYmxlIj4KICAgIDxkaXYgY2xhc3M9ImQtZmxleCBqdXN0aWZ5LWNvbnRlbnQtYmV0d2VlbiBhbGlnbi1pdGVtcy1jZW50ZXIgbWItMyI+CiAgICAgIDxoMz7ova7mrKHliJfooag8L2gzPgogICAgICA8ZGl2IGNsYXNzPSJ0YWJsZS1jb250cm9scyI+CiAgICAgICAgPGRpdiBjbGFzcz0iYXV0by1yZWZyZXNoLWNvbnRyb2wiPgogICAgICAgICAgPGxhYmVsIGNsYXNzPSJmb3JtLWNoZWNrLWxhYmVsIj4KICAgICAgICAgICAgPGlucHV0CiAgICAgICAgICAgICAgdHlwZT0iY2hlY2tib3giCiAgICAgICAgICAgICAgdi1tb2RlbD0iYXV0b1JlZnJlc2giCiAgICAgICAgICAgICAgQGNoYW5nZT0idG9nZ2xlQXV0b1JlZnJlc2giCiAgICAgICAgICAgICAgY2xhc3M9ImZvcm0tY2hlY2staW5wdXQiCiAgICAgICAgICAgID4KICAgICAgICAgICAg6Ieq5Yqo5Yi35pawICh7eyByZWZyZXNoSW50ZXJ2YWwgLyAxMDAwIH1956eSKQogICAgICAgICAgPC9sYWJlbD4KICAgICAgICAgIDxzcGFuIHYtaWY9ImF1dG9SZWZyZXNoIiBjbGFzcz0icmVmcmVzaC1jb3VudGRvd24iPgogICAgICAgICAgICDkuIvmrKHliLfmlrA6IHt7IHJlZnJlc2hDb3VudGRvd24gfX3np5IKICAgICAgICAgIDwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8YnV0dG9uIEBjbGljaz0icmVmcmVzaERhdGEiIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSBidG4tc20iPgogICAgICAgICAgPGkgY2xhc3M9ImZhIGZhLXJlZnJlc2giIDpjbGFzcz0ieyAnZmEtc3Bpbic6IGxvYWRpbmcgfSI+PC9pPgogICAgICAgICAg5omL5Yqo5Yi35pawCiAgICAgICAgPC9idXR0b24+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CgogICAgPCEtLSDmibnph4/mk43kvZzljLrln58gLS0+CiAgICA8ZGl2IHYtaWY9InNlbGVjdGVkUm91bmRzLmxlbmd0aCA+IDAiIGNsYXNzPSJiYXRjaC1vcGVyYXRpb25zIG1iLTMiPgogICAgICA8ZGl2IGNsYXNzPSJhbGVydCBhbGVydC1pbmZvIj4KICAgICAgICDlt7LpgInmi6kge3sgc2VsZWN0ZWRSb3VuZHMubGVuZ3RoIH19IOS4qui9ruasoQogICAgICAgIDxkaXYgY2xhc3M9ImJhdGNoLWFjdGlvbnMiPgogICAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImJhdGNoQ2xvc2VSb3VuZHMiIGNsYXNzPSJidG4gYnRuLXdhcm5pbmcgYnRuLXNtIj7mibnph4/lhbPpl608L2J1dHRvbj4KICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJiYXRjaERlbGV0ZVJvdW5kcyIgY2xhc3M9ImJ0biBidG4tZGFuZ2VyIGJ0bi1zbSI+5om56YeP5Yig6ZmkPC9idXR0b24+CiAgICAgICAgICA8YnV0dG9uIEBjbGljaz0iY2xlYXJTZWxlY3Rpb24iIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSBidG4tc20iPuWPlua2iOmAieaLqTwvYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDx0YWJsZSBjbGFzcz0idGFibGUiPgogICAgICA8dGhlYWQ+CiAgICAgICAgPHRyPgogICAgICAgICAgPHRoPgogICAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgICB0eXBlPSJjaGVja2JveCIKICAgICAgICAgICAgICBAY2hhbmdlPSJ0b2dnbGVTZWxlY3RBbGwiCiAgICAgICAgICAgICAgOmNoZWNrZWQ9ImlzQWxsU2VsZWN0ZWQiCiAgICAgICAgICAgICAgOmluZGV0ZXJtaW5hdGUucHJvcD0iaXNJbmRldGVybWluYXRlIgogICAgICAgICAgICA+CiAgICAgICAgICA8L3RoPgogICAgICAgICAgPHRoPui9ruasoee8luWPtzwvdGg+CiAgICAgICAgICA8dGg+6L2u5qyh5ZCN56ewPC90aD4KICAgICAgICAgIDx0aD7llYblk4HmlbDph488L3RoPgogICAgICAgICAgPHRoPuaAu+W6k+WtmDwvdGg+CiAgICAgICAgICA8dGg+5bey5ZSu5Ye6PC90aD4KICAgICAgICAgIDx0aD7lvIDlp4vml7bpl7Q8L3RoPgogICAgICAgICAgPHRoPue7k+adn+aXtumXtDwvdGg+CiAgICAgICAgICA8dGg+54q25oCBPC90aD4KICAgICAgICAgIDx0aD7mk43kvZw8L3RoPgogICAgICAgIDwvdHI+CiAgICAgIDwvdGhlYWQ+CiAgICAgIDx0Ym9keT4KICAgICAgICA8dHIgdi1mb3I9InJvdW5kIGluIHJvdW5kc0xpc3QuZGF0YSIgOmtleT0icm91bmQuaWQiPgogICAgICAgICAgPHRkPgogICAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgICB0eXBlPSJjaGVja2JveCIKICAgICAgICAgICAgICA6dmFsdWU9InJvdW5kLmlkIgogICAgICAgICAgICAgIHYtbW9kZWw9InNlbGVjdGVkUm91bmRzIgogICAgICAgICAgICAgIEBjaGFuZ2U9InVwZGF0ZVNlbGVjdGlvbiIKICAgICAgICAgICAgPgogICAgICAgICAgPC90ZD4KICAgICAgICAgIDx0ZD4je3sgcm91bmQucm91bmRfbnVtYmVyIH19PC90ZD4KICAgICAgICAgIDx0ZD57eyByb3VuZC5yb3VuZF9uYW1lIH19PC90ZD4KICAgICAgICAgIDx0ZD57eyByb3VuZC5nb29kc19jb3VudCB9fTwvdGQ+CiAgICAgICAgICA8dGQ+e3sgcm91bmQudG90YWxfc3RvY2sgfX08L3RkPgogICAgICAgICAgPHRkPnt7IHJvdW5kLnRvdGFsX3NvbGQgfX08L3RkPgogICAgICAgICAgPHRkPnt7IGZvcm1hdERhdGVUaW1lKHJvdW5kLnN0YXJ0X3RpbWUpIH19PC90ZD4KICAgICAgICAgIDx0ZD57eyBmb3JtYXREYXRlVGltZShyb3VuZC5lbmRfdGltZSkgfX08L3RkPgogICAgICAgICAgPHRkPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdGF0dXMtaW5mbyI+CiAgICAgICAgICAgICAgPHNwYW4gOmNsYXNzPSJnZXRTdGF0dXNDbGFzcyhyb3VuZC5zdGF0dXMpIj57eyBnZXRTdGF0dXNUZXh0KHJvdW5kLnN0YXR1cykgfX08L3NwYW4+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0idGltZS1pbmZvIj4KICAgICAgICAgICAgICAgIDxzbWFsbCB2LWlmPSJyb3VuZC5zdGF0dXMgPT09ICd1cGNvbWluZyciPgogICAgICAgICAgICAgICAgICDlvIDlp4vml7bpl7Q6IHt7IGZvcm1hdFRpbWUocm91bmQuc3RhcnRfdGltZSkgfX0KICAgICAgICAgICAgICAgICAgPHNwYW4gdi1pZj0iZ2V0Q291bnRkb3duKHJvdW5kLnN0YXJ0X3RpbWUpIiBjbGFzcz0iY291bnRkb3duIj4KICAgICAgICAgICAgICAgICAgICAoe3sgZ2V0Q291bnRkb3duKHJvdW5kLnN0YXJ0X3RpbWUpIH19KQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L3NtYWxsPgogICAgICAgICAgICAgICAgPHNtYWxsIHYtZWxzZS1pZj0icm91bmQuc3RhdHVzID09PSAnYWN0aXZlJyI+CiAgICAgICAgICAgICAgICAgIOe7k+adn+aXtumXtDoge3sgZm9ybWF0VGltZShyb3VuZC5lbmRfdGltZSkgfX0KICAgICAgICAgICAgICAgICAgPHNwYW4gdi1pZj0iZ2V0Q291bnRkb3duKHJvdW5kLmVuZF90aW1lKSIgY2xhc3M9ImNvdW50ZG93biB0ZXh0LWRhbmdlciI+CiAgICAgICAgICAgICAgICAgICAgKHt7IGdldENvdW50ZG93bihyb3VuZC5lbmRfdGltZSkgfX0pCiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvc21hbGw+CiAgICAgICAgICAgICAgICA8c21hbGwgdi1lbHNlLWlmPSJyb3VuZC5zdGF0dXMgPT09ICdlbmRlZCciPgogICAgICAgICAgICAgICAgICDlt7Lkuo4ge3sgZm9ybWF0VGltZShyb3VuZC5lbmRfdGltZSkgfX0g57uT5p2fCiAgICAgICAgICAgICAgICA8L3NtYWxsPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvdGQ+CiAgICAgICAgICA8dGQ+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InJvdW5kLWFjdGlvbnMiPgogICAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJ2aWV3Um91bmREZXRhaWxzKHJvdW5kKSIgY2xhc3M9ImJ0biBidG4taW5mbyBidG4tc20iPuafpeeci+ivpuaDhTwvYnV0dG9uPgoKICAgICAgICAgICAgICA8IS0tIOWNs+WwhuW8gOWni+eahOi9ruasoSAtLT4KICAgICAgICAgICAgICA8dGVtcGxhdGUgdi1pZj0icm91bmQuc3RhdHVzID09PSAndXBjb21pbmcnIj4KICAgICAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJlZGl0Um91bmQocm91bmQpIiBjbGFzcz0iYnRuIGJ0bi1zdWNjZXNzIGJ0bi1zbSI+57yW6L6RPC9idXR0b24+CiAgICAgICAgICAgICAgICA8YnV0dG9uIEBjbGljaz0iY2xvc2VSb3VuZChyb3VuZCkiIGNsYXNzPSJidG4gYnRuLXdhcm5pbmcgYnRuLXNtIj7lj5bmtojova7mrKE8L2J1dHRvbj4KICAgICAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJkZWxldGVSb3VuZChyb3VuZCkiIGNsYXNzPSJidG4gYnRuLWRhbmdlciBidG4tc20iPuWIoOmZpDwvYnV0dG9uPgogICAgICAgICAgICAgIDwvdGVtcGxhdGU+CgogICAgICAgICAgICAgIDwhLS0g6L+b6KGM5Lit55qE6L2u5qyhIC0tPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSB2LWlmPSJyb3VuZC5zdGF0dXMgPT09ICdhY3RpdmUnIj4KICAgICAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJzaG93RXh0ZW5kTW9kYWwocm91bmQpIiBjbGFzcz0iYnRuIGJ0bi1wcmltYXJ5IGJ0bi1zbSI+5bu25pyfPC9idXR0b24+CiAgICAgICAgICAgICAgICA8YnV0dG9uIEBjbGljaz0iY2xvc2VSb3VuZChyb3VuZCkiIGNsYXNzPSJidG4gYnRuLXdhcm5pbmcgYnRuLXNtIj7nq4vljbPnu5PmnZ88L2J1dHRvbj4KICAgICAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJkZWxldGVSb3VuZChyb3VuZCkiIGNsYXNzPSJidG4gYnRuLWRhbmdlciBidG4tc20iPuWIoOmZpDwvYnV0dG9uPgogICAgICAgICAgICAgIDwvdGVtcGxhdGU+CgogICAgICAgICAgICAgIDwhLS0g5bey57uT5p2f55qE6L2u5qyhIC0tPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSB2LWlmPSJyb3VuZC5zdGF0dXMgPT09ICdlbmRlZCciPgogICAgICAgICAgICAgICAgPGJ1dHRvbiBAY2xpY2s9InNob3dSZXN0YXJ0TW9kYWwocm91bmQpIiBjbGFzcz0iYnRuIGJ0bi1zdWNjZXNzIGJ0bi1zbSI+6YeN5paw5ZCv5YqoPC9idXR0b24+CiAgICAgICAgICAgICAgICA8YnV0dG9uIEBjbGljaz0ic2hvd0NvcHlNb2RhbChyb3VuZCkiIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSBidG4tc20iPuWkjeWItui9ruasoTwvYnV0dG9uPgogICAgICAgICAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImRlbGV0ZVJvdW5kKHJvdW5kKSIgY2xhc3M9ImJ0biBidG4tZGFuZ2VyIGJ0bi1zbSI+5Yig6ZmkPC9idXR0b24+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L3RkPgogICAgICAgIDwvdHI+CiAgICAgIDwvdGJvZHk+CiAgICA8L3RhYmxlPgogIDwvZGl2PgoKICA8IS0tIOW7tuacn+i9ruasoeaooeaAgeahhiAtLT4KICA8ZGl2IHYtaWY9InNob3dFeHRlbmRNb2RhbEZsYWciIGNsYXNzPSJtb2RhbC1vdmVybGF5IiBAY2xpY2s9ImNsb3NlRXh0ZW5kTW9kYWwiPgogICAgPGRpdiBjbGFzcz0ibW9kYWwtY29udGVudCIgQGNsaWNrLnN0b3A+CiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWhlYWRlciI+CiAgICAgICAgPGg0PuW7tuacn+i9ruasoTwvaDQ+CiAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImNsb3NlRXh0ZW5kTW9kYWwiIGNsYXNzPSJjbG9zZS1idG4iPiZ0aW1lczs8L2J1dHRvbj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWJvZHkiPgogICAgICAgIDxwPui9ruasoToge3sgZXh0ZW5kUm91bmQgJiYgZXh0ZW5kUm91bmQucm91bmRfbmFtZSB9fTwvcD4KICAgICAgICA8ZGl2IGNsYXNzPSJmb3JtLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbD7lu7bmnJ/ml7bpl7QgKOWIhumSnyk6PC9sYWJlbD4KICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICB0eXBlPSJudW1iZXIiCiAgICAgICAgICAgIHYtbW9kZWw9ImV4dGVuZE1pbnV0ZXMiCiAgICAgICAgICAgIGNsYXNzPSJmb3JtLWNvbnRyb2wiCiAgICAgICAgICAgIG1pbj0iMSIKICAgICAgICAgICAgbWF4PSIxNDQwIgogICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5bu25pyf5YiG6ZKf5pWwIgogICAgICAgICAgPgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0ibW9kYWwtZm9vdGVyIj4KICAgICAgICA8YnV0dG9uIEBjbGljaz0iY29uZmlybUV4dGVuZCIgY2xhc3M9ImJ0biBidG4tcHJpbWFyeSIgOmRpc2FibGVkPSIhZXh0ZW5kTWludXRlcyI+56Gu6K6k5bu25pyfPC9idXR0b24+CiAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImNsb3NlRXh0ZW5kTW9kYWwiIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSI+5Y+W5raIPC9idXR0b24+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CgogIDwhLS0g6YeN5paw5ZCv5Yqo6L2u5qyh5qih5oCB5qGGIC0tPgogIDxkaXYgdi1pZj0ic2hvd1Jlc3RhcnRNb2RhbEZsYWciIGNsYXNzPSJtb2RhbC1vdmVybGF5IiBAY2xpY2s9ImNsb3NlUmVzdGFydE1vZGFsIj4KICAgIDxkaXYgY2xhc3M9Im1vZGFsLWNvbnRlbnQiIEBjbGljay5zdG9wPgogICAgICA8ZGl2IGNsYXNzPSJtb2RhbC1oZWFkZXIiPgogICAgICAgIDxoND7ph43mlrDlkK/liqjova7mrKE8L2g0PgogICAgICAgIDxidXR0b24gQGNsaWNrPSJjbG9zZVJlc3RhcnRNb2RhbCIgY2xhc3M9ImNsb3NlLWJ0biI+JnRpbWVzOzwvYnV0dG9uPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0ibW9kYWwtYm9keSI+CiAgICAgICAgPHA+6L2u5qyhOiB7eyByZXN0YXJ0Um91bmQgJiYgcmVzdGFydFJvdW5kLnJvdW5kX25hbWUgfX08L3A+CiAgICAgICAgPGRpdiBjbGFzcz0iZm9ybS1ncm91cCI+CiAgICAgICAgICA8bGFiZWw+5paw5byA5aeL5pe26Ze0OjwvbGFiZWw+CiAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgdHlwZT0iZGF0ZXRpbWUtbG9jYWwiCiAgICAgICAgICAgIHYtbW9kZWw9Im5ld1N0YXJ0VGltZSIKICAgICAgICAgICAgY2xhc3M9ImZvcm0tY29udHJvbCIKICAgICAgICAgID4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJmb3JtLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbD7mlrDnu5PmnZ/ml7bpl7Q6PC9sYWJlbD4KICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICB0eXBlPSJkYXRldGltZS1sb2NhbCIKICAgICAgICAgICAgdi1tb2RlbD0ibmV3RW5kVGltZSIKICAgICAgICAgICAgY2xhc3M9ImZvcm0tY29udHJvbCIKICAgICAgICAgID4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWZvb3RlciI+CiAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImNvbmZpcm1SZXN0YXJ0IiBjbGFzcz0iYnRuIGJ0bi1wcmltYXJ5Ij7noa7orqTph43lkK88L2J1dHRvbj4KICAgICAgICA8YnV0dG9uIEBjbGljaz0iY2xvc2VSZXN0YXJ0TW9kYWwiIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSI+5Y+W5raIPC9idXR0b24+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CgogIDwhLS0g5aSN5Yi26L2u5qyh5qih5oCB5qGGIC0tPgogIDxkaXYgdi1pZj0ic2hvd0NvcHlNb2RhbEZsYWciIGNsYXNzPSJtb2RhbC1vdmVybGF5IiBAY2xpY2s9ImNsb3NlQ29weU1vZGFsIj4KICAgIDxkaXYgY2xhc3M9Im1vZGFsLWNvbnRlbnQiIEBjbGljay5zdG9wPgogICAgICA8ZGl2IGNsYXNzPSJtb2RhbC1oZWFkZXIiPgogICAgICAgIDxoND7lpI3liLbova7mrKE8L2g0PgogICAgICAgIDxidXR0b24gQGNsaWNrPSJjbG9zZUNvcHlNb2RhbCIgY2xhc3M9ImNsb3NlLWJ0biI+JnRpbWVzOzwvYnV0dG9uPgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0ibW9kYWwtYm9keSI+CiAgICAgICAgPHA+5rqQ6L2u5qyhOiB7eyBjb3B5Um91bmQgJiYgY29weVJvdW5kLnJvdW5kX25hbWUgfX08L3A+CiAgICAgICAgPGRpdiBjbGFzcz0iZm9ybS1ncm91cCI+CiAgICAgICAgICA8bGFiZWw+5paw6L2u5qyh5ZCN56ewOjwvbGFiZWw+CiAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgdi1tb2RlbD0ibmV3Um91bmROYW1lIgogICAgICAgICAgICBjbGFzcz0iZm9ybS1jb250cm9sIgogICAgICAgICAgICA6cGxhY2Vob2xkZXI9ImNvcHlSb3VuZCAmJiBjb3B5Um91bmQucm91bmRfbmFtZSA/IGNvcHlSb3VuZC5yb3VuZF9uYW1lICsgJyAo5aSN5Yi2KScgOiAnJyIKICAgICAgICAgID4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJmb3JtLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbD7lvIDlp4vml7bpl7Q6PC9sYWJlbD4KICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICB0eXBlPSJkYXRldGltZS1sb2NhbCIKICAgICAgICAgICAgdi1tb2RlbD0iY29weVN0YXJ0VGltZSIKICAgICAgICAgICAgY2xhc3M9ImZvcm0tY29udHJvbCIKICAgICAgICAgID4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJmb3JtLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbD7nu5PmnZ/ml7bpl7Q6PC9sYWJlbD4KICAgICAgICAgIDxpbnB1dAogICAgICAgICAgICB0eXBlPSJkYXRldGltZS1sb2NhbCIKICAgICAgICAgICAgdi1tb2RlbD0iY29weUVuZFRpbWUiCiAgICAgICAgICAgIGNsYXNzPSJmb3JtLWNvbnRyb2wiCiAgICAgICAgICA+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJtb2RhbC1mb290ZXIiPgogICAgICAgIDxidXR0b24gQGNsaWNrPSJjb25maXJtQ29weSIgY2xhc3M9ImJ0biBidG4tcHJpbWFyeSI+56Gu6K6k5aSN5Yi2PC9idXR0b24+CiAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImNsb3NlQ29weU1vZGFsIiBjbGFzcz0iYnRuIGJ0bi1zZWNvbmRhcnkiPuWPlua2iDwvYnV0dG9uPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogIDwvZGl2PgoKICA8IS0tIOWIm+W7uui9ruasoeaooeaAgeahhiAtLT4KICA8ZGl2IHYtaWY9InNob3dBZGRNb2RhbCIgY2xhc3M9Im1vZGFsLW92ZXJsYXkiIEBjbGljaz0iY2xvc2VNb2RhbCI+CiAgICA8ZGl2IGNsYXNzPSJtb2RhbC1jb250ZW50IiBAY2xpY2suc3RvcD4KICAgICAgPGRpdiBjbGFzcz0ibW9kYWwtaGVhZGVyIj4KICAgICAgICA8aDM+5Yib5bu65paw6L2u5qyhPC9oMz4KICAgICAgICA8YnV0dG9uIEBjbGljaz0iY2xvc2VNb2RhbCIgY2xhc3M9ImNsb3NlLWJ0biI+JnRpbWVzOzwvYnV0dG9uPgogICAgICA8L2Rpdj4KICAgICAgCiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWJvZHkiPgogICAgICAgIDwhLS0g5pW054K556eS5p2A5pe26Ze06K6+572uIC0tPgogICAgICAgIDxkaXYgY2xhc3M9ImhvdXJseS1mbGFzaC1zZXR0aW5ncyI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJzZXR0aW5nLWhlYWRlciI+CiAgICAgICAgICAgIDxoND7mlbTngrnnp5LmnYDorr7nva48L2g0PgogICAgICAgICAgICA8cCBjbGFzcz0ic2V0dGluZy1kZXNjcmlwdGlvbiI+5q+P5bCP5pe25pW054K55byA5aeL77yM5oyB57utNTXliIbpkp/vvIwyNOWwj+aXtuS4jemXtOaWrTwvcD4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDxkaXYgY2xhc3M9ImZvcm0tcm93Ij4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iZm9ybS1ncm91cCI+CiAgICAgICAgICAgICAgPGxhYmVsPua0u+WKqOW8gOWni+aXpeacnyAqPC9sYWJlbD4KICAgICAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgICAgIHYtbW9kZWw9Im5ld1JvdW5kLnN0YXJ0X2RhdGUiCiAgICAgICAgICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgICAgICAgICAgY2xhc3M9ImZvcm0tY29udHJvbCIKICAgICAgICAgICAgICAgIHJlcXVpcmVkCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImZvcm0tZ3JvdXAiPgogICAgICAgICAgICAgIDxsYWJlbD7mtLvliqjnu5PmnZ/ml6XmnJ8gKjwvbGFiZWw+CiAgICAgICAgICAgICAgPGlucHV0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJuZXdSb3VuZC5lbmRfZGF0ZSIKICAgICAgICAgICAgICAgIHR5cGU9ImRhdGUiCiAgICAgICAgICAgICAgICBjbGFzcz0iZm9ybS1jb250cm9sIgogICAgICAgICAgICAgICAgOm1pbj0ibmV3Um91bmQuc3RhcnRfZGF0ZSIKICAgICAgICAgICAgICAgIHJlcXVpcmVkCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8IS0tIOiHquWKqOeUn+aIkOeahOi9ruasoeWQjeensOmihOiniCAtLT4KICAgICAgICAgIDxkaXYgdi1pZj0iZ2VuZXJhdGVkUm91bmROYW1lIiBjbGFzcz0icm91bmQtbmFtZS1wcmV2aWV3Ij4KICAgICAgICAgICAgPGg1Pui9ruasoeWQjeensO+8mjwvaDU+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Im5hbWUtZGlzcGxheSI+e3sgZ2VuZXJhdGVkUm91bmROYW1lIH19PC9kaXY+CiAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICA8IS0tIOaXtuautemihOiniCAtLT4KICAgICAgICAgIDxkaXYgdi1pZj0iaG91cmx5U2xvdFByZXZpZXcubGVuZ3RoID4gMCIgY2xhc3M9InNsb3QtcHJldmlldyI+CiAgICAgICAgICAgIDxoNT7lsIbnlJ/miJDku6XkuIvnp5LmnYDml7bmrrXvvJo8L2g1PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzbG90LWxpc3QtY29udGFpbmVyIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzbG90LWxpc3QiPgogICAgICAgICAgICAgICAgPGRpdiB2LWZvcj0iKHNsb3QsIGluZGV4KSBpbiBob3VybHlTbG90UHJldmlldyIgOmtleT0iaW5kZXgiIGNsYXNzPSJzbG90LWl0ZW0iPgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0ic2xvdC1udW1iZXIiPuesrHt7IGluZGV4ICsgMSB9feWcujwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InNsb3QtdGltZSI+e3sgZm9ybWF0U2xvdFRpbWUoc2xvdC5zdGFydCkgfX0gLSB7eyBmb3JtYXRTbG90VGltZShzbG90LmVuZCkgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJzbG90LWR1cmF0aW9uIj41NeWIhumSnzwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPHNwYW4gdi1pZj0iaXNTbG90SW5QYXN0KHNsb3Quc3RhcnQpIiBjbGFzcz0ic2xvdC1zdGF0dXMgcGFzdCI+5bey6L+H5pyfPC9zcGFuPgogICAgICAgICAgICAgICAgICA8c3BhbiB2LWVsc2UtaWY9ImlzU2xvdEFjdGl2ZShzbG90LnN0YXJ0LCBzbG90LmVuZCkiIGNsYXNzPSJzbG90LXN0YXR1cyBhY3RpdmUiPui/m+ihjOS4rTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPHNwYW4gdi1lbHNlIGNsYXNzPSJzbG90LXN0YXR1cyB1cGNvbWluZyI+5b6F5byA5aeLPC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9ImhvdXJseVNsb3RQcmV2aWV3Lmxlbmd0aCA+IDEwIiBjbGFzcz0ibW9yZS1zbG90cyI+CiAgICAgICAgICAgICAgICAgIC4uLiDov5jmnIkge3sgaG91cmx5U2xvdFByZXZpZXcubGVuZ3RoIC0gMTAgfX0g5Liq5pe25q61CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNsb3Qtc3VtbWFyeSI+CiAgICAgICAgICAgICAgPHNwYW4+5YWxIHt7IGhvdXJseVNsb3RQcmV2aWV3Lmxlbmd0aCB9fSDkuKrml7bmrrU8L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InZhbGlkLXNsb3RzIj7vvIjmnInmlYjml7bmrrXvvJp7eyB2YWxpZFNsb3RzQ291bnQgfX0g5Liq77yJPC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgoKICAgICAgICA8IS0tIOWVhuWTgemAieaLqSAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJmb3JtLWdyb3VwIj4KICAgICAgICAgIDxsYWJlbD7pgInmi6nllYblk4EgKiAo54K55Ye75ZWG5ZOB5Y2h54mH6L+b6KGM6YCJ5oupKTwvbGFiZWw+CgogICAgICAgICAgPCEtLSDliqDovb3nirbmgIEgLS0+CiAgICAgICAgICA8ZGl2IHYtaWY9ImxvYWRpbmdHb29kcyIgY2xhc3M9ImxvYWRpbmctc3RhdGUiPgogICAgICAgICAgICA8aSBjbGFzcz0ibG9hZGluZy1pY29uIj7ij7M8L2k+CiAgICAgICAgICAgIDxzcGFuPuato+WcqOWKoOi9veWVhuWTgeWIl+ihqC4uLjwvc3Bhbj4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDwhLS0g5ZWG5ZOB5YiX6KGoIC0tPgogICAgICAgICAgPGRpdiB2LWVsc2UtaWY9Imdvb2RzTGlzdCAmJiBnb29kc0xpc3QubGVuZ3RoID4gMCIgY2xhc3M9Imdvb2RzLXNlbGVjdGlvbi1ncmlkIj4KICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgIHYtZm9yPSJnb29kcyBpbiBnb29kc0xpc3QiCiAgICAgICAgICAgICAgOmtleT0iZ29vZHMuaWQiCiAgICAgICAgICAgICAgY2xhc3M9Imdvb2RzLWNhcmQiCiAgICAgICAgICAgICAgOmNsYXNzPSJ7CiAgICAgICAgICAgICAgICAnc2VsZWN0ZWQnOiBpc0dvb2RzU2VsZWN0ZWQoZ29vZHMuaWQpLAogICAgICAgICAgICAgICAgJ2Rpc2FibGVkJzogIWdvb2RzLmNhbl9zZWxlY3QKICAgICAgICAgICAgICB9IgogICAgICAgICAgICAgIEBjbGljaz0idG9nZ2xlR29vZHMoZ29vZHMpIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPCEtLSDllYblk4Hln7rmnKzkv6Hmga8gLS0+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZ29vZHMtY2FyZC1oZWFkZXIiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZ29vZHMtaW1hZ2UtY29udGFpbmVyIj4KICAgICAgICAgICAgICAgICAgPGltZyA6c3JjPSJnb29kcy5saXN0X3BpY191cmwiIDphbHQ9Imdvb2RzLm5hbWUiIGNsYXNzPSJnb29kcy1jYXJkLWltYWdlIiAvPgogICAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9ImlzR29vZHNTZWxlY3RlZChnb29kcy5pZCkiIGNsYXNzPSJzZWxlY3RlZC1iYWRnZSI+CiAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImNoZWNrbWFyayI+4pyTPC9pPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSIhZ29vZHMuY2FuX3NlbGVjdCIgY2xhc3M9ImRpc2FibGVkLW92ZXJsYXkiPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPuW3suWPguS4juWFtuS7luenkuadgDwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imdvb2RzLWNhcmQtaW5mbyI+CiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzcz0iZ29vZHMtbmFtZSI+e3sgZ29vZHMubmFtZSB9fTwvaDQ+CiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJvcmlnaW5hbC1wcmljZSI+5Y6f5Lu3OiDCpXt7IGdvb2RzLnJldGFpbF9wcmljZSB9fTwvcD4KICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSIhZ29vZHMuY2FuX3NlbGVjdCIgY2xhc3M9Indhcm5pbmctdGV4dCI+CiAgICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9Indhcm5pbmctaWNvbiI+4pqgPC9pPgogICAgICAgICAgICAgICAgICAgIDxzcGFuPuW3suWPguS4juWFtuS7luenkuadgOa0u+WKqDwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgPCEtLSDnp5LmnYDorr7nva7pnaLmnb8gLS0+CiAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpc0dvb2RzU2VsZWN0ZWQoZ29vZHMuaWQpIiBjbGFzcz0iZ29vZHMtc2V0dGluZ3MtcGFuZWwiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2V0dGluZ3MtdGl0bGUiPuenkuadgOiuvue9rjwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2V0dGluZ3MtZ3JpZCI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNldHRpbmctaXRlbSBmdWxsLXdpZHRoIj4KICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5oqY5omj6K6+572uPC9sYWJlbD4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJkaXNjb3VudC1zZXR0aW5nIj4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImRpc2NvdW50LWlucHV0LWdyb3VwIj4KICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0CiAgICAgICAgICAgICAgICAgICAgICAgICAgdi1tb2RlbC5udW1iZXI9ImdldFNlbGVjdGVkR29vZHMoZ29vZHMuaWQpLmRpc2NvdW50X3JhdGUiCiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT0ibnVtYmVyIgogICAgICAgICAgICAgICAgICAgICAgICAgIHN0ZXA9IjEiCiAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPSIxMCIKICAgICAgICAgICAgICAgICAgICAgICAgICBtYXg9IjkwIgogICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJkaXNjb3VudC1pbnB1dCIKICAgICAgICAgICAgICAgICAgICAgICAgICBAaW5wdXQ9InVwZGF0ZUZsYXNoUHJpY2VCeURpc2NvdW50KGdvb2RzLmlkKSIKICAgICAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcAogICAgICAgICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iZGlzY291bnQtdW5pdCI+JSBPRkY8L3NwYW4+CiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InByaWNlLXByZXZpZXciPgogICAgICAgICAgICAgICAgICAgICAgICDljp/ku7c6IMKle3sgcGFyc2VGbG9hdChnb29kcy5yZXRhaWxfcHJpY2UpLnRvRml4ZWQoMikgfX0g4oaSCiAgICAgICAgICAgICAgICAgICAgICAgIOenkuadgOS7tzogwqV7eyBnZXRTZWxlY3RlZEdvb2RzKGdvb2RzLmlkKS5mbGFzaF9wcmljZSB9fQogICAgICAgICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcmljZS1yYW5nZS1oaW50IiB2LWlmPSJnb29kcy5wcmljZV9yYW5nZSI+CiAgICAgICAgICAgICAgICAgICAgICAgIOWVhuWTgeS7t+agvOWMuumXtDoge3sgZ29vZHMucHJpY2VfcmFuZ2UgfX0KICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KCiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNldHRpbmctaXRlbSI+CiAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuenkuadgOW6k+WtmDwvbGFiZWw+CiAgICAgICAgICAgICAgICAgICAgPGlucHV0CiAgICAgICAgICAgICAgICAgICAgICB2LW1vZGVsLm51bWJlcj0iZ2V0U2VsZWN0ZWRHb29kcyhnb29kcy5pZCkuc3RvY2siCiAgICAgICAgICAgICAgICAgICAgICB0eXBlPSJudW1iZXIiCiAgICAgICAgICAgICAgICAgICAgICBtaW49IjEiCiAgICAgICAgICAgICAgICAgICAgICBtYXg9Ijk5OTkiCiAgICAgICAgICAgICAgICAgICAgICBjbGFzcz0ic3RvY2staW5wdXQiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcAogICAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2V0dGluZy1pdGVtIj4KICAgICAgICAgICAgICAgICAgICA8bGFiZWw+6ZmQ6LSt5pWw6YePPC9sYWJlbD4KICAgICAgICAgICAgICAgICAgICA8aW5wdXQKICAgICAgICAgICAgICAgICAgICAgIHYtbW9kZWwubnVtYmVyPSJnZXRTZWxlY3RlZEdvb2RzKGdvb2RzLmlkKS5saW1pdF9xdWFudGl0eSIKICAgICAgICAgICAgICAgICAgICAgIHR5cGU9Im51bWJlciIKICAgICAgICAgICAgICAgICAgICAgIG1pbj0iMSIKICAgICAgICAgICAgICAgICAgICAgIG1heD0iOTkiCiAgICAgICAgICAgICAgICAgICAgICBjbGFzcz0ibGltaXQtaW5wdXQiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2suc3RvcAogICAgICAgICAgICAgICAgICAgIC8+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CgogICAgICAgICAgPCEtLSDnqbrnirbmgIHmmL7npLogLS0+CiAgICAgICAgICA8ZGl2IHYtZWxzZSBjbGFzcz0iZW1wdHktc3RhdGUiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJlbXB0eS1pY29uIj7wn5OmPC9kaXY+CiAgICAgICAgICAgIDxwIGNsYXNzPSJlbXB0eS10ZXh0Ij7mmoLml6Dlj6/pgInllYblk4E8L3A+CiAgICAgICAgICAgIDxwIGNsYXNzPSJlbXB0eS1oaW50Ij7or7fnoa7kv53mnInkuIrmnrbnmoTllYblk4HvvIzkuJTmnKrlj4LkuI7lhbbku5bnp5LmnYDmtLvliqg8L3A+CiAgICAgICAgICAgIDxidXR0b24gQGNsaWNrPSJsb2FkR29vZHNMaXN0IiBjbGFzcz0iYnRuIGJ0bi1zZWNvbmRhcnkgYnRuLXNtIj7ph43mlrDliqDovb08L2J1dHRvbj4KICAgICAgICAgIDwvZGl2PgoKICAgICAgICAgIDwhLS0g6YCJ5oup5o+Q56S6IC0tPgogICAgICAgICAgPGRpdiBjbGFzcz0ic2VsZWN0aW9uLWhpbnQiPgogICAgICAgICAgICA8cCB2LWlmPSJuZXdSb3VuZC5nb29kc19saXN0Lmxlbmd0aCA9PT0gMCIgY2xhc3M9ImhpbnQtdGV4dCI+CiAgICAgICAgICAgICAgPGkgY2xhc3M9ImluZm8taWNvbiI+4oS5PC9pPgogICAgICAgICAgICAgIOivt+eCueWHu+WVhuWTgeWNoeeJh+mAieaLqeWPguS4juenkuadgOeahOWVhuWTge+8jOWPr+S7pemAieaLqeWkmuS4quWVhuWTgQogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwIHYtZWxzZSBjbGFzcz0ic2VsZWN0ZWQtY291bnQiPgogICAgICAgICAgICAgIOW3sumAieaLqSA8c3Ryb25nPnt7IG5ld1JvdW5kLmdvb2RzX2xpc3QubGVuZ3RoIH19PC9zdHJvbmc+IOS4quWVhuWTgQogICAgICAgICAgICA8L3A+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPCEtLSDlt7LpgInllYblk4HmsYfmgLsgLS0+CiAgICAgICAgPGRpdiB2LWlmPSJuZXdSb3VuZC5nb29kc19saXN0Lmxlbmd0aCA+IDAiIGNsYXNzPSJzZWxlY3RlZC1zdW1tYXJ5Ij4KICAgICAgICAgIDxoND7lt7LpgInmi6kge3sgbmV3Um91bmQuZ29vZHNfbGlzdC5sZW5ndGggfX0g5Liq5ZWG5ZOBPC9oND4KICAgICAgICAgIDxkaXYgY2xhc3M9InN1bW1hcnktbGlzdCI+CiAgICAgICAgICAgIDxkaXYgdi1mb3I9Imdvb2RzIGluIG5ld1JvdW5kLmdvb2RzX2xpc3QiIDprZXk9Imdvb2RzLmdvb2RzX2lkIiBjbGFzcz0ic3VtbWFyeS1pdGVtIj4KICAgICAgICAgICAgICA8c3Bhbj57eyBnb29kcy5nb29kc19uYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuPsKle3sgZ29vZHMuZmxhc2hfcHJpY2UgfX0gKHt7IGNhbGN1bGF0ZURpc2NvdW50UmF0ZShnb29kcy5vcmlnaW5hbF9wcmljZSwgZ29vZHMuZmxhc2hfcHJpY2UpIH19JSBPRkYpPC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuPuW6k+WtmDoge3sgZ29vZHMuc3RvY2sgfX08L3NwYW4+CiAgICAgICAgICAgICAgPGJ1dHRvbiBAY2xpY2s9InJlbW92ZUdvb2RzKGdvb2RzLmdvb2RzX2lkKSIgY2xhc3M9InJlbW92ZS1idG4iPuenu+mZpDwvYnV0dG9uPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KCiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWZvb3RlciI+CiAgICAgICAgPGJ1dHRvbiBAY2xpY2s9ImNsb3NlTW9kYWwiIGNsYXNzPSJidG4gYnRuLXNlY29uZGFyeSI+5Y+W5raIPC9idXR0b24+CiAgICAgICAgPGJ1dHRvbgogICAgICAgICAgQGNsaWNrPSJjcmVhdGVSb3VuZCIKICAgICAgICAgIGNsYXNzPSJidG4gYnRuLXByaW1hcnkiCiAgICAgICAgICA6ZGlzYWJsZWQ9IiFjYW5DcmVhdGVSb3VuZCB8fCBpc0NyZWF0aW5nIgogICAgICAgID4KICAgICAgICAgIHt7IGlzQ3JlYXRpbmcgPyBg5q2j5Zyo5Yib5bu6Li4uICgke2NyZWF0aW9uUHJvZ3Jlc3MuY3VycmVudH0vJHtjcmVhdGlvblByb2dyZXNzLnRvdGFsfSlgIDogJ+WIm+W7uuaVtOeCueenkuadgOi9ruasoScgfX0KICAgICAgICA8L2J1dHRvbj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4KCiAgPCEtLSDova7mrKHor6bmg4XmqKHmgIHmoYYgLS0+CiAgPGRpdiB2LWlmPSJzaG93RGV0YWlsTW9kYWwiIGNsYXNzPSJtb2RhbC1vdmVybGF5IiBAY2xpY2s9InNob3dEZXRhaWxNb2RhbCA9IGZhbHNlIj4KICAgIDxkaXYgY2xhc3M9Im1vZGFsLWNvbnRlbnQgbGFyZ2UiIEBjbGljay5zdG9wPgogICAgICA8ZGl2IGNsYXNzPSJtb2RhbC1oZWFkZXIiPgogICAgICAgIDxoMz7ova7mrKHor6bmg4UgLSB7eyBzZWxlY3RlZFJvdW5kLnJvdW5kX25hbWUgfX08L2gzPgogICAgICAgIDxidXR0b24gQGNsaWNrPSJzaG93RGV0YWlsTW9kYWwgPSBmYWxzZSIgY2xhc3M9ImNsb3NlLWJ0biI+JnRpbWVzOzwvYnV0dG9uPgogICAgICA8L2Rpdj4KICAgICAgCiAgICAgIDxkaXYgY2xhc3M9Im1vZGFsLWJvZHkiPgogICAgICAgIDxkaXYgY2xhc3M9InJvdW5kLWRldGFpbHMiPgogICAgICAgICAgPGRpdiBjbGFzcz0iZGV0YWlsLXNlY3Rpb24iPgogICAgICAgICAgICA8aDQ+5Z+65pys5L+h5oGvPC9oND4KICAgICAgICAgICAgPHA+6L2u5qyh57yW5Y+3OiAje3sgc2VsZWN0ZWRSb3VuZC5yb3VuZF9udW1iZXIgfX08L3A+CiAgICAgICAgICAgIDxwPuW8gOWni+aXtumXtDoge3sgZm9ybWF0RGF0ZVRpbWUoc2VsZWN0ZWRSb3VuZC5zdGFydF90aW1lKSB9fTwvcD4KICAgICAgICAgICAgPHA+57uT5p2f5pe26Ze0OiB7eyBmb3JtYXREYXRlVGltZShzZWxlY3RlZFJvdW5kLmVuZF90aW1lKSB9fTwvcD4KICAgICAgICAgICAgPHA+54q25oCBOiB7eyBnZXRTdGF0dXNUZXh0KHNlbGVjdGVkUm91bmQuc3RhdHVzKSB9fTwvcD4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkZXRhaWwtc2VjdGlvbiI+CiAgICAgICAgICAgIDxoND7llYblk4HliJfooag8L2g0PgogICAgICAgICAgICA8dGFibGUgY2xhc3M9InRhYmxlIj4KICAgICAgICAgICAgICA8dGhlYWQ+CiAgICAgICAgICAgICAgICA8dHI+CiAgICAgICAgICAgICAgICAgIDx0aD7llYblk4E8L3RoPgogICAgICAgICAgICAgICAgICA8dGg+5Y6f5Lu3PC90aD4KICAgICAgICAgICAgICAgICAgPHRoPuenkuadgOS7tzwvdGg+CiAgICAgICAgICAgICAgICAgIDx0aD7mipjmiaM8L3RoPgogICAgICAgICAgICAgICAgICA8dGg+5bqT5a2YPC90aD4KICAgICAgICAgICAgICAgICAgPHRoPuW3suWUrjwvdGg+CiAgICAgICAgICAgICAgICAgIDx0aD7pmZDotK08L3RoPgogICAgICAgICAgICAgICAgPC90cj4KICAgICAgICAgICAgICA8L3RoZWFkPgogICAgICAgICAgICAgIDx0Ym9keT4KICAgICAgICAgICAgICAgIDx0ciB2LWZvcj0iZ29vZHMgaW4gc2VsZWN0ZWRSb3VuZC5nb29kc19saXN0IiA6a2V5PSJnb29kcy5pZCI+CiAgICAgICAgICAgICAgICAgIDx0ZD4KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJnb29kcy1jZWxsIj4KICAgICAgICAgICAgICAgICAgICAgIDxpbWcgOnNyYz0iZ29vZHMuZ29vZHNfaW1hZ2UiIDphbHQ9Imdvb2RzLmdvb2RzX25hbWUiIC8+CiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57eyBnb29kcy5nb29kc19uYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8L3RkPgogICAgICAgICAgICAgICAgICA8dGQ+wqV7eyBnb29kcy5vcmlnaW5hbF9wcmljZSB9fTwvdGQ+CiAgICAgICAgICAgICAgICAgIDx0ZD7CpXt7IGdvb2RzLmZsYXNoX3ByaWNlIH19PC90ZD4KICAgICAgICAgICAgICAgICAgPHRkPnt7IGdvb2RzLmRpc2NvdW50X3JhdGUgfX0lPC90ZD4KICAgICAgICAgICAgICAgICAgPHRkPnt7IGdvb2RzLnN0b2NrIH19PC90ZD4KICAgICAgICAgICAgICAgICAgPHRkPnt7IGdvb2RzLnNvbGRfY291bnQgfX08L3RkPgogICAgICAgICAgICAgICAgICA8dGQ+e3sgZ29vZHMubGltaXRfcXVhbnRpdHkgfX08L3RkPgogICAgICAgICAgICAgICAgPC90cj4KICAgICAgICAgICAgICA8L3Rib2R5PgogICAgICAgICAgICA8L3RhYmxlPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}