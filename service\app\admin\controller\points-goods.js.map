{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\points-goods.js"], "names": ["Base", "require", "module", "exports", "listAction", "console", "log", "page", "get", "limit", "status", "category_id", "keyword", "where", "parseInt", "pointsGoodsModel", "model", "list", "alias", "join", "field", "order", "select", "total", "count", "item", "goods_specification_ids", "specInfo", "getSpecificationInfo", "specification_info", "length", "success", "error", "fail", "availableGoodsAction", "goodsModel", "leftJoin", "is_points_goods", "points_goods_id", "specificationIds", "specIds", "split", "map", "id", "specModel", "specs", "is_delete", "spec", "value", "addAction", "data", "post", "goods_id", "product_id", "points_price", "existing", "find", "think", "isEmpty", "goods", "productModel", "product", "insertData", "cash_price", "parseFloat", "stock_limit", "daily_limit", "user_limit", "sort", "is_hot", "start_time", "end_time", "description", "created_at", "Date", "updated_at", "result", "add", "detailAction", "detail", "updateAction", "updateData", "undefined", "update", "deleteAction", "batchStatusAction", "ids", "Array", "isArray", "statisticsAction", "pointsOrdersModel", "totalGoods", "onlineGoods", "totalOrders", "totalPoints", "sum", "statistics", "total_goods", "online_goods", "offline_goods", "total_orders", "total_points", "categoriesAction", "pointsCategoryModel", "categories", "categoryModel", "is_show", "goodsProductsAction", "goodsId", "specificationModel", "products", "JSON", "parse", "e", "updateStatusAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,YAAN,GAAmB;AAAA;;AAAA;AACjBC,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAMC,QAAQ,MAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAME,SAAS,MAAKF,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,cAAMG,cAAc,MAAKH,GAAL,CAAS,aAAT,KAA2B,EAA/C;AACA,cAAMI,UAAU,MAAKJ,GAAL,CAAS,SAAT,KAAuB,EAAvC;;AAEAH,gBAAQC,GAAR,CAAY,OAAZ,EAAqB,EAAEC,IAAF,EAAQE,KAAR,EAAeC,MAAf,EAAuBC,WAAvB,EAAoCC,OAApC,EAArB;;AAEA;AACA,cAAMC,QAAQ,EAAE,gBAAgB,CAAlB,EAAd;;AAEA,YAAIH,WAAW,EAAf,EAAmB;AACjBG,gBAAM,WAAN,IAAqBC,SAASJ,MAAT,CAArB;AACD;;AAED,YAAIC,gBAAgB,EAApB,EAAwB;AACtBE,gBAAM,eAAN,IAAyBC,SAASH,WAAT,CAAzB;AACD;;AAED,YAAIC,YAAY,EAAhB,EAAoB;AAClBC,gBAAM,QAAN,IAAkB,CAAC,MAAD,EAAU,IAAGD,OAAQ,GAArB,CAAlB;AACD;;AAED;AACA,cAAMG,mBAAmB,MAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMC,OAAO,MAAMF,iBAAiBG,KAAjB,CAAuB,IAAvB,EAChBC,IADgB,CACX,uCADW,EAEhBA,IAFgB,CAEX,2CAFW,EAGhBN,KAHgB,CAGVA,KAHU,EAIhBO,KAJgB,CAIV,gKAJU,EAKhBC,KALgB,CAKV,0BALU,EAMhBd,IANgB,CAMXA,IANW,EAMLE,KANK,EAOhBa,MAPgB,EAAnB;;AASA;AACA,cAAMC,QAAQ,MAAMR,iBAAiBG,KAAjB,CAAuB,IAAvB,EACjBC,IADiB,CACZ,uCADY,EAEjBA,IAFiB,CAEZ,2CAFY,EAGjBN,KAHiB,CAGXA,KAHW,EAIjBW,KAJiB,EAApB;;AAMA;AACA,aAAK,IAAIC,IAAT,IAAiBR,IAAjB,EAAuB;AACrB,cAAIQ,KAAKC,uBAAT,EAAkC;AAChC;AACA,kBAAMC,WAAW,MAAM,MAAKC,oBAAL,CAA0BH,KAAKC,uBAA/B,CAAvB;AACAD,iBAAKI,kBAAL,GAA0BF,QAA1B;AACD;AACF;;AAEDtB,gBAAQC,GAAR,CAAa,OAAMW,KAAKa,MAAO,WAAUP,KAAM,IAA/C;;AAEA,eAAO,MAAKQ,OAAL,CAAa;AAClBd,gBAAMA,IADY;AAElBM,iBAAOA,KAFW;AAGlBhB,gBAAMO,SAASP,IAAT,CAHY;AAIlBE,iBAAOK,SAASL,KAAT;AAJW,SAAb,CAAP;AAOD,OA5DD,CA4DE,OAAOuB,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;AAlEgB;AAmElB;;AAED;;;;AAIMC,sBAAN,GAA6B;AAAA;;AAAA;AAC3B7B,cAAQC,GAAR,CAAY,oBAAZ;;AAEA,UAAI;AACF,cAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAMI,UAAU,OAAKJ,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,cAAMG,cAAc,OAAKH,GAAL,CAAS,aAAT,KAA2B,EAA/C;;AAEA;AACA,cAAMK,QAAQ;AACZ,yBAAe,CADH;AAEZ,0BAAgB,CAFJ;AAGZ,yBAAe,CAHH;AAIZ,0BAAgB;AAJJ,SAAd;;AAOA,YAAID,YAAY,EAAhB,EAAoB;AAClBC,gBAAM,QAAN,IAAkB,CAAC,MAAD,EAAU,IAAGD,OAAQ,GAArB,CAAlB;AACD;;AAED,YAAID,gBAAgB,EAApB,EAAwB;AACtBE,gBAAM,eAAN,IAAyBC,SAASH,WAAT,CAAzB;AACD;;AAED;AACA,cAAMwB,aAAa,OAAKnB,KAAL,CAAW,OAAX,CAAnB;AACA,cAAMC,OAAO,MAAMkB,WAAWjB,KAAX,CAAiB,GAAjB,EAChBC,IADgB,CACX,wCADW,EAEhBiB,QAFgB,CAEP,sEAFO,EAGhBvB,KAHgB,CAGVA,KAHU,EAIhBO,KAJgB,CAIV,uNAJU,EAKhBC,KALgB,CAKV,8BALU,EAMhBd,IANgB,CAMXA,IANW,EAMLE,KANK,EAOhBa,MAPgB,EAAnB;;AASA;AACA,cAAMC,QAAQ,MAAMY,WAAWjB,KAAX,CAAiB,GAAjB,EACjBC,IADiB,CACZ,wCADY,EAEjBiB,QAFiB,CAER,sEAFQ,EAGjBvB,KAHiB,CAGXA,KAHW,EAIjBW,KAJiB,EAApB;;AAMA;AACA,aAAK,IAAIC,IAAT,IAAiBR,IAAjB,EAAuB;AACrB,cAAIQ,KAAKC,uBAAT,EAAkC;AAChC,kBAAMC,WAAW,MAAM,OAAKC,oBAAL,CAA0BH,KAAKC,uBAA/B,CAAvB;AACAD,iBAAKI,kBAAL,GAA0BF,QAA1B;AACD;;AAED;AACAF,eAAKY,eAAL,GAAuB,CAAC,CAACZ,KAAKa,eAA9B;AACD;;AAEDjC,gBAAQC,GAAR,CAAa,OAAMW,KAAKa,MAAO,aAAYP,KAAM,IAAjD;;AAEA,eAAO,OAAKQ,OAAL,CAAa;AAClBd,gBAAMA,IADY;AAElBM,iBAAOA,KAFW;AAGlBhB,gBAAMO,SAASP,IAAT,CAHY;AAIlBE,iBAAOK,SAASL,KAAT;AAJW,SAAb,CAAP;AAOD,OA5DD,CA4DE,OAAOuB,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACD;AAlE0B;AAmE5B;;AAED;;;AAGML,sBAAN,CAA2BW,gBAA3B,EAA6C;AAAA;;AAAA;AAC3C,UAAI,CAACA,gBAAL,EAAuB,OAAO,EAAP;;AAEvB,UAAI;AACF,cAAMC,UAAUD,iBAAiBE,KAAjB,CAAuB,GAAvB,EAA4BC,GAA5B,CAAgC;AAAA,iBAAM5B,SAAS6B,EAAT,CAAN;AAAA,SAAhC,CAAhB;AACA,cAAMC,YAAY,OAAK5B,KAAL,CAAW,qBAAX,CAAlB;AACA,cAAM6B,QAAQ,MAAMD,UAAU/B,KAAV,CAAgB;AAClC8B,cAAI,CAAC,IAAD,EAAOH,OAAP,CAD8B;AAElCM,qBAAW;AAFuB,SAAhB,EAGjBxB,MAHiB,EAApB;;AAKA,eAAOuB,MAAMH,GAAN,CAAU;AAAA,iBAAQK,KAAKC,KAAb;AAAA,SAAV,EAA8B7B,IAA9B,CAAmC,KAAnC,CAAP;AACD,OATD,CASE,OAAOa,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,EAAP;AACD;AAf0C;AAgB5C;;AAED;;;;AAIMiB,WAAN,GAAkB;AAAA;;AAAA;AAChB5C,cAAQC,GAAR,CAAY,gBAAZ;;AAEA,UAAI;AACF,cAAM4C,OAAO,OAAKC,IAAL,EAAb;AACA9C,gBAAQC,GAAR,CAAY,SAAZ,EAAuB4C,IAAvB;;AAEA;AACA,YAAI,CAACA,KAAKE,QAAN,IAAkB,CAACF,KAAKG,UAAxB,IAAsC,CAACH,KAAKI,YAAhD,EAA8D;AAC5D,iBAAO,OAAKrB,IAAL,CAAU,GAAV,EAAe,oBAAf,CAAP;AACD;;AAED;AACA,cAAMlB,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMuC,WAAW,MAAMxC,iBAAiBF,KAAjB,CAAuB;AAC5CwC,sBAAYH,KAAKG,UAD2B;AAE5CP,qBAAW;AAFiC,SAAvB,EAGpBU,IAHoB,EAAvB;;AAKA,YAAI,CAACC,MAAMC,OAAN,CAAcH,QAAd,CAAL,EAA8B;AAC5B,iBAAO,OAAKtB,IAAL,CAAU,GAAV,EAAe,gBAAf,CAAP;AACD;;AAED;AACA,cAAME,aAAa,OAAKnB,KAAL,CAAW,OAAX,CAAnB;AACA,cAAM2C,QAAQ,MAAMxB,WAAWtB,KAAX,CAAiB;AACnC8B,cAAIO,KAAKE,QAD0B;AAEnCN,qBAAW;AAFwB,SAAjB,EAGjBU,IAHiB,EAApB;;AAKA,YAAIC,MAAMC,OAAN,CAAcC,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAK1B,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACD;;AAED,cAAM2B,eAAe,OAAK5C,KAAL,CAAW,SAAX,CAArB;AACA,cAAM6C,UAAU,MAAMD,aAAa/C,KAAb,CAAmB;AACvC8B,cAAIO,KAAKG,UAD8B;AAEvCD,oBAAUF,KAAKE,QAFwB;AAGvCN,qBAAW;AAH4B,SAAnB,EAInBU,IAJmB,EAAtB;;AAMA,YAAIC,MAAMC,OAAN,CAAcG,OAAd,CAAJ,EAA4B;AAC1B,iBAAO,OAAK5B,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;;AAED;AACA,cAAM6B,aAAa;AACjBV,oBAAUtC,SAASoC,KAAKE,QAAd,CADO;AAEjBC,sBAAYvC,SAASoC,KAAKG,UAAd,CAFK;AAGjBC,wBAAcxC,SAASoC,KAAKI,YAAd,CAHG;AAIjBS,sBAAYC,WAAWd,KAAKa,UAAL,IAAmB,CAA9B,CAJK;AAKjBE,uBAAanD,SAASoC,KAAKe,WAAL,IAAoB,CAA7B,CALI;AAMjBC,uBAAapD,SAASoC,KAAKgB,WAAL,IAAoB,CAA7B,CANI;AAOjBC,sBAAYrD,SAASoC,KAAKiB,UAAL,IAAmB,CAA5B,CAPK;AAQjBC,gBAAMtD,SAASoC,KAAKkB,IAAL,IAAa,CAAtB,CARW;AASjB1D,kBAAQI,SAASoC,KAAKxC,MAAL,IAAe,CAAxB,CATS;AAUjB2D,kBAAQvD,SAASoC,KAAKmB,MAAL,IAAe,CAAxB,CAVS;AAWjBC,sBAAYpB,KAAKoB,UAAL,IAAmB,IAXd;AAYjBC,oBAAUrB,KAAKqB,QAAL,IAAiB,IAZV;AAajBC,uBAAatB,KAAKsB,WAAL,IAAoB,EAbhB;AAcjBC,sBAAY,IAAIC,IAAJ,EAdK;AAejBC,sBAAY,IAAID,IAAJ;AAfK,SAAnB;;AAkBA,cAAME,SAAS,MAAM7D,iBAAiB8D,GAAjB,CAAqBf,UAArB,CAArB;;AAEA,YAAIc,MAAJ,EAAY;AACVvE,kBAAQC,GAAR,CAAY,cAAZ,EAA4BsE,MAA5B;AACA,iBAAO,OAAK7C,OAAL,CAAa,EAAEY,IAAIiC,MAAN,EAAb,EAA6B,MAA7B,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,OAAK3C,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACD;AAEF,OAtED,CAsEE,OAAOD,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA5Ee;AA6EjB;;AAED;;;;AAIM6C,cAAN,GAAqB;AAAA;;AAAA;AACnBzE,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAMqC,KAAK,OAAKnC,GAAL,CAAS,IAAT,CAAX;;AAEA,YAAI,CAACmC,EAAL,EAAS;AACP,iBAAO,OAAKV,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED;AACA,cAAMlB,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAM+D,SAAS,MAAMhE,iBAAiBG,KAAjB,CAAuB,IAAvB,EAClBC,IADkB,CACb,uCADa,EAElBA,IAFkB,CAEb,2CAFa,EAGlBN,KAHkB,CAGZ;AACL,mBAAS8B,EADJ;AAEL,0BAAgB;AAFX,SAHY,EAOlBvB,KAPkB,CAOZ,8KAPY,EAQlBoC,IARkB,EAArB;;AAUA,YAAIC,MAAMC,OAAN,CAAcqB,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAK9C,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;;AAED;AACA,YAAI8C,OAAOrD,uBAAX,EAAoC;AAClC,gBAAMC,WAAW,MAAM,OAAKC,oBAAL,CAA0BmD,OAAOrD,uBAAjC,CAAvB;AACAqD,iBAAOlD,kBAAP,GAA4BF,QAA5B;AACD;;AAEDtB,gBAAQC,GAAR,CAAY,SAAZ,EAAuByE,MAAvB;;AAEA,eAAO,OAAKhD,OAAL,CAAagD,MAAb,CAAP;AAED,OAjCD,CAiCE,OAAO/C,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;AAvCkB;AAwCpB;;AAED;;;;AAIM+C,cAAN,GAAqB;AAAA;;AAAA;AACnB3E,cAAQC,GAAR,CAAY,gBAAZ;;AAEA,UAAI;AACF,cAAMqC,KAAK,OAAKnC,GAAL,CAAS,IAAT,CAAX;AACA,cAAM0C,OAAO,OAAKC,IAAL,EAAb;;AAEA,YAAI,CAACR,EAAL,EAAS;AACP,iBAAO,OAAKV,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED5B,gBAAQC,GAAR,CAAY,OAAZ,EAAqB4C,IAArB;;AAEA;AACA,cAAMnC,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMuC,WAAW,MAAMxC,iBAAiBF,KAAjB,CAAuB;AAC5C8B,cAAIA,EADwC;AAE5CG,qBAAW;AAFiC,SAAvB,EAGpBU,IAHoB,EAAvB;;AAKA,YAAIC,MAAMC,OAAN,CAAcH,QAAd,CAAJ,EAA6B;AAC3B,iBAAO,OAAKtB,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;;AAED;AACA,cAAMgD,aAAa;AACjBN,sBAAY,IAAID,IAAJ;AADK,SAAnB;;AAIA;AACA,YAAIxB,KAAKI,YAAL,KAAsB4B,SAA1B,EAAqC;AACnCD,qBAAW3B,YAAX,GAA0BxC,SAASoC,KAAKI,YAAd,CAA1B;AACD;AACD,YAAIJ,KAAKa,UAAL,KAAoBmB,SAAxB,EAAmC;AACjCD,qBAAWlB,UAAX,GAAwBC,WAAWd,KAAKa,UAAhB,CAAxB;AACD;AACD,YAAIb,KAAKe,WAAL,KAAqBiB,SAAzB,EAAoC;AAClCD,qBAAWhB,WAAX,GAAyBnD,SAASoC,KAAKe,WAAd,CAAzB;AACD;AACD,YAAIf,KAAKgB,WAAL,KAAqBgB,SAAzB,EAAoC;AAClCD,qBAAWf,WAAX,GAAyBpD,SAASoC,KAAKgB,WAAd,CAAzB;AACD;AACD,YAAIhB,KAAKiB,UAAL,KAAoBe,SAAxB,EAAmC;AACjCD,qBAAWd,UAAX,GAAwBrD,SAASoC,KAAKiB,UAAd,CAAxB;AACD;AACD,YAAIjB,KAAKkB,IAAL,KAAcc,SAAlB,EAA6B;AAC3BD,qBAAWb,IAAX,GAAkBtD,SAASoC,KAAKkB,IAAd,CAAlB;AACD;AACD,YAAIlB,KAAKxC,MAAL,KAAgBwE,SAApB,EAA+B;AAC7BD,qBAAWvE,MAAX,GAAoBI,SAASoC,KAAKxC,MAAd,CAApB;AACD;AACD,YAAIwC,KAAKmB,MAAL,KAAgBa,SAApB,EAA+B;AAC7BD,qBAAWZ,MAAX,GAAoBvD,SAASoC,KAAKmB,MAAd,CAApB;AACD;AACD,YAAInB,KAAKoB,UAAL,KAAoBY,SAAxB,EAAmC;AACjCD,qBAAWX,UAAX,GAAwBpB,KAAKoB,UAAL,IAAmB,IAA3C;AACD;AACD,YAAIpB,KAAKqB,QAAL,KAAkBW,SAAtB,EAAiC;AAC/BD,qBAAWV,QAAX,GAAsBrB,KAAKqB,QAAL,IAAiB,IAAvC;AACD;AACD,YAAIrB,KAAKsB,WAAL,KAAqBU,SAAzB,EAAoC;AAClCD,qBAAWT,WAAX,GAAyBtB,KAAKsB,WAA9B;AACD;;AAED,cAAMI,SAAS,MAAM7D,iBAAiBF,KAAjB,CAAuB,EAAE8B,IAAIA,EAAN,EAAvB,EAAmCwC,MAAnC,CAA0CF,UAA1C,CAArB;;AAEA,YAAIL,MAAJ,EAAY;AACVvE,kBAAQC,GAAR,CAAY,UAAZ;AACA,iBAAO,OAAKyB,OAAL,CAAa,EAAb,EAAiB,MAAjB,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,OAAKE,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACD;AAEF,OAtED,CAsEE,OAAOD,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA5EkB;AA6EpB;;AAED;;;;AAIMmD,cAAN,GAAqB;AAAA;;AAAA;AACnB/E,cAAQC,GAAR,CAAY,gBAAZ;;AAEA,UAAI;AACF,cAAMqC,KAAK,OAAKnC,GAAL,CAAS,IAAT,CAAX;;AAEA,YAAI,CAACmC,EAAL,EAAS;AACP,iBAAO,OAAKV,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED;AACA,cAAMlB,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAMuC,WAAW,MAAMxC,iBAAiBF,KAAjB,CAAuB;AAC5C8B,cAAIA,EADwC;AAE5CG,qBAAW;AAFiC,SAAvB,EAGpBU,IAHoB,EAAvB;;AAKA,YAAIC,MAAMC,OAAN,CAAcH,QAAd,CAAJ,EAA6B;AAC3B,iBAAO,OAAKtB,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;;AAED;AACA,cAAM2C,SAAS,MAAM7D,iBAAiBF,KAAjB,CAAuB,EAAE8B,IAAIA,EAAN,EAAvB,EAAmCwC,MAAnC,CAA0C;AAC7DrC,qBAAW,CADkD;AAE7D6B,sBAAY,IAAID,IAAJ;AAFiD,SAA1C,CAArB;;AAKA,YAAIE,MAAJ,EAAY;AACVvE,kBAAQC,GAAR,CAAY,UAAZ;AACA,iBAAO,OAAKyB,OAAL,CAAa,EAAb,EAAiB,MAAjB,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,OAAKE,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACD;AAEF,OA/BD,CA+BE,OAAOD,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AArCkB;AAsCpB;;AAED;;;;AAIMoD,mBAAN,GAA0B;AAAA;;AAAA;AACxBhF,cAAQC,GAAR,CAAY,oBAAZ;;AAEA,UAAI;AACF,cAAM4C,OAAO,OAAKC,IAAL,EAAb;AACA,cAAM,EAAEmC,GAAF,EAAO5E,MAAP,KAAkBwC,IAAxB;;AAEA,YAAI,CAACoC,GAAD,IAAQ,CAACC,MAAMC,OAAN,CAAcF,GAAd,CAAT,IAA+BA,IAAIxD,MAAJ,KAAe,CAAlD,EAAqD;AACnD,iBAAO,OAAKG,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACD;;AAED,YAAIvB,WAAWwE,SAAf,EAA0B;AACxB,iBAAO,OAAKjD,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACD;;AAED5B,gBAAQC,GAAR,CAAY,OAAZ,EAAqB,EAAEgF,GAAF,EAAO5E,MAAP,EAArB;;AAEA,cAAMK,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAM4D,SAAS,MAAM7D,iBAAiBF,KAAjB,CAAuB;AAC1C8B,cAAI,CAAC,IAAD,EAAO2C,GAAP,CADsC;AAE1CxC,qBAAW;AAF+B,SAAvB,EAGlBqC,MAHkB,CAGX;AACRzE,kBAAQI,SAASJ,MAAT,CADA;AAERiE,sBAAY,IAAID,IAAJ;AAFJ,SAHW,CAArB;;AAQA,YAAIE,MAAJ,EAAY;AACVvE,kBAAQC,GAAR,CAAY,QAAZ;AACA,iBAAO,OAAKyB,OAAL,CAAa,EAAb,EAAiB,QAAjB,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,OAAKE,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AAEF,OA9BD,CA8BE,OAAOD,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AApCuB;AAqCzB;;AAED;;;;AAIMwD,kBAAN,GAAyB;AAAA;;AAAA;AACvBpF,cAAQC,GAAR,CAAY,oBAAZ;;AAEA,UAAI;AACF,cAAMS,mBAAmB,OAAKC,KAAL,CAAW,cAAX,CAAzB;AACA,cAAM0E,oBAAoB,OAAK1E,KAAL,CAAW,eAAX,CAA1B;;AAEA;AACA,cAAM2E,aAAa,MAAM5E,iBAAiBF,KAAjB,CAAuB,EAAEiC,WAAW,CAAb,EAAvB,EAAyCtB,KAAzC,EAAzB;AACA,cAAMoE,cAAc,MAAM7E,iBAAiBF,KAAjB,CAAuB,EAAEiC,WAAW,CAAb,EAAgBpC,QAAQ,CAAxB,EAAvB,EAAoDc,KAApD,EAA1B;;AAEA;AACA,cAAMqE,cAAc,MAAMH,kBAAkB7E,KAAlB,CAAwB,EAAEiC,WAAW,CAAb,EAAxB,EAA0CtB,KAA1C,EAA1B;AACA,cAAMsE,cAAc,MAAMJ,kBAAkB7E,KAAlB,CAAwB,EAAEiC,WAAW,CAAb,EAAxB,EAA0CiD,GAA1C,CAA8C,cAA9C,CAA1B;;AAEA,cAAMC,aAAa;AACjBC,uBAAaN,UADI;AAEjBO,wBAAcN,WAFG;AAGjBO,yBAAeR,aAAaC,WAHX;AAIjBQ,wBAAcP,WAJG;AAKjBQ,wBAAcP,eAAe;AALZ,SAAnB;;AAQAzF,gBAAQC,GAAR,CAAY,OAAZ,EAAqB0F,UAArB;;AAEA,eAAO,OAAKjE,OAAL,CAAaiE,UAAb,CAAP;AAED,OAxBD,CAwBE,OAAOhE,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA9BsB;AA+BxB;;AAED;;;;AAIMqE,kBAAN,GAAyB;AAAA;;AAAA;AACvBjG,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF;AACA,cAAMiG,sBAAsB,QAAKvF,KAAL,CAAW,mBAAX,CAA5B;AACA,YAAIwF,aAAa,MAAMD,oBAAoB1F,KAApB,CAA0B;AAC/CH,kBAAQ,CADuC;AAE/CoC,qBAAW;AAFoC,SAA1B,EAGpB1B,KAHoB,CAGd,UAHc,EAGFC,KAHE,CAGI,mBAHJ,EAGyBC,MAHzB,EAAvB;;AAKA;AACA,YAAI,CAACkF,UAAD,IAAeA,WAAW1E,MAAX,KAAsB,CAAzC,EAA4C;AAC1CzB,kBAAQC,GAAR,CAAY,qBAAZ;AACA,gBAAMmG,gBAAgB,QAAKzF,KAAL,CAAW,UAAX,CAAtB;AACAwF,uBAAa,MAAMC,cAAc5F,KAAd,CAAoB;AACrC6F,qBAAS;AAD4B,WAApB,EAEhBtF,KAFgB,CAEV,qBAFU,EAEaC,KAFb,CAEmB,wBAFnB,EAE6CC,MAF7C,EAAnB;AAGD;;AAEDjB,gBAAQC,GAAR,CAAY,OAAZ,EAAqBkG,UAArB;;AAEA,eAAO,QAAKzE,OAAL,CAAayE,UAAb,CAAP;AAED,OArBD,CAqBE,OAAOxE,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,QAAKC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AA3BsB;AA4BxB;;AAED;;;;AAIM0E,qBAAN,GAA4B;AAAA;;AAAA;AAC1BtG,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAMsG,UAAU,QAAKpG,GAAL,CAAS,UAAT,CAAhB;;AAEA,YAAI,CAACoG,OAAL,EAAc;AACZ,iBAAO,QAAK3E,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;;AAED5B,gBAAQC,GAAR,CAAY,OAAZ,EAAqBsG,OAArB;;AAEA,cAAMhD,eAAe,QAAK5C,KAAL,CAAW,SAAX,CAArB;AACA,cAAM6F,qBAAqB,QAAK7F,KAAL,CAAW,eAAX,CAA3B;;AAEA;AACA,cAAM8F,WAAW,MAAMlD,aAAa/C,KAAb,CAAmB;AACxCuC,oBAAUwD,OAD8B;AAExC9D,qBAAW;AAF6B,SAAnB,EAGpB1B,KAHoB,CAGd,mEAHc,EAGuDE,MAHvD,EAAvB;;AAKA;AACA,aAAK,IAAIuC,OAAT,IAAoBiD,QAApB,EAA8B;AAC5B,cAAIjD,QAAQnC,uBAAZ,EAAqC;AACnC,gBAAI;AACF,oBAAMc,UAAUuE,KAAKC,KAAL,CAAWnD,QAAQnC,uBAAnB,CAAhB;AACA,kBAAI6D,MAAMC,OAAN,CAAchD,OAAd,KAA0BA,QAAQV,MAAR,GAAiB,CAA/C,EAAkD;AAChD,sBAAMe,QAAQ,MAAMgE,mBAAmBhG,KAAnB,CAAyB;AAC3C8B,sBAAI,CAAC,IAAD,EAAOH,OAAP;AADuC,iBAAzB,EAEjBpB,KAFiB,CAEX,WAFW,EAEEE,MAFF,EAApB;;AAIAuC,wBAAQhC,kBAAR,GAA6BgB,MAAMH,GAAN,CAAU;AAAA,yBAAQK,KAAKC,KAAb;AAAA,iBAAV,EAA8B7B,IAA9B,CAAmC,KAAnC,CAA7B;AACD,eAND,MAMO;AACL0C,wBAAQhC,kBAAR,GAA6B,MAA7B;AACD;AACF,aAXD,CAWE,OAAOoF,CAAP,EAAU;AACVpD,sBAAQhC,kBAAR,GAA6B,MAA7B;AACD;AACF,WAfD,MAeO;AACLgC,oBAAQhC,kBAAR,GAA6B,MAA7B;AACD;AACF;;AAEDxB,gBAAQC,GAAR,CAAY,OAAZ,EAAqBwG,QAArB;;AAEA,eAAO,QAAK/E,OAAL,CAAa+E,QAAb,CAAP;AAED,OA5CD,CA4CE,OAAO9E,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,QAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AAlDyB;AAmD3B;;AAED;;;;AAIMiF,oBAAN,GAA2B;AAAA;;AAAA;AACzB7G,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF,cAAM4C,OAAO,QAAKC,IAAL,EAAb;AACA,cAAM,EAAER,EAAF,EAAMjC,MAAN,KAAiBwC,IAAvB;;AAEA,YAAI,CAACP,EAAL,EAAS;AACP,iBAAO,QAAKV,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED,YAAIvB,WAAWwE,SAAf,EAA0B;AACxB,iBAAO,QAAKjD,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;;AAED5B,gBAAQC,GAAR,CAAY,OAAZ,EAAqB,EAAEqC,EAAF,EAAMjC,MAAN,EAArB;;AAEA,cAAMK,mBAAmB,QAAKC,KAAL,CAAW,cAAX,CAAzB;;AAEA;AACA,cAAMuC,WAAW,MAAMxC,iBAAiBF,KAAjB,CAAuB;AAC5C8B,cAAIA,EADwC;AAE5CG,qBAAW;AAFiC,SAAvB,EAGpBU,IAHoB,EAAvB;;AAKA,YAAIC,MAAMC,OAAN,CAAcH,QAAd,CAAJ,EAA6B;AAC3B,iBAAO,QAAKtB,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;;AAED;AACA,cAAM2C,SAAS,MAAM7D,iBAAiBF,KAAjB,CAAuB,EAAE8B,IAAIA,EAAN,EAAvB,EAAmCwC,MAAnC,CAA0C;AAC7DzE,kBAAQI,SAASJ,MAAT,CADqD;AAE7DiE,sBAAY,IAAID,IAAJ;AAFiD,SAA1C,CAArB;;AAKA,YAAIE,MAAJ,EAAY;AACVvE,kBAAQC,GAAR,CAAY,QAAZ;AACA,iBAAO,QAAKyB,OAAL,CAAa,EAAb,EAAiB,QAAjB,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,QAAKE,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AAEF,OAvCD,CAuCE,OAAOD,KAAP,EAAc;AACd3B,gBAAQ2B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,QAAKC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AA7CwB;AA8C1B;AAtoBiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\points-goods.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n  \n  /**\n   * 获取积分商品列表\n   * GET /admin/points-goods/list\n   */\n  async listAction() {\n    console.log('=== 获取积分商品列表 ===');\n    \n    try {\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const status = this.get('status') || '';\n      const category_id = this.get('category_id') || '';\n      const keyword = this.get('keyword') || '';\n      \n      console.log('查询参数:', { page, limit, status, category_id, keyword });\n      \n      // 构建查询条件\n      const where = { 'pg.is_delete': 0 };\n      \n      if (status !== '') {\n        where['pg.status'] = parseInt(status);\n      }\n      \n      if (category_id !== '') {\n        where['g.category_id'] = parseInt(category_id);\n      }\n      \n      if (keyword !== '') {\n        where['g.name'] = ['like', `%${keyword}%`];\n      }\n      \n      // 联表查询积分商品列表（关联商品表和规格表）\n      const pointsGoodsModel = this.model('points_goods');\n      const list = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where(where)\n        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')\n        .order('pg.sort DESC, pg.id DESC')\n        .page(page, limit)\n        .select();\n      \n      // 获取总数\n      const total = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where(where)\n        .count();\n      \n      // 处理规格信息\n      for (let item of list) {\n        if (item.goods_specification_ids) {\n          // 获取规格详细信息\n          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);\n          item.specification_info = specInfo;\n        }\n      }\n      \n      console.log(`查询到 ${list.length} 条记录，总计 ${total} 条`);\n      \n      return this.success({\n        list: list,\n        total: total,\n        page: parseInt(page),\n        limit: parseInt(limit)\n      });\n      \n    } catch (error) {\n      console.error('获取积分商品列表失败:', error);\n      return this.fail(500, '获取积分商品列表失败');\n    }\n  }\n\n  /**\n   * 获取可选择的商品列表（用于添加积分商品）\n   * GET /admin/points-goods/available-goods\n   */\n  async availableGoodsAction() {\n    console.log('=== 获取可选择的商品列表 ===');\n    \n    try {\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const keyword = this.get('keyword') || '';\n      const category_id = this.get('category_id') || '';\n      \n      // 构建查询条件\n      const where = { \n        'g.is_delete': 0,\n        'g.is_on_sale': 1,\n        'p.is_delete': 0,\n        'p.is_on_sale': 1\n      };\n      \n      if (keyword !== '') {\n        where['g.name'] = ['like', `%${keyword}%`];\n      }\n      \n      if (category_id !== '') {\n        where['g.category_id'] = parseInt(category_id);\n      }\n      \n      // 查询商品及其规格\n      const goodsModel = this.model('goods');\n      const list = await goodsModel.alias('g')\n        .join('hiolabs_product p ON g.id = p.goods_id')\n        .leftJoin('hiolabs_points_goods pg ON p.id = pg.product_id AND pg.is_delete = 0')\n        .where(where)\n        .field('g.id as goods_id, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.category_id, p.id as product_id, p.goods_specification_ids, p.retail_price, p.goods_number as stock, pg.id as points_goods_id')\n        .order('g.sort_order DESC, g.id DESC')\n        .page(page, limit)\n        .select();\n      \n      // 获取总数\n      const total = await goodsModel.alias('g')\n        .join('hiolabs_product p ON g.id = p.goods_id')\n        .leftJoin('hiolabs_points_goods pg ON p.id = pg.product_id AND pg.is_delete = 0')\n        .where(where)\n        .count();\n      \n      // 处理规格信息和状态\n      for (let item of list) {\n        if (item.goods_specification_ids) {\n          const specInfo = await this.getSpecificationInfo(item.goods_specification_ids);\n          item.specification_info = specInfo;\n        }\n        \n        // 标记是否已添加到积分商城\n        item.is_points_goods = !!item.points_goods_id;\n      }\n      \n      console.log(`查询到 ${list.length} 条可选商品，总计 ${total} 条`);\n      \n      return this.success({\n        list: list,\n        total: total,\n        page: parseInt(page),\n        limit: parseInt(limit)\n      });\n      \n    } catch (error) {\n      console.error('获取可选择商品列表失败:', error);\n      return this.fail(500, '获取可选择商品列表失败');\n    }\n  }\n\n  /**\n   * 获取规格信息\n   */\n  async getSpecificationInfo(specificationIds) {\n    if (!specificationIds) return '';\n    \n    try {\n      const specIds = specificationIds.split(',').map(id => parseInt(id));\n      const specModel = this.model('goods_specification');\n      const specs = await specModel.where({\n        id: ['IN', specIds],\n        is_delete: 0\n      }).select();\n      \n      return specs.map(spec => spec.value).join(' / ');\n    } catch (error) {\n      console.error('获取规格信息失败:', error);\n      return '';\n    }\n  }\n\n  /**\n   * 添加积分商品\n   * POST /admin/points-goods/add\n   */\n  async addAction() {\n    console.log('=== 添加积分商品 ===');\n    \n    try {\n      const data = this.post();\n      console.log('接收到的数据:', data);\n      \n      // 验证必填字段\n      if (!data.goods_id || !data.product_id || !data.points_price) {\n        return this.fail(400, '商品ID、规格ID和积分价格不能为空');\n      }\n      \n      // 检查该规格是否已经添加\n      const pointsGoodsModel = this.model('points_goods');\n      const existing = await pointsGoodsModel.where({\n        product_id: data.product_id,\n        is_delete: 0\n      }).find();\n      \n      if (!think.isEmpty(existing)) {\n        return this.fail(400, '该商品规格已经添加到积分商城');\n      }\n      \n      // 验证商品和规格是否存在\n      const goodsModel = this.model('goods');\n      const goods = await goodsModel.where({\n        id: data.goods_id,\n        is_delete: 0\n      }).find();\n      \n      if (think.isEmpty(goods)) {\n        return this.fail(400, '商品不存在');\n      }\n      \n      const productModel = this.model('product');\n      const product = await productModel.where({\n        id: data.product_id,\n        goods_id: data.goods_id,\n        is_delete: 0\n      }).find();\n      \n      if (think.isEmpty(product)) {\n        return this.fail(400, '商品规格不存在');\n      }\n      \n      // 准备插入数据\n      const insertData = {\n        goods_id: parseInt(data.goods_id),\n        product_id: parseInt(data.product_id),\n        points_price: parseInt(data.points_price),\n        cash_price: parseFloat(data.cash_price || 0),\n        stock_limit: parseInt(data.stock_limit || 0),\n        daily_limit: parseInt(data.daily_limit || 0),\n        user_limit: parseInt(data.user_limit || 0),\n        sort: parseInt(data.sort || 0),\n        status: parseInt(data.status || 1),\n        is_hot: parseInt(data.is_hot || 0),\n        start_time: data.start_time || null,\n        end_time: data.end_time || null,\n        description: data.description || '',\n        created_at: new Date(),\n        updated_at: new Date()\n      };\n      \n      const result = await pointsGoodsModel.add(insertData);\n      \n      if (result) {\n        console.log('积分商品添加成功，ID:', result);\n        return this.success({ id: result }, '添加成功');\n      } else {\n        return this.fail(500, '添加失败');\n      }\n      \n    } catch (error) {\n      console.error('添加积分商品失败:', error);\n      return this.fail(500, '添加积分商品失败');\n    }\n  }\n\n  /**\n   * 获取积分商品详情\n   * GET /admin/points-goods/detail/:id\n   */\n  async detailAction() {\n    console.log('=== 获取积分商品详情 ===');\n    \n    try {\n      const id = this.get('id');\n      \n      if (!id) {\n        return this.fail(400, '积分商品ID不能为空');\n      }\n      \n      // 联表查询积分商品详情\n      const pointsGoodsModel = this.model('points_goods');\n      const detail = await pointsGoodsModel.alias('pg')\n        .join('hiolabs_goods g ON pg.goods_id = g.id')\n        .join('hiolabs_product p ON pg.product_id = p.id')\n        .where({\n          'pg.id': id,\n          'pg.is_delete': 0\n        })\n        .field('pg.*, g.name as goods_name, g.list_pic_url as goods_image, g.goods_brief, g.goods_desc, p.goods_specification_ids, p.retail_price as original_price, p.goods_number as stock')\n        .find();\n      \n      if (think.isEmpty(detail)) {\n        return this.fail(404, '积分商品不存在');\n      }\n      \n      // 获取规格信息\n      if (detail.goods_specification_ids) {\n        const specInfo = await this.getSpecificationInfo(detail.goods_specification_ids);\n        detail.specification_info = specInfo;\n      }\n      \n      console.log('积分商品详情:', detail);\n      \n      return this.success(detail);\n\n    } catch (error) {\n      console.error('获取积分商品详情失败:', error);\n      return this.fail(500, '获取积分商品详情失败');\n    }\n  }\n\n  /**\n   * 更新积分商品\n   * PUT /admin/points-goods/update/:id\n   */\n  async updateAction() {\n    console.log('=== 更新积分商品 ===');\n\n    try {\n      const id = this.get('id');\n      const data = this.post();\n\n      if (!id) {\n        return this.fail(400, '积分商品ID不能为空');\n      }\n\n      console.log('更新数据:', data);\n\n      // 检查积分商品是否存在\n      const pointsGoodsModel = this.model('points_goods');\n      const existing = await pointsGoodsModel.where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(existing)) {\n        return this.fail(404, '积分商品不存在');\n      }\n\n      // 准备更新数据\n      const updateData = {\n        updated_at: new Date()\n      };\n\n      // 只更新传入的字段\n      if (data.points_price !== undefined) {\n        updateData.points_price = parseInt(data.points_price);\n      }\n      if (data.cash_price !== undefined) {\n        updateData.cash_price = parseFloat(data.cash_price);\n      }\n      if (data.stock_limit !== undefined) {\n        updateData.stock_limit = parseInt(data.stock_limit);\n      }\n      if (data.daily_limit !== undefined) {\n        updateData.daily_limit = parseInt(data.daily_limit);\n      }\n      if (data.user_limit !== undefined) {\n        updateData.user_limit = parseInt(data.user_limit);\n      }\n      if (data.sort !== undefined) {\n        updateData.sort = parseInt(data.sort);\n      }\n      if (data.status !== undefined) {\n        updateData.status = parseInt(data.status);\n      }\n      if (data.is_hot !== undefined) {\n        updateData.is_hot = parseInt(data.is_hot);\n      }\n      if (data.start_time !== undefined) {\n        updateData.start_time = data.start_time || null;\n      }\n      if (data.end_time !== undefined) {\n        updateData.end_time = data.end_time || null;\n      }\n      if (data.description !== undefined) {\n        updateData.description = data.description;\n      }\n\n      const result = await pointsGoodsModel.where({ id: id }).update(updateData);\n\n      if (result) {\n        console.log('积分商品更新成功');\n        return this.success({}, '更新成功');\n      } else {\n        return this.fail(500, '更新失败');\n      }\n\n    } catch (error) {\n      console.error('更新积分商品失败:', error);\n      return this.fail(500, '更新积分商品失败');\n    }\n  }\n\n  /**\n   * 删除积分商品\n   * DELETE /admin/points-goods/delete/:id\n   */\n  async deleteAction() {\n    console.log('=== 删除积分商品 ===');\n\n    try {\n      const id = this.get('id');\n\n      if (!id) {\n        return this.fail(400, '积分商品ID不能为空');\n      }\n\n      // 检查积分商品是否存在\n      const pointsGoodsModel = this.model('points_goods');\n      const existing = await pointsGoodsModel.where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(existing)) {\n        return this.fail(404, '积分商品不存在');\n      }\n\n      // 软删除\n      const result = await pointsGoodsModel.where({ id: id }).update({\n        is_delete: 1,\n        updated_at: new Date()\n      });\n\n      if (result) {\n        console.log('积分商品删除成功');\n        return this.success({}, '删除成功');\n      } else {\n        return this.fail(500, '删除失败');\n      }\n\n    } catch (error) {\n      console.error('删除积分商品失败:', error);\n      return this.fail(500, '删除积分商品失败');\n    }\n  }\n\n  /**\n   * 批量更新状态\n   * POST /admin/points-goods/batch-status\n   */\n  async batchStatusAction() {\n    console.log('=== 批量更新积分商品状态 ===');\n\n    try {\n      const data = this.post();\n      const { ids, status } = data;\n\n      if (!ids || !Array.isArray(ids) || ids.length === 0) {\n        return this.fail(400, '请选择要操作的积分商品');\n      }\n\n      if (status === undefined) {\n        return this.fail(400, '请指定状态');\n      }\n\n      console.log('批量更新:', { ids, status });\n\n      const pointsGoodsModel = this.model('points_goods');\n      const result = await pointsGoodsModel.where({\n        id: ['IN', ids],\n        is_delete: 0\n      }).update({\n        status: parseInt(status),\n        updated_at: new Date()\n      });\n\n      if (result) {\n        console.log('批量更新成功');\n        return this.success({}, '批量更新成功');\n      } else {\n        return this.fail(500, '批量更新失败');\n      }\n\n    } catch (error) {\n      console.error('批量更新积分商品状态失败:', error);\n      return this.fail(500, '批量更新失败');\n    }\n  }\n\n  /**\n   * 获取统计数据\n   * GET /admin/points-goods/statistics\n   */\n  async statisticsAction() {\n    console.log('=== 获取积分商品统计数据 ===');\n\n    try {\n      const pointsGoodsModel = this.model('points_goods');\n      const pointsOrdersModel = this.model('points_orders');\n\n      // 统计积分商品数量\n      const totalGoods = await pointsGoodsModel.where({ is_delete: 0 }).count();\n      const onlineGoods = await pointsGoodsModel.where({ is_delete: 0, status: 1 }).count();\n\n      // 统计兑换数据\n      const totalOrders = await pointsOrdersModel.where({ is_delete: 0 }).count();\n      const totalPoints = await pointsOrdersModel.where({ is_delete: 0 }).sum('total_points');\n\n      const statistics = {\n        total_goods: totalGoods,\n        online_goods: onlineGoods,\n        offline_goods: totalGoods - onlineGoods,\n        total_orders: totalOrders,\n        total_points: totalPoints || 0\n      };\n\n      console.log('统计数据:', statistics);\n\n      return this.success(statistics);\n\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail(500, '获取统计数据失败');\n    }\n  }\n\n  /**\n   * 获取商品分类\n   * GET /admin/points-goods/categories\n   */\n  async categoriesAction() {\n    console.log('=== 获取积分商品分类 ===');\n\n    try {\n      // 先尝试使用专门的积分商品分类表\n      const pointsCategoryModel = this.model('points_categories');\n      let categories = await pointsCategoryModel.where({\n        status: 1,\n        is_delete: 0\n      }).field('id, name').order('sort DESC, id ASC').select();\n\n      // 如果积分商品分类表为空，则使用普通商品分类表\n      if (!categories || categories.length === 0) {\n        console.log('积分商品分类表为空，使用普通商品分类表');\n        const categoryModel = this.model('category');\n        categories = await categoryModel.where({\n          is_show: 1\n        }).field('id, name, parent_id').order('sort_order ASC, id ASC').select();\n      }\n\n      console.log('分类数据:', categories);\n\n      return this.success(categories);\n\n    } catch (error) {\n      console.error('获取分类失败:', error);\n      return this.fail(500, '获取分类失败');\n    }\n  }\n\n  /**\n   * 获取商品的规格列表\n   * GET /admin/points-goods/goods-products\n   */\n  async goodsProductsAction() {\n    console.log('=== 获取商品规格列表 ===');\n\n    try {\n      const goodsId = this.get('goods_id');\n\n      if (!goodsId) {\n        return this.fail(400, '商品ID不能为空');\n      }\n\n      console.log('商品ID:', goodsId);\n\n      const productModel = this.model('product');\n      const specificationModel = this.model('specification');\n\n      // 获取商品的所有规格\n      const products = await productModel.where({\n        goods_id: goodsId,\n        is_delete: 0\n      }).field('id, goods_id, goods_specification_ids, retail_price, goods_number').select();\n\n      // 处理规格信息\n      for (let product of products) {\n        if (product.goods_specification_ids) {\n          try {\n            const specIds = JSON.parse(product.goods_specification_ids);\n            if (Array.isArray(specIds) && specIds.length > 0) {\n              const specs = await specificationModel.where({\n                id: ['IN', specIds]\n              }).field('id, value').select();\n\n              product.specification_info = specs.map(spec => spec.value).join(' / ');\n            } else {\n              product.specification_info = '默认规格';\n            }\n          } catch (e) {\n            product.specification_info = '默认规格';\n          }\n        } else {\n          product.specification_info = '默认规格';\n        }\n      }\n\n      console.log('规格列表:', products);\n\n      return this.success(products);\n\n    } catch (error) {\n      console.error('获取商品规格失败:', error);\n      return this.fail(500, '获取商品规格失败');\n    }\n  }\n\n  /**\n   * 更新单个商品状态\n   * POST /admin/points-goods/update-status\n   */\n  async updateStatusAction() {\n    console.log('=== 更新积分商品状态 ===');\n\n    try {\n      const data = this.post();\n      const { id, status } = data;\n\n      if (!id) {\n        return this.fail(400, '积分商品ID不能为空');\n      }\n\n      if (status === undefined) {\n        return this.fail(400, '状态不能为空');\n      }\n\n      console.log('更新状态:', { id, status });\n\n      const pointsGoodsModel = this.model('points_goods');\n\n      // 检查积分商品是否存在\n      const existing = await pointsGoodsModel.where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(existing)) {\n        return this.fail(404, '积分商品不存在');\n      }\n\n      // 更新状态\n      const result = await pointsGoodsModel.where({ id: id }).update({\n        status: parseInt(status),\n        updated_at: new Date()\n      });\n\n      if (result) {\n        console.log('状态更新成功');\n        return this.success({}, '状态更新成功');\n      } else {\n        return this.fail(500, '状态更新失败');\n      }\n\n    } catch (error) {\n      console.error('更新积分商品状态失败:', error);\n      return this.fail(500, '状态更新失败');\n    }\n  }\n};\n"]}