//const ApiRoot = 'https://ht.rxkjsdj.com';
const ApiRoot = 'http://127.0.0.1:8360';
const ApiRootUrl = ApiRoot + '/api/'

// 判断是否为IP环境（本地开发）
function isIPEnvironment(url) {
  // 匹配IP地址格式：http://192.168.x.x 或 http://127.0.0.1 等
  const ipPattern = /^https?:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|localhost|127\.0\.0\.1)/;
  return ipPattern.test(url);
}

// 根据环境选择支付接口
const payInterface = isIPEnvironment(ApiRoot) ? 'pay/preWeixinPaya' : 'pay/preWeixinPay';

module.exports = {
  ApiRoot: ApiRoot,
  // 登录
  AuthLoginByWeixin: ApiRootUrl + 'auth/loginByWeixin', //微信登录
  GetPhoneNumber: ApiRootUrl + 'auth/getPhoneNumber', //获取手机号
  ConfirmQrLogin: ApiRootUrl + 'auth/confirmQrLogin', //确认二维码登录
  // 首页
  IndexUrl: ApiRootUrl + 'index/appInfo', //首页数据接口
  // 分类
  CatalogList: ApiRootUrl + 'catalog/index', //分类目录全部分类数据接口
  CatalogCurrent: ApiRootUrl + 'catalog/current', //分类目录当前分类数据接口
  GetCurrentList: ApiRootUrl + 'catalog/currentlist',
  // 购物车
  CartAdd: ApiRootUrl + 'cart/add', // 添加商品到购物车
  CartList: ApiRootUrl + 'cart/index', //获取购物车的数据
  CartUpdate: ApiRootUrl + 'cart/update', // 更新购物车的商品//
  CartDelete: ApiRootUrl + 'cart/delete', // 删除购物车的商品
  CartChecked: ApiRootUrl + 'cart/checked', // 选择或取消选择商品
  CartGoodsCount: ApiRootUrl + 'cart/goodsCount', // 获取购物车商品件数
  CartCheckout: ApiRootUrl + 'cart/checkout', // 下单前信息确认
  // 商品
  GoodsCount: ApiRootUrl + 'goods/count', //统计商品总数
  GoodsDetail: ApiRootUrl + 'goods/detail', //获得商品的详情
  GoodsList: ApiRootUrl + 'goods/list', //获得商品列表
  GoodsShare: ApiRootUrl + 'goods/goodsShare', //获得商品的详情
  SaveUserId: ApiRootUrl + 'goods/saveUserId',
  // 收货地址
  AddressDetail: ApiRootUrl + 'address/addressDetail', //收货地址详情
  DeleteAddress: ApiRootUrl + 'address/deleteAddress', //保存收货地址
  SaveAddress: ApiRootUrl + 'address/saveAddress', //保存收货地址
  GetAddresses: ApiRootUrl + 'address/getAddresses',
  RegionList: ApiRootUrl + 'region/list', //获取区域列表
  PayPrepayId: ApiRootUrl + payInterface, //获取微信统一下单prepay_id（自动切换）
  OrderSubmit: ApiRootUrl + 'order/submit', // 提交订单
  OrderList: ApiRootUrl + 'order/list', //订单列表
  OrderDetail: ApiRootUrl + 'order/detail', //订单详情
  OrderDelete: ApiRootUrl + 'order/delete', //订单删除
  OrderCancel: ApiRootUrl + 'order/cancel', //取消订单
  OrderConfirm: ApiRootUrl + 'order/confirm', //物流详情
  RefundApply: ApiRootUrl + 'order/refundApply', //申请售后
  RefundDetail: ApiRootUrl + 'order/refundDetail', //售后详情
  RefundCancel: ApiRootUrl + 'order/refundCancel', //撤销售后
  SubmitReturnLogistics: ApiRootUrl + 'order/submitReturnLogistics', //提交退货物流信息
  OrderCount: ApiRootUrl + 'order/count', // 获取订单数
  OrderCountInfo: ApiRootUrl + 'order/orderCount', // 我的页面获取订单数状态
  OrderExpressInfo: ApiRootUrl + 'order/express', //物流信息
  OrderGoods: ApiRootUrl + 'order/orderGoods', // 获取checkout页面的商品列表
  // 足迹
  FootprintList: ApiRootUrl + 'footprint/list', //足迹列表
  FootprintDelete: ApiRootUrl + 'footprint/delete', //删除足迹
  // 搜索
  SearchIndex: ApiRootUrl + 'search/index', //搜索页面数据
  SearchHelper: ApiRootUrl + 'search/helper', //搜索帮助
  SearchClearHistory: ApiRootUrl + 'search/clearHistory', //搜索帮助
  ShowSettings: ApiRootUrl + 'settings/showSettings',
  SaveSettings: ApiRootUrl + 'settings/save',
  SettingsDetail: ApiRootUrl + 'settings/userDetail',
  UploadAvatar: ApiRootUrl + 'upload/uploadAvatar',
  GetBase64: ApiRootUrl + 'qrcode/getBase64', //获取商品详情二维码
  // 推广记录
  RecordPromotionVisit: ApiRootUrl + 'promotion/recordVisit', //记录推广访问
  // 佣金相关
  CommissionInfo: ApiRootUrl + 'commission/info', //获取佣金信息
  CommissionWithdraw: ApiRootUrl + 'commission/withdraw', //申请提现
  CommissionWithdraws: ApiRootUrl + 'commission/withdraws', //提现记录
  // 用户数据
  UserPoints: ApiRootUrl + 'user/points', //用户积分
  // 签到
  SignInData: ApiRootUrl + 'signin/data', //获取签到数据
  SignIn: ApiRootUrl + 'signin/checkin', //执行签到
  // 订单兑换
  OrderExchangeConfig: ApiRootUrl + 'order-exchange/config', //获取兑换配置
  OrderExchange: ApiRootUrl + 'order-exchange/exchange', //执行订单兑换
  OrderExchangeRecords: ApiRootUrl + 'order-exchange/records', //获取兑换记录

  // 优惠券
  CouponAvailable: ApiRootUrl + 'coupon/available', //获取可领取的优惠券
  CouponReceive: ApiRootUrl + 'coupon/receive', //领取优惠券
  CouponMy: ApiRootUrl + 'coupon/my', //我的优惠券NotFoundError: url `/admin/admin/points-goods/categories` not found.
    at Object.throw (D:\py-ide\hioshop-miniprogram-master\service\node_modules\koa\lib\context.js:97:11)
    at D:\py-ide\hioshop-miniprogram-master\service\node_modules\think-trace\lib\index.js:33:23
    at tryCatcher (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\promise.js:502:31)
    at Promise._settlePromise (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\promise.js:559:18)
    at Promise._settlePromise0 (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\promise.js:604:10)
    at Promise._settlePromises (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\promise.js:683:18)
    at Async._drainQueue (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\async.js:138:16)
    at Async._drainQueues (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\async.js:148:10)
    at Immediate.Async.drainQueues [as _onImmediate] (D:\py-ide\hioshop-miniprogram-master\service\node_modules\thinkjs\node_modules\bluebird\js\release\async.js:17:14)
    at processImmediate (internal/timers.js:461:21) {
  request: {
    method: 'GET',
    url: '/admin/admin/points-goods/categories',
    header: {
      host: '127.0.0.1:8360',
      connection: 'keep-alive',
      'sec-ch-ua-platform': '"Windows"',
      authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyNiwiaWF0IjoxNzU0MzAzNjIwfQ.XGqvkkapDX9l8mpMCcGl2jloFwHkZjrShGgPGz4Urvk',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      accept: 'application/json, text/plain, */*',
      'x-hioshop-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyNiwiaWF0IjoxNzU0MzAzNjIwfQ.XGqvkkapDX9l8mpMCcGl2jloFwHkZjrShGgPGz4Urvk',
      'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
      'sec-ch-ua-mobile': '?0',
      origin: 'http://localhost:9528',
      'sec-fetch-site': 'cross-site',
      'sec-fetch-mode': 'cors',
      'sec-fetch-dest': 'empty',
      referer: 'http://localhost:9528/',
      'accept-encoding': 'gzip, deflate, br, zstd',
      'accept-language': 'zh-CN,zh;q=0.9'
    }
  },
  response: {
    status: 404,
    message: 'Not Found',
    header: [Object: null prototype] {
      vary: 'Origin',
      'access-control-allow-origin': 'http://localhost:9528',
  CouponAvailableForOrder: ApiRootUrl + 'coupon/availableForOrder', //订单可用优惠券
  CouponAutoSelectBest: ApiRootUrl + 'coupon/autoSelectBest', //自动选择最优优惠券

  // 推广相关
  PromotionDataCenter: ApiRootUrl + 'promotion/dataCenter', //推广数据中心

  // 秒杀相关
  SeckillTimeIndex: ApiRootUrl + 'seckill/index', //秒杀时间段列表
  SeckillList: ApiRootUrl + 'seckill/list', //秒杀商品列表
  SeckillDetail: ApiRootUrl + 'seckill/detail', //秒杀商品详情
  SeckillGoodsDetail: ApiRootUrl + 'seckill/goodsDetail', //秒杀商品详情（含价格和倒计时）
  SeckillHome: ApiRootUrl + 'seckill/home', //首页秒杀数据

  // 积分商城相关
  PointsGoodsHome: ApiRootUrl + 'points-goods/home', //积分商城首页数据
  PointsGoodsList: ApiRootUrl + 'points-goods/list', //积分商品列表
  PointsGoodsDetail: ApiRootUrl + 'points-goods/detail', //积分商品详情
  PointsGoodsExchange: ApiRootUrl + 'points-goods/exchange', //兑换积分商品
  PointsGoodsOrders: ApiRootUrl + 'points-goods/orders', //用户兑换记录

};