{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\order.js"], "names": ["Base", "require", "moment", "rp", "fs", "http", "module", "exports", "listAction", "userId", "getLoginUserId", "showType", "get", "page", "size", "status", "model", "getOrderStatus", "is_delete", "orderList", "field", "where", "user_id", "order_type", "order_status", "order", "countSelect", "newOrderList", "item", "data", "goodsList", "order_id", "id", "select", "goodsCount", "for<PERSON>ach", "v", "number", "add_time", "unix", "getOrderAddTime", "format", "order_status_text", "getOrderStatusText", "handleOption", "getOrderHandleOption", "refundApply", "find", "think", "isEmpty", "refundInfo", "refund_type", "status_text", "getRefundStatusText", "type_text", "getRefundTypeText", "hasRefund", "display_status_text", "push", "success", "countAction", "allCount", "count", "orderCountAction", "toPay", "refundOrderIds", "excludeOrderIds", "map", "toDeliveryCondition", "length", "toDelivery", "toReceiveCondition", "toReceive", "newStatus", "detailAction", "orderId", "orderInfo", "currentTime", "parseInt", "Date", "getTime", "fail", "province_name", "province", "getField", "city_name", "city", "district_name", "district", "full_region", "postscript", "<PERSON><PERSON><PERSON>", "from", "toString", "orderGoods", "gitem", "confirm_time", "dealdone_time", "pay_time", "shipping_time", "confirm_remainTime", "final_pay_time", "updateInfo", "update", "textCode", "getOrderTextCode", "orderGoodsAction", "cartList", "checked", "is_fast", "cancelAction", "post", "cancel", "goodsInfo", "goods_id", "product_id", "increment", "succesInfo", "deleteAction", "delete", "orderDeleteById", "confirmAction", "confirm", "handlePromotionCommissionRewards", "refundAction", "refundReason", "console", "log", "refund_time", "commissionService", "service", "refundResult", "handleRefundCommission", "message", "error", "refundApplyAction", "refundType", "refundAmount", "parseFloat", "refundDesc", "images", "apply_refund", "actual_price", "existingApply", "applyData", "order_sn", "refund_amount", "refund_reason", "refund_desc", "JSON", "stringify", "apply_time", "created_at", "updated_at", "applyId", "add", "refundDetailAction", "parse", "e", "apply_time_text", "toLocaleString", "refundCancelAction", "statusMap", "typeMap", "submitReturnLogisticsAction", "logisticsCompany", "logisticsNo", "user_logistics_company", "user_logistics_no", "user_return_time", "completeAction", "submitAction", "addressId", "freightPrice", "offlinePay", "userCouponId", "buffer", "checkedAddress", "checkedGoodsList", "checkPrice", "checkStock", "product", "goods_number", "seckill_round_id", "seckillGoods", "round_id", "currentSeckillPrice", "retail_price", "original_price", "discountRate", "flash_price", "Math", "round", "max", "abs", "add_price", "goodsTotalPrice", "cartItem", "discountAmount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couponResult", "validateAndUseCoupon", "userCoupon", "bestCouponResult", "autoSelectBestCoupon", "orderTotalPrice", "actualPrice", "print_info", "i", "Number", "goods_aka", "def", "sender_name", "Name", "sender_mobile", "Tel", "userInfo", "promoterUserId", "promoterId", "parentPromoterUserId", "promoter", "establishPromoterRelation", "parent_user_id", "generateOrderNumber", "consignee", "name", "mobile", "province_id", "city_id", "district_id", "address", "freight_price", "goods_price", "order_price", "change_price", "offline_pay", "user_coupon_id", "discount_amount", "original_amount", "orderGoodsData", "goodsItem", "goods_name", "list_pic_url", "goods_specifition_name_value", "goods_specifition_ids", "addMany", "round_goods_id", "quantity", "total_amount", "decrement", "mark<PERSON>ouponAsUsed", "createPromotionOrder", "recordShareCommissionRewards", "clearBuyGoods", "couponUsed", "updateAction", "updateAddress", "buyerUserId", "orderAmount", "goods", "commissionConfig", "getCommissionConfig", "commissionRate", "commission_rate", "commissionAmount", "toFixed", "isFirstOrder", "checkFirstOrder", "promotionOrderData", "promoter_id", "promoter_user_id", "buyer_user_id", "order_amount", "commission_amount", "share_source", "is_first_order", "create_time", "update_time", "updatePromoterOrderStats", "goodsId", "config", "is_active", "orderCount", "total_orders", "month_orders", "promotionOrders", "promotionOrder", "distributionConfig", "is_distributed", "personalCommission", "personal_rate", "level1Commission", "level1_rate", "level2Commission", "level2_rate", "teamLeaderCommission", "team_leader_rate", "result", "settleCommission", "parent_promoter_user_id", "settle_time", "personal_commission", "level1_commission", "level2_commission", "team_leader_commission", "existingPromoter", "promoterData", "level", "total_views", "total_commission", "month_views", "month_commission", "first_share_time", "addShareCommissionWithParent", "commission", "expressAction", "info", "expressInfo", "updateTime", "com", "is_finish", "shipperCode", "shipper_code", "expressNo", "logistic_code", "lastExpressInfo", "getExpressInfo", "deliverystatus", "newUpdateTime", "getDeliverystatus", "issign", "traces", "list", "dataInfo", "express_status", "express", "appCode", "options", "method", "url", "headers", "sessionData", "alias", "join", "min_amount", "calculateDiscount", "logger", "availableCoupons", "bestCoupon", "maxDiscount", "coupon", "discount", "amount", "discount_type", "min", "discount_value", "max_discount", "originalAmount", "finalAmount", "used_at", "coupon_id", "final_amount", "coupon_code"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,KAAKF,QAAQ,iBAAR,CAAX;AACA,MAAMG,KAAKH,QAAQ,IAAR,CAAX;AACA,MAAMI,OAAOJ,QAAQ,MAAR,CAAb;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;AAChC;;;;AAIMQ,cAAN,GAAmB;AAAA;;AAAA;AACrB,kBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;AACA,kBAAMC,WAAW,MAAKC,GAAL,CAAS,UAAT,CAAjB;AACM,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAME,OAAO,MAAKF,GAAL,CAAS,MAAT,CAAb;AACA,gBAAIG,SAAS,EAAb;AACAA,qBAAS,MAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,cAApB,CAAmCN,QAAnC,CAAf;AACA,gBAAIO,YAAY,CAAhB;AACA,kBAAMC,YAAY,MAAM,MAAKH,KAAL,CAAW,OAAX,EAAoBI,KAApB,CAA0B,oDAA1B,EAAgFC,KAAhF,CAAsF;AAC1GC,yBAASb,MADiG;AAE1GS,2BAAWA,SAF+F;AAG1GK,4BAAY,CAAC,GAAD,EAAM,CAAN,CAH8F;AAI1GC,8BAAc,CAAC,IAAD,EAAOT,MAAP;AAJ4F,aAAtF,EAKrBF,IALqB,CAKhBA,IALgB,EAKVC,IALU,EAKJW,KALI,CAKE,eALF,EAKmBC,WALnB,EAAxB;AAMA,kBAAMC,eAAe,EAArB;AACA,iBAAK,MAAMC,IAAX,IAAmBT,UAAUU,IAA7B,EAAmC;AAC/B;AACAD,qBAAKE,SAAL,GAAiB,MAAM,MAAKd,KAAL,CAAW,aAAX,EAA0BI,KAA1B,CAAgC,uFAAhC,EAAyHC,KAAzH,CAA+H;AAClJC,6BAASb,MADyI;AAElJsB,8BAAUH,KAAKI,EAFmI;AAGlJd,+BAAW;AAHuI,iBAA/H,EAIpBe,MAJoB,EAAvB;AAKAL,qBAAKM,UAAL,GAAkB,CAAlB;AACAN,qBAAKE,SAAL,CAAeK,OAAf,CAAuB,aAAK;AACxBP,yBAAKM,UAAL,IAAmBE,EAAEC,MAArB;AACH,iBAFD;AAGAT,qBAAKU,QAAL,GAAgBpC,OAAOqC,IAAP,EAAY,MAAM,MAAKvB,KAAL,CAAW,OAAX,EAAoBwB,eAApB,CAAoCZ,KAAKI,EAAzC,CAAlB,GAAgES,MAAhE,CAAuE,qBAAvE,CAAhB;AACA;AACA;AACA;AACAb,qBAAKc,iBAAL,GAAyB,MAAM,MAAK1B,KAAL,CAAW,OAAX,EAAoB2B,kBAApB,CAAuCf,KAAKI,EAA5C,CAA/B;AACA;AACAJ,qBAAKgB,YAAL,GAAoB,MAAM,MAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyCjB,KAAKI,EAA9C,CAA1B;;AAEA;AACA,oBAAIc,cAAc,MAAM,MAAK9B,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACrDU,8BAAUH,KAAKI;AADsC,iBAAjC,EAErBe,IAFqB,EAAxB;;AAIA,oBAAI,CAACC,MAAMC,OAAN,CAAcH,WAAd,CAAL,EAAiC;AAC7B;AACAlB,yBAAKsB,UAAL,GAAkB;AACdnC,gCAAQ+B,YAAY/B,MADN;AAEdoC,qCAAaL,YAAYK,WAFX;AAGdC,qCAAa,MAAKC,mBAAL,CAAyBP,YAAY/B,MAArC,CAHC;AAIduC,mCAAW,MAAKC,iBAAL,CAAuBT,YAAYK,WAAnC;AAJG,qBAAlB;AAMAvB,yBAAK4B,SAAL,GAAiB,IAAjB;;AAEA;AACA5B,yBAAK6B,mBAAL,GAA2B7B,KAAKsB,UAAL,CAAgBI,SAAhB,GAA4B1B,KAAKsB,UAAL,CAAgBE,WAAvE;AACH,iBAZD,MAYO;AACHxB,yBAAKsB,UAAL,GAAkB,IAAlB;AACAtB,yBAAK4B,SAAL,GAAiB,KAAjB;AACA5B,yBAAK6B,mBAAL,GAA2B7B,KAAKc,iBAAhC;AACH;;AAEDf,6BAAa+B,IAAb,CAAkB9B,IAAlB;AACH;AACDT,sBAAUU,IAAV,GAAiBF,YAAjB;AACA,mBAAO,MAAKgC,OAAL,CAAaxC,SAAb,CAAP;AA5De;AA6DlB;AACD;AACA;AACMyC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMjD,WAAW,OAAKC,GAAL,CAAS,UAAT,CAAjB;AACN,kBAAMH,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAIK,SAAS,EAAb;AACAA,qBAAS,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBC,cAApB,CAAmCN,QAAnC,CAAf;AACA,gBAAIO,YAAY,CAAhB;AACA,kBAAM2C,WAAW,MAAM,OAAK7C,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC7CC,yBAASb,MADoC;AAE7CS,2BAAWA,SAFkC;AAG7CM,8BAAc,CAAC,IAAD,EAAOT,MAAP;AAH+B,aAA1B,EAIpB+C,KAJoB,CAId,IAJc,CAAvB;AAKA,mBAAO,OAAKH,OAAL,CAAa;AAChBE,0BAAUA;AADM,aAAb,CAAP;AAXgB;AAcnB;AACD;AACA;AACME,oBAAN,GAAyB;AAAA;;AAAA;AAC3B;AACA,kBAAMzC,UAAU,MAAM,OAAKZ,cAAL,EAAtB;AACM,gBAAGY,WAAW,CAAd,EAAgB;AACZ,oBAAI0C,QAAQ,MAAM,OAAKhD,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AACxCC,6BAASA,OAD+B;AAExCJ,+BAAW,CAF6B;AAGxCK,gCAAY,CAAC,GAAD,EAAM,CAAN,CAH4B;AAIxCC,kCAAc,CAAC,IAAD,EAAO,SAAP;AAJ0B,iBAA1B,EAKfsC,KALe,CAKT,IALS,CAAlB;AAMA;AACA,sBAAMG,iBAAiB,MAAM,OAAKjD,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AAC1DC,6BAASA;AADiD,iBAAjC,EAE1BF,KAF0B,CAEpB,UAFoB,EAERa,MAFQ,EAA7B;AAGA,sBAAMiC,kBAAkBD,eAAeE,GAAf,CAAmB;AAAA,2BAAQvC,KAAKG,QAAb;AAAA,iBAAnB,CAAxB;;AAEA,oBAAIqC,sBAAsB;AACtB9C,6BAASA,OADa;AAEtBJ,+BAAW,CAFW;AAGtBK,gCAAY,CAAC,GAAD,EAAM,CAAN,CAHU;AAItBC,kCAAc,CAAC,IAAD,EAAO,SAAP,CAJQ,CAIW;AAJX,iBAA1B;;AAOA;AACA,oBAAI0C,gBAAgBG,MAAhB,GAAyB,CAA7B,EAAgC;AAC5BD,wCAAoBpC,EAApB,GAAyB,CAAC,QAAD,EAAWkC,eAAX,CAAzB;AACH;;AAED,oBAAII,aAAa,MAAM,OAAKtD,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B+C,mBAA1B,EAA+CN,KAA/C,CAAqD,IAArD,CAAvB;AACA,oBAAIS,qBAAqB;AACrBjD,6BAASA,OADY;AAErBC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAFS;AAGrBL,+BAAW,CAHU;AAIrBM,kCAAc;AAJO,iBAAzB;;AAOA;AACA,oBAAI0C,gBAAgBG,MAAhB,GAAyB,CAA7B,EAAgC;AAC5BE,uCAAmBvC,EAAnB,GAAwB,CAAC,QAAD,EAAWkC,eAAX,CAAxB;AACH;;AAED,oBAAIM,YAAY,MAAM,OAAKxD,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0BkD,kBAA1B,EAA8CT,KAA9C,CAAoD,IAApD,CAAtB;AACA,oBAAIW,YAAY;AACZT,2BAAOA,KADK;AAEZM,gCAAYA,UAFA;AAGZE,+BAAWA;AAHC,iBAAhB;AAKA,uBAAO,OAAKb,OAAL,CAAac,SAAb,CAAP;AACH;AAhDoB;AAkDxB;AACKC,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMC,UAAU,OAAK/D,GAAL,CAAS,SAAT,CAAhB;AACN,kBAAMH,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMkE,YAAY,MAAM,OAAK5D,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC9CC,yBAASb,MADqC;AAE9CuB,oBAAI2C;AAF0C,aAA1B,EAGrB5B,IAHqB,EAAxB;AAIA,kBAAM8B,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,gBAAIhC,MAAMC,OAAN,CAAc2B,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAKK,IAAL,CAAU,OAAV,CAAP;AACH;AACDL,sBAAUM,aAAV,GAA0B,MAAM,OAAKlE,KAAL,CAAW,QAAX,EAAqBK,KAArB,CAA2B;AACvDW,oBAAI4C,UAAUO;AADyC,aAA3B,EAE7BC,QAF6B,CAEpB,MAFoB,EAEZ,IAFY,CAAhC;AAGAR,sBAAUS,SAAV,GAAsB,MAAM,OAAKrE,KAAL,CAAW,QAAX,EAAqBK,KAArB,CAA2B;AACnDW,oBAAI4C,UAAUU;AADqC,aAA3B,EAEzBF,QAFyB,CAEhB,MAFgB,EAER,IAFQ,CAA5B;AAGAR,sBAAUW,aAAV,GAA0B,MAAM,OAAKvE,KAAL,CAAW,QAAX,EAAqBK,KAArB,CAA2B;AACvDW,oBAAI4C,UAAUY;AADyC,aAA3B,EAE7BJ,QAF6B,CAEpB,MAFoB,EAEZ,IAFY,CAAhC;AAGAR,sBAAUa,WAAV,GAAwBb,UAAUM,aAAV,GAA0BN,UAAUS,SAApC,GAAgDT,UAAUW,aAAlF;AACAX,sBAAUc,UAAV,GAAuBC,OAAOC,IAAP,CAAYhB,UAAUc,UAAtB,EAAkC,QAAlC,EAA4CG,QAA5C,EAAvB;AACA,kBAAMC,aAAa,MAAM,OAAK9E,KAAL,CAAW,aAAX,EAA0BK,KAA1B,CAAgC;AACrDC,yBAASb,MAD4C;AAErDsB,0BAAU4C,OAF2C;AAGrDzD,2BAAW;AAH0C,aAAhC,EAItBe,MAJsB,EAAzB;AAKA,gBAAIC,aAAa,CAAjB;AACA,iBAAK,MAAM6D,KAAX,IAAoBD,UAApB,EAAgC;AAC5B5D,8BAAc6D,MAAM1D,MAApB;AACH;AACD;AACAuC,sBAAUlC,iBAAV,GAA8B,MAAM,OAAK1B,KAAL,CAAW,OAAX,EAAoB2B,kBAApB,CAAuCgC,OAAvC,CAApC;AACA,gBAAI3B,MAAMC,OAAN,CAAc2B,UAAUoB,YAAxB,CAAJ,EAA2C;AACvCpB,0BAAUoB,YAAV,GAAyB,CAAzB;AACH,aAFD,MAEOpB,UAAUoB,YAAV,GAAyB9F,OAAOqC,IAAP,CAAYqC,UAAUoB,YAAtB,EAAoCvD,MAApC,CAA2C,qBAA3C,CAAzB;AACP,gBAAIO,MAAMC,OAAN,CAAc2B,UAAUqB,aAAxB,CAAJ,EAA4C;AACxCrB,0BAAUqB,aAAV,GAA0B,CAA1B;AACH,aAFD,MAEOrB,UAAUqB,aAAV,GAA0B/F,OAAOqC,IAAP,CAAYqC,UAAUqB,aAAtB,EAAqCxD,MAArC,CAA4C,qBAA5C,CAA1B;AACP,gBAAIO,MAAMC,OAAN,CAAc2B,UAAUsB,QAAxB,CAAJ,EAAuC;AACnCtB,0BAAUsB,QAAV,GAAqB,CAArB;AACH,aAFD,MAEOtB,UAAUsB,QAAV,GAAqBhG,OAAOqC,IAAP,CAAYqC,UAAUsB,QAAtB,EAAgCzD,MAAhC,CAAuC,qBAAvC,CAArB;AACP,gBAAIO,MAAMC,OAAN,CAAc2B,UAAUuB,aAAxB,CAAJ,EAA4C;AACxCvB,0BAAUuB,aAAV,GAA0B,CAA1B;AACH,aAFD,MAEO;AACHvB,0BAAUwB,kBAAV,GAA+BxB,UAAUuB,aAAV,GAA0B,KAAK,EAAL,GAAU,EAAV,GAAe,EAAxE;AACAvB,0BAAUuB,aAAV,GAA0BjG,OAAOqC,IAAP,CAAYqC,UAAUuB,aAAtB,EAAqC1D,MAArC,CAA4C,qBAA5C,CAA1B;AACH;AACD;AACA,gBAAImC,UAAUpD,YAAV,KAA2B,GAA3B,IAAkCoD,UAAUpD,YAAV,KAA2B,GAAjE,EAAsE;AAClE;AACAoD,0BAAUyB,cAAV,GAA2BzB,UAAUtC,QAAV,GAAqB,KAAK,EAAL,GAAU,EAA1D,CAFkE,CAEJ;AAC9D,oBAAIsC,UAAUyB,cAAV,GAA2BxB,WAA/B,EAA4C;AACxC;AACA,wBAAIyB,aAAa;AACb9E,sCAAc;AADD,qBAAjB;AAGA,0BAAM,OAAKR,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC5BW,4BAAI2C;AADwB,qBAA1B,EAEH4B,MAFG,CAEID,UAFJ,CAAN;AAGH;AACJ;AACD1B,sBAAUtC,QAAV,GAAqBpC,OAAOqC,IAAP,CAAYqC,UAAUtC,QAAtB,EAAgCG,MAAhC,CAAuC,qBAAvC,CAArB;AACAmC,sBAAUpD,YAAV,GAAyB,EAAzB;AACA;AACA,kBAAMoB,eAAe,MAAM,OAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyC8B,OAAzC,CAA3B;AACA,kBAAM6B,WAAW,MAAM,OAAKxF,KAAL,CAAW,OAAX,EAAoByF,gBAApB,CAAqC9B,OAArC,CAAvB;AACA,mBAAO,OAAKhB,OAAL,CAAa;AAChBiB,2BAAWA,SADK;AAEhBkB,4BAAYA,UAFI;AAGhBlD,8BAAcA,YAHE;AAIhB4D,0BAAUA,QAJM;AAKhBtE,4BAAYA;AALI,aAAb,CAAP;AAnEiB;AA0EpB;AACD;;;;AAIMwE,oBAAN,GAAyB;AAAA;;AAAA;AAC3B,kBAAMjG,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMiE,UAAU,OAAK/D,GAAL,CAAS,SAAT,CAAhB;AACA,gBAAI+D,UAAU,CAAd,EAAiB;AACb,sBAAMmB,aAAa,MAAM,OAAK9E,KAAL,CAAW,aAAX,EAA0BI,KAA1B,CAAgC,2GAAhC,EAA6IC,KAA7I,CAAmJ;AACxKC,6BAASb,MAD+J;AAExKsB,8BAAU4C,OAF8J;AAGxKzD,+BAAW;AAH6J,iBAAnJ,EAItBe,MAJsB,EAAzB;AAKA,oBAAIC,aAAa,CAAjB;AACA,qBAAK,MAAM6D,KAAX,IAAoBD,UAApB,EAAgC;AAC5B5D,kCAAc6D,MAAM1D,MAApB;AACH;AACD,uBAAO,OAAKsB,OAAL,CAAamC,UAAb,CAAP;AACH,aAXD,MAWO;AACH,sBAAMa,WAAW,MAAM,OAAK3F,KAAL,CAAW,MAAX,EAAmBK,KAAnB,CAAyB;AAC5CC,6BAASb,MADmC;AAE5CmG,6BAAQ,CAFoC;AAG5C1F,+BAAW,CAHiC;AAI5C2F,6BAAS;AAJmC,iBAAzB,EAKpB5E,MALoB,EAAvB;AAMA,uBAAO,OAAK0B,OAAL,CAAagD,QAAb,CAAP;AACH;AAtBoB;AAuBxB;AACD;;;;AAIMG,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMnC,UAAU,OAAKoC,IAAL,CAAU,SAAV,CAAhB;AACN,kBAAMtG,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM;AACA,kBAAMkC,eAAe,MAAM,OAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyC8B,OAAzC,CAA3B;AACA;AACA,gBAAI,CAAC/B,aAAaoE,MAAlB,EAA0B;AACtB,uBAAO,OAAK/B,IAAL,CAAU,QAAV,CAAP;AACH;AACD;AACA,gBAAIqB,aAAa;AACb9E,8BAAc;AADD,aAAjB;AAGA,gBAAIoD,YAAY,MAAM,OAAK5D,KAAL,CAAW,OAAX,EAAoBI,KAApB,CAA0B,YAA1B,EAAwCC,KAAxC,CAA8C;AAChEW,oBAAI2C,OAD4D;AAEhErD,yBAASb;AAFuD,aAA9C,EAGnBsC,IAHmB,EAAtB;AAIA;AACA,kBAAMkE,YAAY,MAAM,OAAKjG,KAAL,CAAW,aAAX,EAA0BK,KAA1B,CAAgC;AACpDU,0BAAU4C,OAD0C;AAEpDrD,yBAASb;AAF2C,aAAhC,EAGrBwB,MAHqB,EAAxB;AAIA,iBAAK,MAAML,IAAX,IAAmBqF,SAAnB,EAA8B;AAC1B,oBAAIC,WAAWtF,KAAKsF,QAApB;AACA,oBAAIC,aAAavF,KAAKuF,UAAtB;AACA,oBAAI9E,SAAST,KAAKS,MAAlB;AACA,sBAAM,OAAKrB,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC5BW,wBAAIkF;AADwB,iBAA1B,EAEHE,SAFG,CAEO,cAFP,EAEuB/E,MAFvB,CAAN;AAGA,sBAAM,OAAKrB,KAAL,CAAW,SAAX,EAAsBK,KAAtB,CAA4B;AAC9BW,wBAAImF;AAD0B,iBAA5B,EAEHC,SAFG,CAEO,cAFP,EAEuB/E,MAFvB,CAAN;AAGH;AACD,kBAAMgF,aAAa,MAAM,OAAKrG,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC/CW,oBAAI2C;AAD2C,aAA1B,EAEtB4B,MAFsB,CAEfD,UAFe,CAAzB;AAGA,mBAAO,OAAK3C,OAAL,CAAa0D,UAAb,CAAP;AApCiB;AAqCpB;AACD;;;;AAIMC,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAM3C,UAAU,OAAKoC,IAAL,CAAU,SAAV,CAAhB;AACA;AACA,kBAAMnE,eAAe,MAAM,OAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyC8B,OAAzC,CAA3B;AACA,gBAAI,CAAC/B,aAAa2E,MAAlB,EAA0B;AACtB,uBAAO,OAAKtC,IAAL,CAAU,QAAV,CAAP;AACH;AACD,kBAAMoC,aAAa,MAAM,OAAKrG,KAAL,CAAW,OAAX,EAAoBwG,eAApB,CAAoC7C,OAApC,CAAzB;AACA,mBAAO,OAAKhB,OAAL,CAAa0D,UAAb,CAAP;AARiB;AASpB;AACD;;;;AAIMI,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM9C,UAAU,OAAKoC,IAAL,CAAU,SAAV,CAAhB;AACA;AACA,kBAAMnE,eAAe,MAAM,OAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyC8B,OAAzC,CAA3B;AACA,gBAAI,CAAC/B,aAAa8E,OAAlB,EAA2B;AACvB,uBAAO,OAAKzC,IAAL,CAAU,QAAV,CAAP;AACH;AACD;AACA,kBAAMJ,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,gBAAIsB,aAAa;AACb9E,8BAAc,GADD;AAEbwE,8BAAcnB;AAFD,aAAjB;AAIA,kBAAMwC,aAAa,MAAM,OAAKrG,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC/CW,oBAAI2C;AAD2C,aAA1B,EAEtB4B,MAFsB,CAEfD,UAFe,CAAzB;;AAIA;AACA,kBAAM,OAAKqB,gCAAL,CAAsChD,OAAtC,CAAN;;AAEA,mBAAO,OAAKhB,OAAL,CAAa0D,UAAb,CAAP;AApBkB;AAqBrB;;AAED;;;;AAIMO,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMjD,UAAU,OAAKoC,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMc,eAAe,OAAKd,IAAL,CAAU,cAAV,KAA6B,QAAlD;;AAEA,gBAAI;AACAe,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBpD,OAArB,EAA8B,OAA9B,EAAuCkD,YAAvC;;AAEA;AACA,sBAAMpG,QAAQ,MAAM,OAAKT,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B,EAAEW,IAAI2C,OAAN,EAA1B,EAA2C5B,IAA3C,EAApB;AACA,oBAAIC,MAAMC,OAAN,CAAcxB,KAAd,CAAJ,EAA0B;AACtB,2BAAO,OAAKwD,IAAL,CAAU,OAAV,CAAP;AACH;;AAED,oBAAIxD,MAAMD,YAAN,KAAuB,GAA3B,EAAgC;AAC5B,2BAAO,OAAKyD,IAAL,CAAU,QAAV,CAAP;AACH;;AAED;AACA,sBAAMJ,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAM,OAAKhE,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B,EAAEW,IAAI2C,OAAN,EAA1B,EAA2C4B,MAA3C,CAAkD;AACpD/E,kCAAc,GADsC,EACjC;AACnBwG,iCAAanD;AAFuC,iBAAlD,CAAN;;AAKA;AACA,sBAAMoD,oBAAoB,OAAKC,OAAL,CAAa,YAAb,CAA1B;AACA,sBAAMC,eAAe,MAAMF,kBAAkBG,sBAAlB,CAAyCzD,OAAzC,CAA3B;;AAEA,oBAAIwD,aAAaxE,OAAjB,EAA0B;AACtBmE,4BAAQC,GAAR,CAAY,YAAZ;AACA,2BAAO,OAAKpE,OAAL,CAAa;AAChB0E,iCAAS,QADO;AAEhBF,sCAAcA;AAFE,qBAAb,CAAP;AAIH,iBAND,MAMO;AACHL,4BAAQC,GAAR,CAAY,aAAZ,EAA2BI,aAAaE,OAAxC;AACA,2BAAO,OAAKpD,IAAL,CAAU,eAAekD,aAAaE,OAAtC,CAAP;AACH;AAEJ,aApCD,CAoCE,OAAOC,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKrD,IAAL,CAAU,aAAaqD,MAAMD,OAA7B,CAAP;AACH;AA3CgB;AA4CpB;;AAED;;;;AAIME,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAM9H,SAAS,MAAM,QAAKC,cAAL,EAArB;AACA,kBAAMiE,UAAU,QAAKoC,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMyB,aAAa,QAAKzB,IAAL,CAAU,YAAV,KAA2B,aAA9C,CAHsB,CAGuC;AAC7D,kBAAM0B,eAAeC,WAAW,QAAK3B,IAAL,CAAU,cAAV,CAAX,KAAyC,CAA9D;AACA,kBAAMc,eAAe,QAAKd,IAAL,CAAU,cAAV,KAA6B,EAAlD;AACA,kBAAM4B,aAAa,QAAK5B,IAAL,CAAU,YAAV,KAA2B,EAA9C;AACA,kBAAM6B,SAAS,QAAK7B,IAAL,CAAU,QAAV,KAAuB,EAAtC;;AAEA,gBAAI;AACAe,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBtH,MAArB,EAA6B,OAA7B,EAAsCkE,OAAtC;AACAmD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBS,UAArB,EAAiC,OAAjC,EAA0CC,YAA1C;;AAEA;AACA,oBAAI,CAAChI,MAAL,EAAa;AACT,2BAAO,QAAKwE,IAAL,CAAU,MAAV,CAAP;AACH;;AAED;AACA,sBAAMxD,QAAQ,MAAM,QAAKT,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC1CW,wBAAI2C,OADsC;AAE1CrD,6BAASb;AAFiC,iBAA1B,EAGjBsC,IAHiB,EAApB;;AAKA,oBAAIC,MAAMC,OAAN,CAAcxB,KAAd,CAAJ,EAA0B;AACtB,2BAAO,QAAKwD,IAAL,CAAU,aAAV,CAAP;AACH;;AAED;AACA,sBAAMrC,eAAe,MAAM,QAAK5B,KAAL,CAAW,OAAX,EAAoB6B,oBAApB,CAAyC8B,OAAzC,CAA3B;AACA,oBAAI,CAAC/B,aAAaiG,YAAlB,EAAgC;AAC5B,2BAAO,QAAK5D,IAAL,CAAU,eAAV,CAAP;AACH;;AAED;AACA,oBAAIwD,gBAAgB,CAAhB,IAAqBA,eAAeC,WAAWjH,MAAMqH,YAAjB,CAAxC,EAAwE;AACpE,2BAAO,QAAK7D,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,sBAAM8D,gBAAgB,MAAM,QAAK/H,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACzDU,8BAAU4C;AAD+C,iBAAjC,EAEzB5B,IAFyB,EAA5B;;AAIA,oBAAI,CAACC,MAAMC,OAAN,CAAc8F,aAAd,CAAL,EAAmC;AAC/B;AACA,4BAAQA,cAAchI,MAAtB;AACI,6BAAK,SAAL;AACI,mCAAO,QAAKkE,IAAL,CAAU,oBAAV,CAAP;AACJ,6BAAK,YAAL;AACI,mCAAO,QAAKA,IAAL,CAAU,oBAAV,CAAP;AACJ,6BAAK,UAAL;AACI,mCAAO,QAAKA,IAAL,CAAU,mBAAV,CAAP;AACJ,6BAAK,UAAL;AACI,mCAAO,QAAKA,IAAL,CAAU,uBAAV,CAAP;AACJ,6BAAK,WAAL;AACI,mCAAO,QAAKA,IAAL,CAAU,iBAAV,CAAP;AACJ;AACI,mCAAO,QAAKA,IAAL,CAAU,oBAAV,CAAP;AAZR;AAcH;;AAED;AACA,sBAAMJ,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAMgE,YAAY;AACdjH,8BAAU4C,OADI;AAEdrD,6BAASb,MAFK;AAGdwI,8BAAUxH,MAAMwH,QAHF;AAId9F,iCAAaqF,UAJC;AAKdU,mCAAeT,YALD;AAMdU,mCAAetB,YAND;AAOduB,iCAAaT,UAPC;AAQdC,4BAAQS,KAAKC,SAAL,CAAeV,MAAf,CARM;AASd7H,4BAAQ,SATM,EASK;AACnBwI,gCAAY1E,WAVE;AAWd2E,gCAAY,IAAIzE,IAAJ,EAXE;AAYd0E,gCAAY,IAAI1E,IAAJ;AAZE,iBAAlB;;AAeA,sBAAM2E,UAAU,MAAM,QAAK1I,KAAL,CAAW,cAAX,EAA2B2I,GAA3B,CAA+BX,SAA/B,CAAtB;;AAEAlB,wBAAQC,GAAR,CAAY,kBAAZ,EAAgC2B,OAAhC;AACA,uBAAO,QAAK/F,OAAL,CAAa;AAChB0E,6BAAS,kBADO;AAEhBqB,6BAASA;AAFO,iBAAb,CAAP;AAKH,aA/ED,CA+EE,OAAOpB,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,uBAAO,QAAKrD,IAAL,CAAU,aAAaqD,MAAMD,OAA7B,CAAP;AACH;AA3FqB;AA4FzB;;AAED;;;;AAIMuB,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMnJ,SAAS,MAAM,QAAKC,cAAL,EAArB;AACA,kBAAMgJ,UAAU,QAAK9I,GAAL,CAAS,SAAT,CAAhB;;AAEA,gBAAI;AACAkH,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBtH,MAArB,EAA6B,OAA7B,EAAsCiJ,OAAtC;;AAEA,oBAAI,CAACjJ,MAAL,EAAa;AACT,2BAAO,QAAKwE,IAAL,CAAU,MAAV,CAAP;AACH;;AAED;AACA,sBAAMnC,cAAc,MAAM,QAAK9B,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACvDW,wBAAI0H,OADmD;AAEvDpI,6BAASb;AAF8C,iBAAjC,EAGvBsC,IAHuB,EAA1B;;AAKA,oBAAIC,MAAMC,OAAN,CAAcH,WAAd,CAAJ,EAAgC;AAC5B,2BAAO,QAAKmC,IAAL,CAAU,eAAV,CAAP;AACH;;AAED;AACA,sBAAML,YAAY,MAAM,QAAK5D,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC9CW,wBAAIc,YAAYf;AAD8B,iBAA1B,EAErBgB,IAFqB,EAAxB;;AAIA;AACA,sBAAM+C,aAAa,MAAM,QAAK9E,KAAL,CAAW,aAAX,EAA0BK,KAA1B,CAAgC;AACrDU,8BAAUe,YAAYf;AAD+B,iBAAhC,EAEtBE,MAFsB,EAAzB;;AAIA;AACA,oBAAIa,YAAY8F,MAAhB,EAAwB;AACpB,wBAAI;AACA9F,oCAAY8F,MAAZ,GAAqBS,KAAKQ,KAAL,CAAW/G,YAAY8F,MAAvB,CAArB;AACH,qBAFD,CAEE,OAAOkB,CAAP,EAAU;AACRhH,oCAAY8F,MAAZ,GAAqB,EAArB;AACH;AACJ,iBAND,MAMO;AACH9F,gCAAY8F,MAAZ,GAAqB,EAArB;AACH;;AAED;AACA9F,4BAAYiH,eAAZ,GAA8B,IAAIhF,IAAJ,CAASjC,YAAYyG,UAAZ,GAAyB,IAAlC,EAAwCS,cAAxC,EAA9B;;AAEAlC,wBAAQC,GAAR,CAAY,YAAZ;AACA,uBAAO,QAAKpE,OAAL,CAAa;AAChBT,gCAAYJ,WADI;AAEhB8B,+BAAWA,SAFK;AAGhBkB,gCAAYA;AAHI,iBAAb,CAAP;AAMH,aAjDD,CAiDE,OAAOwC,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKrD,IAAL,CAAU,eAAeqD,MAAMD,OAA/B,CAAP;AACH;AAxDsB;AAyD1B;;AAED;;;;AAIM4B,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMxJ,SAAS,MAAM,QAAKC,cAAL,EAArB;AACA,kBAAMgJ,UAAU,QAAK3C,IAAL,CAAU,SAAV,CAAhB;;AAEA,gBAAI;AACAe,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBtH,MAArB,EAA6B,OAA7B,EAAsCiJ,OAAtC;;AAEA,oBAAI,CAACjJ,MAAL,EAAa;AACT,2BAAO,QAAKwE,IAAL,CAAU,MAAV,CAAP;AACH;;AAED;AACA,sBAAMnC,cAAc,MAAM,QAAK9B,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACvDW,wBAAI0H,OADmD;AAEvDpI,6BAASb;AAF8C,iBAAjC,EAGvBsC,IAHuB,EAA1B;;AAKA,oBAAIC,MAAMC,OAAN,CAAcH,WAAd,CAAJ,EAAgC;AAC5B,2BAAO,QAAKmC,IAAL,CAAU,eAAV,CAAP;AACH;;AAED;AACA,oBAAInC,YAAY/B,MAAZ,KAAuB,SAA3B,EAAsC;AAClC,2BAAO,QAAKkE,IAAL,CAAU,aAAV,CAAP;AACH;;AAED;AACA,sBAAM,QAAKjE,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACnCW,wBAAI0H;AAD+B,iBAAjC,EAEHnC,MAFG,EAAN;;AAIAO,wBAAQC,GAAR,CAAY,YAAZ;AACA,uBAAO,QAAKpE,OAAL,CAAa;AAChB0E,6BAAS;AADO,iBAAb,CAAP;AAIH,aAjCD,CAiCE,OAAOC,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKrD,IAAL,CAAU,eAAeqD,MAAMD,OAA/B,CAAP;AACH;AAxCsB;AAyC1B;;AAED;;;AAGAhF,wBAAoBtC,MAApB,EAA4B;AACxB,cAAMmJ,YAAY;AACd,uBAAW,KADG;AAEd,0BAAc,KAFA;AAGd,wBAAY,KAHE;AAId,wBAAY,KAJE;AAKd,2BAAe,MALD;AAMd,wBAAY,KANE;AAOd,yBAAa;AAPC,SAAlB;AASA,eAAOA,UAAUnJ,MAAV,KAAqB,MAA5B;AACH;;AAED;;;AAGAwC,sBAAkBiF,UAAlB,EAA8B;AAC1B,cAAM2B,UAAU;AACZ,2BAAe,KADH;AAEZ,6BAAiB;AAFL,SAAhB;AAIA,eAAOA,QAAQ3B,UAAR,KAAuB,IAA9B;AACH;;AAED;;;;AAIM4B,+BAAN,GAAoC;AAAA;;AAAA;AAChC,kBAAM3J,SAAS,MAAM,QAAKC,cAAL,EAArB;AACA,kBAAMgJ,UAAU,QAAK3C,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMsD,mBAAmB,QAAKtD,IAAL,CAAU,kBAAV,CAAzB;AACA,kBAAMuD,cAAc,QAAKvD,IAAL,CAAU,aAAV,CAApB;;AAEA,gBAAI;AACAe,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBtH,MAArB,EAA6B,OAA7B,EAAsCiJ,OAAtC;;AAEA,oBAAI,CAACjJ,MAAL,EAAa;AACT,2BAAO,QAAKwE,IAAL,CAAU,MAAV,CAAP;AACH;;AAED,oBAAI,CAACoF,gBAAD,IAAqB,CAACC,WAA1B,EAAuC;AACnC,2BAAO,QAAKrF,IAAL,CAAU,YAAV,CAAP;AACH;;AAED;AACA,sBAAMnC,cAAc,MAAM,QAAK9B,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACvDW,wBAAI0H,OADmD;AAEvDpI,6BAASb;AAF8C,iBAAjC,EAGvBsC,IAHuB,EAA1B;;AAKA,oBAAIC,MAAMC,OAAN,CAAcH,WAAd,CAAJ,EAAgC;AAC5B,2BAAO,QAAKmC,IAAL,CAAU,eAAV,CAAP;AACH;;AAED;AACA,oBAAInC,YAAY/B,MAAZ,KAAuB,aAA3B,EAA0C;AACtC,2BAAO,QAAKkE,IAAL,CAAU,eAAV,CAAP;AACH;;AAED;AACA,sBAAM,QAAKjE,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC;AACnCW,wBAAI0H;AAD+B,iBAAjC,EAEHnD,MAFG,CAEI;AACNgE,4CAAwBF,gBADlB;AAENG,uCAAmBF,WAFb;AAGNG,sCAAkB3F,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAHZ;AAINjE,4BAAQ,UAJF;AAKN0I,gCAAY,IAAI1E,IAAJ;AALN,iBAFJ,CAAN;;AAUA+C,wBAAQC,GAAR,CAAY,cAAZ;AACA,uBAAO,QAAKpE,OAAL,CAAa;AAChB0E,6BAAS;AADO,iBAAb,CAAP;AAIH,aA3CD,CA2CE,OAAOC,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,QAAKrD,IAAL,CAAU,iBAAiBqD,MAAMD,OAAjC,CAAP;AACH;AApD+B;AAqDnC;AACD;;;;AAIMqC,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAM/F,UAAU,QAAK/D,GAAL,CAAS,SAAT,CAAhB;AACA;AACA,kBAAMiE,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,gBAAIsB,aAAa;AACb9E,8BAAc,GADD;AAEbyE,+BAAepB;AAFF,aAAjB;AAIA,kBAAMwC,aAAa,MAAM,QAAKrG,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC/CW,oBAAI2C;AAD2C,aAA1B,EAEtB4B,MAFsB,CAEfD,UAFe,CAAzB;AAGA,mBAAO,QAAK3C,OAAL,CAAa0D,UAAb,CAAP;AAXmB;AAYtB;AACD;;;;AAIMsD,gBAAN,GAAqB;AAAA;;AAAA;AACjB;AACN,kBAAMlK,SAAS,MAAM,QAAKC,cAAL,EAArB;AACM,kBAAMkK,YAAY,QAAK7D,IAAL,CAAU,WAAV,CAAlB;AACA,kBAAM8D,eAAe,QAAK9D,IAAL,CAAU,cAAV,CAArB;AACA,kBAAM+D,aAAa,QAAK/D,IAAL,CAAU,YAAV,CAAnB;AACA,kBAAMgE,eAAe,QAAKhE,IAAL,CAAU,cAAV,CAArB,CANiB,CAM+B;AAChD,gBAAIrB,aAAa,QAAKqB,IAAL,CAAU,YAAV,CAAjB;AACA,kBAAMiE,SAASrF,OAAOC,IAAP,CAAYF,UAAZ,CAAf,CARiB,CAQuB;AACxC,kBAAMuF,iBAAiB,MAAM,QAAKjK,KAAL,CAAW,SAAX,EAAsBK,KAAtB,CAA4B;AACrDW,oBAAI4I;AADiD,aAA5B,EAE1B7H,IAF0B,EAA7B;AAGA,gBAAIC,MAAMC,OAAN,CAAcgI,cAAd,CAAJ,EAAmC;AAC/B,uBAAO,QAAKhG,IAAL,CAAU,SAAV,CAAP;AACH;AACD;AACA,kBAAMiG,mBAAmB,MAAM,QAAKlK,KAAL,CAAW,MAAX,EAAmBK,KAAnB,CAAyB;AACpDC,yBAASb,MAD2C;AAEpDmG,yBAAS,CAF2C;AAGpD1F,2BAAW;AAHyC,aAAzB,EAI5Be,MAJ4B,EAA/B;AAKA,gBAAIe,MAAMC,OAAN,CAAciI,gBAAd,CAAJ,EAAqC;AACjC,uBAAO,QAAKjG,IAAL,CAAU,OAAV,CAAP;AACH;AACD,gBAAIkG,aAAa,CAAjB;AACA,gBAAIC,aAAa,CAAjB;AACA,iBAAI,MAAMxJ,IAAV,IAAkBsJ,gBAAlB,EAAmC;AAC/B,oBAAIG,UAAU,MAAM,QAAKrK,KAAL,CAAW,SAAX,EAAsBK,KAAtB,CAA4B;AAC5CW,wBAAGJ,KAAKuF;AADoC,iBAA5B,EAEjBpE,IAFiB,EAApB;AAGA,oBAAGnB,KAAKS,MAAL,GAAcgJ,QAAQC,YAAzB,EAAsC;AAClCF;AACH;;AAED;AACA,oBAAGxJ,KAAK2J,gBAAR,EAAyB;AACrB;AACA,0BAAMC,eAAe,MAAM,QAAKxK,KAAL,CAAW,wBAAX,EAAqCK,KAArC,CAA2C;AAClEoK,kCAAU7J,KAAK2J,gBADmD;AAElErE,kCAAUtF,KAAKsF;AAFmD,qBAA3C,EAGxBnE,IAHwB,EAA3B;;AAKA,wBAAG,CAACC,MAAMC,OAAN,CAAcuI,YAAd,CAAJ,EAAgC;AAC5B;AACA,4BAAIE,sBAAsBL,QAAQM,YAAlC;AACA,4BAAIH,aAAaI,cAAb,GAA8B,CAAlC,EAAqC;AACjC,kCAAMC,eAAeL,aAAaM,WAAb,GAA2BN,aAAaI,cAA7D;AACAF,kDAAsBK,KAAKC,KAAL,CAAWX,QAAQM,YAAR,GAAuBE,YAAvB,GAAsC,GAAjD,IAAwD,GAA9E;AACAH,kDAAsBK,KAAKE,GAAL,CAAS,IAAT,EAAeP,mBAAf,CAAtB;AACH,yBAJD,MAIO;AACHA,kDAAsBF,aAAaM,WAAnC;AACH;;AAED;AACA,4BAAGC,KAAKG,GAAL,CAAStK,KAAKuK,SAAL,GAAiBT,mBAA1B,IAAiD,IAApD,EAAyD;AACrD5D,oCAAQC,GAAR,CAAa,QAAOnG,KAAKsF,QAAS,kBAAiBtF,KAAKuK,SAAU,UAAST,mBAAoB,EAA/F;AACAP;AACH;AACJ,qBAhBD,MAgBO;AACH;AACArD,gCAAQC,GAAR,CAAa,QAAOnG,KAAKsF,QAAS,kBAAlC;AACAiE;AACH;AACJ,iBA5BD,MA4BO;AACH;AACA,wBAAGvJ,KAAK+J,YAAL,IAAqB/J,KAAKuK,SAA7B,EAAuC;AACnChB;AACH;AACJ;AACJ;AACD,gBAAGC,aAAa,CAAhB,EAAkB;AACd,uBAAO,QAAKnG,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;AACD,gBAAGkG,aAAa,CAAhB,EAAkB;AACd,uBAAO,QAAKlG,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACH;AACD;AACA;AACA;AACA,gBAAImH,kBAAkB,IAAtB;AACA,iBAAK,MAAMC,QAAX,IAAuBnB,gBAAvB,EAAyC;AACrCkB,mCAAmBC,SAAShK,MAAT,GAAkBgK,SAASV,YAA9C;AACH;AACD;AACA,gBAAIW,iBAAiB,CAArB;AACA,gBAAIC,qBAAqB,IAAzB;;AAEA,gBAAIxB,YAAJ,EAAkB;AACd;AACA,sBAAMyB,eAAe,MAAM,QAAKC,oBAAL,CAA0BhM,MAA1B,EAAkCsK,YAAlC,EAAgDqB,eAAhD,CAA3B;AACA,oBAAII,aAAa7I,OAAjB,EAA0B;AACtB2I,qCAAiBE,aAAaF,cAA9B;AACAC,yCAAqBC,aAAaE,UAAlC;AACH,iBAHD,MAGO;AACH,2BAAO,QAAKzH,IAAL,CAAUuH,aAAanE,OAAvB,CAAP;AACH;AACJ,aATD,MASO;AACH;AACA,sBAAMsE,mBAAmB,MAAM,QAAKC,oBAAL,CAA0BnM,MAA1B,EAAkC2L,eAAlC,CAA/B;AACA,oBAAIO,iBAAiBhJ,OAAjB,IAA4BgJ,iBAAiBD,UAAjD,EAA6D;AACzDJ,qCAAiBK,iBAAiBL,cAAlC;AACAC,yCAAqBI,iBAAiBD,UAAtC;AACH;AACJ;;AAED;AACA,kBAAMG,kBAAkBT,kBAAkBvB,YAA1C,CA1GiB,CA0GuC;AACxD,kBAAMiC,cAAcD,kBAAkBP,cAAtC,CA3GiB,CA2GqC;AACtD,kBAAMzH,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,gBAAI+H,aAAa,EAAjB;AACA,iBAAK,MAAMnL,IAAX,IAAmBsJ,gBAAnB,EAAqC;AACjC,oBAAI8B,IAAIC,OAAOrL,IAAP,IAAe,CAAvB;AACAmL,6BAAaA,aAAaC,CAAb,GAAiB,GAAjB,GAAuB9B,iBAAiBtJ,IAAjB,EAAuBsL,SAA9C,GAA0D,GAA1D,GAAgEhC,iBAAiBtJ,IAAjB,EAAuBS,MAAvF,GAAgG,IAA7G;AACH;AACD,gBAAI8K,MAAM,MAAM,QAAKnM,KAAL,CAAW,UAAX,EAAuBK,KAAvB,CAA6B;AACzCW,oBAAI;AADqC,aAA7B,EAEbe,IAFa,EAAhB;AAGA,gBAAIqK,cAAcD,IAAIE,IAAtB;AACA,gBAAIC,gBAAgBH,IAAII,GAAxB;AACA;AACA,gBAAIC,WAAW,MAAM,QAAKxM,KAAL,CAAW,MAAX,EAAmBK,KAAnB,CAAyB;AAC1CW,oBAAIvB;AADsC,aAAzB,EAElBsC,IAFkB,EAArB;AAGA;AACA;AACA,kBAAM0K,iBAAiB,QAAK1G,IAAL,CAAU,aAAV,KAA4B,CAAnD;AACA,gBAAI2G,aAAa,IAAjB;AACA,gBAAIC,uBAAuB,IAA3B;;AAEA;AACA,gBAAIF,iBAAiB,CAArB,EAAwB;AACpB3F,wBAAQC,GAAR,CAAY,kBAAZ,EAAgC0F,cAAhC,EAAgD,QAAhD,EAA0DhN,MAA1D;;AAEA;AACA,sBAAMmN,WAAW,MAAM,QAAK5M,KAAL,CAAW,oBAAX,EAAiCK,KAAjC,CAAuC;AAC1DC,6BAASmM;AADiD,iBAAvC,EAEpB1K,IAFoB,EAAvB;;AAIA,oBAAI,CAACC,MAAMC,OAAN,CAAc2K,QAAd,CAAL,EAA8B;AAC1BF,iCAAaE,SAAS5L,EAAtB;AACA8F,4BAAQC,GAAR,CAAY,UAAZ,EAAwB2F,UAAxB;;AAEA;AACA,0BAAM,QAAKG,yBAAL,CAA+BpN,MAA/B,EAAuCgN,cAAvC,CAAN;;AAEA;AACA;AACAE,2CAAuBC,SAASE,cAAhC;AACA,wBAAIH,oBAAJ,EAA0B;AACtB7F,gCAAQC,GAAR,CAAY,aAAZ,EAA2B4F,oBAA3B,EAAiD,SAAjD;AACH;AACD7F,4BAAQC,GAAR,CAAY,cAAZ,EAA4B0F,cAA5B,EAA4C,OAA5C,EAAqDE,oBAArD;AACH,iBAdD,MAcO;AACH7F,4BAAQC,GAAR,CAAY,kBAAZ;AACH;AACJ;;AAED,kBAAMnD,YAAY;AACdqE,0BAAU,QAAKjI,KAAL,CAAW,OAAX,EAAoB+M,mBAApB,EADI;AAEdzM,yBAASb,MAFK;AAGd;AACAuN,2BAAW/C,eAAegD,IAJZ;AAKdC,wBAAQjD,eAAeiD,MALT;AAMd/I,0BAAU8F,eAAekD,WANX;AAOd7I,sBAAM2F,eAAemD,OAPP;AAQd5I,0BAAUyF,eAAeoD,WARX;AASdC,yBAASrD,eAAeqD,OATV;AAUd9M,8BAAc,GAVA,EAUK;AACnB;AACA+M,+BAAe1D,YAZD;AAadnF,4BAAYsF,OAAOnF,QAAP,CAAgB,QAAhB,CAbE;AAcdvD,0BAAUuC,WAdI;AAed2J,6BAAapC,eAfC;AAgBdqC,6BAAa5B,eAhBC;AAiBd/D,8BAAcgE,WAjBA;AAkBd4B,8BAAc5B,WAlBA;AAmBdC,4BAAYA,UAnBE;AAoBd4B,6BAAa7D,UApBC;AAqBd;AACA8D,gCAAgBrC,qBAAqBA,mBAAmBvK,EAAxC,GAA6C,IAtB/C;AAuBd6M,iCAAiBvC,cAvBH;AAwBdwC,iCAAiBjC;AACjB;AACA;AACA;AA3Bc,aAAlB;AA6BA;AACA,kBAAMlI,UAAU,MAAM,QAAK3D,KAAL,CAAW,OAAX,EAAoB2I,GAApB,CAAwB/E,SAAxB,CAAtB;AACAA,sBAAU5C,EAAV,GAAe2C,OAAf;AACA,gBAAI,CAACA,OAAL,EAAc;AACV,uBAAO,QAAKM,IAAL,CAAU,QAAV,CAAP;AACH;AACD;AACA,kBAAM8J,iBAAiB,EAAvB;AACA,iBAAK,MAAMC,SAAX,IAAwB9D,gBAAxB,EAA0C;AACtC6D,+BAAerL,IAAf,CAAoB;AAChBpC,6BAASb,MADO;AAEhBsB,8BAAU4C,OAFM;AAGhBuC,8BAAU8H,UAAU9H,QAHJ;AAIhBC,gCAAY6H,UAAU7H,UAJN;AAKhB8H,gCAAYD,UAAUC,UALN;AAMhB/B,+BAAW8B,UAAU9B,SANL;AAOhBgC,kCAAcF,UAAUE,YAPR;AAQhBvD,kCAAcqD,UAAUrD,YARR;AAShBtJ,4BAAQ2M,UAAU3M,MATF;AAUhB8M,kDAA8BH,UAAUG,4BAVxB;AAWhBC,2CAAuBJ,UAAUI;AAXjB,iBAApB;AAaH;AACD,kBAAM,QAAKpO,KAAL,CAAW,aAAX,EAA0BqO,OAA1B,CAAkCN,cAAlC,CAAN;;AAEA;AACA,iBAAK,MAAMC,SAAX,IAAwB9D,gBAAxB,EAA0C;AACtC,oBAAI8D,UAAUzD,gBAAd,EAAgC;AAC5B;AACA,0BAAMC,eAAe,MAAM,QAAKxK,KAAL,CAAW,wBAAX,EAAqCK,KAArC,CAA2C;AAClEoK,kCAAUuD,UAAUzD,gBAD8C;AAElErE,kCAAU8H,UAAU9H;AAF8C,qBAA3C,EAGxBnE,IAHwB,EAA3B;;AAKA,wBAAI,CAACC,MAAMC,OAAN,CAAcuI,YAAd,CAAL,EAAkC;AAC9B;AACA,8BAAM,QAAKxK,KAAL,CAAW,mBAAX,EAAgC2I,GAAhC,CAAoC;AACtC8B,sCAAUuD,UAAUzD,gBADkB;AAEtC+D,4CAAgB9D,aAAaxJ,EAFS;AAGtCD,sCAAU4C,OAH4B;AAItCrD,qCAASb,MAJ6B;AAKtCyG,sCAAU8H,UAAU9H,QALkB;AAMtCqI,sCAAUP,UAAU3M,MANkB;AAOtCyJ,yCAAakD,UAAUrD,YAPe;AAQtC6D,0CAAcR,UAAUrD,YAAV,GAAyBqD,UAAU3M,MARX;AAStCmH,wCAAY,IAAIzE,IAAJ;AAT0B,yBAApC,CAAN;;AAYA;AACA,8BAAM,QAAK/D,KAAL,CAAW,wBAAX,EAAqCK,KAArC,CAA2C;AAC7CW,gCAAIwJ,aAAaxJ;AAD4B,yBAA3C,EAEHyN,SAFG,CAEO,OAFP,EAEgBT,UAAU3M,MAF1B,CAAN;;AAIA;AACA,8BAAM,QAAKrB,KAAL,CAAW,wBAAX,EAAqCK,KAArC,CAA2C;AAC7CW,gCAAIwJ,aAAaxJ;AAD4B,yBAA3C,EAEHoF,SAFG,CAEO,YAFP,EAEqB4H,UAAU3M,MAF/B,CAAN;;AAIAyF,gCAAQC,GAAR,CAAa,QAAOiH,UAAU9H,QAAS,SAAQ8H,UAAU3M,MAAO,UAAhE;AACH;AACJ;AACJ;;AAED;AACA,gBAAIkK,kBAAJ,EAAwB;AACpB,sBAAM,QAAKmD,gBAAL,CAAsBnD,kBAAtB,EAA0C5H,OAA1C,EAAmDkI,eAAnD,EAAoEP,cAApE,EAAoFQ,WAApF,CAAN;AACH;;AAED;AACA,gBAAIY,cAAcD,cAAlB,EAAkC;AAC9B,sBAAM,QAAKkC,oBAAL,CAA0BjC,UAA1B,EAAsCD,cAAtC,EAAsDhN,MAAtD,EAA8DkE,OAA9D,EAAuEuG,gBAAvE,EAAyF4B,WAAzF,CAAN;;AAEA;AACA,sBAAM,QAAK8C,4BAAL,CAAkCnC,cAAlC,EAAkD9I,OAAlD,EAA2DuG,gBAA3D,EAA6EyC,oBAA7E,CAAN;AACH;;AAED,kBAAM,QAAK3M,KAAL,CAAW,MAAX,EAAmB6O,aAAnB,EAAN;AACA,mBAAO,QAAKlM,OAAL,CAAa;AAChBiB,2BAAWA,SADK;AAEhB0H,gCAAgBA,cAFA;AAGhBwD,4BAAYvD,qBAAqB,IAArB,GAA4B;AAHxB,aAAb,CAAP;AAvQiB;AA4QpB;AACKwD,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMnF,YAAY,QAAK7D,IAAL,CAAU,WAAV,CAAlB;AACA,kBAAMpC,UAAU,QAAKoC,IAAL,CAAU,SAAV,CAAhB;AACA;AACA;AACA;AACA,kBAAMiJ,gBAAgB,MAAM,QAAKhP,KAAL,CAAW,SAAX,EAAsBK,KAAtB,CAA4B;AACpDW,oBAAI4I;AADgD,aAA5B,EAEzB7H,IAFyB,EAA5B;AAGA,kBAAM8B,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAMJ,YAAY;AACd;AACAoJ,2BAAWgC,cAAc/B,IAFX;AAGdC,wBAAQ8B,cAAc9B,MAHR;AAId/I,0BAAU6K,cAAc7B,WAJV;AAKd7I,sBAAM0K,cAAc5B,OALN;AAMd5I,0BAAUwK,cAAc3B,WANV;AAOdC,yBAAS0B,cAAc1B;AACvB;AACA;AACA;AACA;AACA;AAZc,aAAlB;AAcA,kBAAMhI,aAAa,MAAM,QAAKtF,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC/CW,oBAAI2C;AAD2C,aAA1B,EAEtB4B,MAFsB,CAEf3B,SAFe,CAAzB;AAGA,mBAAO,QAAKjB,OAAL,CAAa2C,UAAb,CAAP;AA3BiB;AA4BpB;;AAED;;;;;;;;;AASMqJ,wBAAN,CAA2BjC,UAA3B,EAAuCD,cAAvC,EAAuDwC,WAAvD,EAAoEtL,OAApE,EAA6E7C,SAA7E,EAAwFoO,WAAxF,EAAqG;AAAA;;AAAA;AACjG,gBAAI;AACApI,wBAAQC,GAAR,CAAY,WAAZ,EAAyB,EAAE2F,UAAF,EAAcD,cAAd,EAA8BwC,WAA9B,EAA2CtL,OAA3C,EAAoDuL,WAApD,EAAzB;;AAEA;AACA,sBAAMtL,YAAY,MAAM,QAAK5D,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B,EAAEW,IAAI2C,OAAN,EAA1B,EAA2C5B,IAA3C,EAAxB;;AAEA;AACA,qBAAK,MAAMoN,KAAX,IAAoBrO,SAApB,EAA+B;AAC3B;AACA,0BAAMsO,mBAAmB,MAAM,QAAKC,mBAAL,CAAyBF,MAAMjJ,QAA/B,CAA/B;AACA,0BAAMoJ,iBAAiBF,mBAAmBA,iBAAiBG,eAApC,GAAsD,GAA7E,CAH2B,CAGuD;AAClF,0BAAMC,mBAAmB,CAACL,MAAM3B,WAAN,GAAoB2B,MAAM9N,MAA1B,GAAmCiO,cAAnC,GAAoD,GAArD,EAA0DG,OAA1D,CAAkE,CAAlE,CAAzB;;AAEA;AACA,0BAAMC,eAAe,MAAM,QAAKC,eAAL,CAAqBV,WAArB,CAA3B;;AAEA,0BAAMW,qBAAqB;AACvBC,qCAAanD,UADU;AAEvBoD,0CAAkBrD,cAFK;AAGvBsD,uCAAed,WAHQ;AAIvBlO,kCAAU4C,OAJa;AAKvBsE,kCAAUrE,UAAUqE,QALG;AAMvB/B,kCAAUiJ,MAAMjJ,QANO;AAOvB+H,oCAAYkB,MAAMlB,UAPK;AAQvBT,qCAAa2B,MAAM3B,WARI;AASvBwC,sCAAcb,MAAM3B,WAAN,GAAoB2B,MAAM9N,MATjB;AAUvBkO,yCAAiBD,cAVM;AAWvBW,2CAAmBT,gBAXI;AAYvBU,sCAAc,OAZS,EAYA;AACvBnQ,gCAAQ,SAbe;AAcvBoQ,wCAAgBT,eAAe,CAAf,GAAmB,CAdZ;AAevBU,qCAAatM,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAfU;AAgBvBqM,qCAAavM,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAhBU,qBAA3B;;AAmBA,0BAAM,QAAKhE,KAAL,CAAW,kBAAX,EAA+B2I,GAA/B,CAAmCiH,kBAAnC,CAAN;AACA9I,4BAAQC,GAAR,CAAY,kBAAZ,EAAgCoI,MAAMjJ,QAAtC,EAAgD,KAAhD,EAAuDsJ,gBAAvD;AACH;;AAED;AACA,sBAAM,QAAKc,wBAAL,CAA8B5D,UAA9B,CAAN;AAEH,aA1CD,CA0CE,OAAOpF,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACH;AA7CgG;AA8CpG;;AAED;;;;AAIM+H,uBAAN,CAA0BkB,OAA1B,EAAmC;AAAA;;AAAA;AAC/B,gBAAI;AACA;AACA,oBAAIC,SAAS,MAAM,QAAKxQ,KAAL,CAAW,mBAAX,EAAgCK,KAAhC,CAAsC;AACrD6F,8BAAUqK,OAD2C;AAErDE,+BAAW;AAF0C,iBAAtC,EAGhB1O,IAHgB,EAAnB;;AAKA,oBAAIC,MAAMC,OAAN,CAAcuO,MAAd,CAAJ,EAA2B;AACvB;AACAA,6BAAS,MAAM,QAAKxQ,KAAL,CAAW,mBAAX,EAAgCK,KAAhC,CAAsC;AACjD6F,kCAAU,CADuC;AAEjDuK,mCAAW;AAFsC,qBAAtC,EAGZ1O,IAHY,EAAf;AAIH;;AAED,uBAAOyO,MAAP;AACH,aAhBD,CAgBE,OAAOlJ,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,IAAP;AACH;AApB8B;AAqBlC;;AAED;;;;AAIMqI,mBAAN,CAAsBlQ,MAAtB,EAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMiR,aAAa,MAAM,QAAK1Q,KAAL,CAAW,OAAX,EAAoBK,KAApB,CAA0B;AAC/CC,6BAASb,MADsC;AAE/Ce,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAFiC,CAEJ;AAFI,iBAA1B,EAGtBsC,KAHsB,EAAzB;;AAKA,uBAAO4N,eAAe,CAAtB;AACH,aAPD,CAOE,OAAOpJ,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,KAAP;AACH;AAXyB;AAY7B;;AAED;;;;AAIMgJ,4BAAN,CAA+B5D,UAA/B,EAA2C;AAAA;;AAAA;AACvC,gBAAI;AACA,sBAAM7I,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA,sBAAM,QAAKhE,KAAL,CAAW,oBAAX,EAAiCK,KAAjC,CAAuC;AACzCW,wBAAI0L;AADqC,iBAAvC,EAEHnH,MAFG,CAEI;AACNoL,kCAAc,CAAC,KAAD,EAAQ,kBAAR,CADR;AAENC,kCAAc,CAAC,KAAD,EAAQ,kBAAR,CAFR;AAGNP,iCAAaxM;AAHP,iBAFJ,CAAN;;AAQAiD,wBAAQC,GAAR,CAAY,aAAZ;AAEH,aAbD,CAaE,OAAOO,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACH;AAhBsC;AAiB1C;;AAED;;;;AAIMX,oCAAN,CAAuChD,OAAvC,EAAgD;AAAA;;AAAA;AAC5C,gBAAI;AACAmD,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBpD,OAArB;;AAEA;AACA,sBAAMkN,kBAAkB,MAAM,QAAK7Q,KAAL,CAAW,kBAAX,EAA+BK,KAA/B,CAAqC;AAC/DU,8BAAU4C,OADqD;AAE/D5D,4BAAQ,SAFuD,CAE5C;AAF4C,iBAArC,EAG3BkB,MAH2B,EAA9B;;AAKA,oBAAIe,MAAMC,OAAN,CAAc4O,eAAd,CAAJ,EAAoC;AAChC/J,4BAAQC,GAAR,CAAY,qBAAZ;AACA;AACH;;AAEDD,wBAAQC,GAAR,CAAa,MAAK8J,gBAAgBxN,MAAO,QAAzC;;AAEA;AACA,qBAAK,MAAMyN,cAAX,IAA6BD,eAA7B,EAA8C;AAC1C/J,4BAAQC,GAAR,CAAY,SAAZ,EAAuB+J,eAAe9P,EAAtC,EAA0C,MAA1C,EAAkD8P,eAAehB,gBAAjE;;AAEA;AACA,0BAAMiB,qBAAqB,MAAM,QAAK/Q,KAAL,CAAW,oBAAX,EAAiCK,KAAjC,CAAuC;AACpE6F,kCAAU4K,eAAe5K;AAD2C,qBAAvC,EAE9BnE,IAF8B,EAAjC;;AAIA,wBAAIC,MAAMC,OAAN,CAAc8O,kBAAd,KAAqCA,mBAAmBC,cAAnB,KAAsC,CAA/E,EAAkF;AAC9ElK,gCAAQC,GAAR,CAAY,qBAAZ;AACA;AACH;;AAED;AACA,0BAAMmI,cAAcxH,WAAWoJ,eAAed,YAA1B,CAApB;AACA,0BAAMiB,qBAAqB,CAAC/B,eAAe6B,mBAAmBG,aAAnB,IAAoC,CAAnD,IAAwD,GAAzD,EAA8DzB,OAA9D,CAAsE,CAAtE,CAA3B;AACA,0BAAM0B,mBAAmB,CAACjC,eAAe6B,mBAAmBK,WAAnB,IAAkC,CAAjD,IAAsD,GAAvD,EAA4D3B,OAA5D,CAAoE,CAApE,CAAzB;AACA,0BAAM4B,mBAAmB,CAACnC,eAAe6B,mBAAmBO,WAAnB,IAAkC,CAAjD,IAAsD,GAAvD,EAA4D7B,OAA5D,CAAoE,CAApE,CAAzB;AACA,0BAAM8B,uBAAuB,CAACrC,eAAe6B,mBAAmBS,gBAAnB,IAAuC,CAAtD,IAA2D,GAA5D,EAAiE/B,OAAjE,CAAyE,CAAzE,CAA7B;;AAEA3I,4BAAQC,GAAR,CAAa,SAAQmI,WAAY,WAAU+B,kBAAmB,GAA9D;;AAEA;AACA,0BAAMhK,oBAAoB,QAAKC,OAAL,CAAa,YAAb,CAA1B;;AAEA;AACA,wBAAIQ,WAAWuJ,kBAAX,IAAiC,CAArC,EAAwC;AACpC,8BAAMQ,SAAS,MAAMxK,kBAAkByK,gBAAlB,CACjBZ,eAAehB,gBADE,EAEjBpI,WAAWuJ,kBAAX,CAFiB,EAGjBtN,OAHiB,EAIjBmN,eAAe9P,EAJE,EAKhB,OAAM8P,eAAe7C,UAAW,MALhB,CAArB;AAOAnH,gCAAQC,GAAR,CAAa,kBAAiB+J,eAAehB,gBAAiB,OAAMmB,kBAAmB,GAAvF;AACH;;AAED;AACA,wBAAIH,eAAea,uBAAf,IAA0CjK,WAAWyJ,gBAAX,IAA+B,CAA7E,EAAgF;AAC5E,8BAAMM,SAAS,MAAMxK,kBAAkByK,gBAAlB,CACjBZ,eAAea,uBADE,EAEjBjK,WAAWyJ,gBAAX,CAFiB,EAGjBxN,OAHiB,EAIjBmN,eAAe9P,EAJE,EAKhB,SAAQ8P,eAAe7C,UAAW,QALlB,CAArB;AAOAnH,gCAAQC,GAAR,CAAa,oBAAmB+J,eAAea,uBAAwB,OAAMR,gBAAiB,GAA9F;AACH;;AAED;AACA,0BAAM,QAAKnR,KAAL,CAAW,kBAAX,EAA+BK,KAA/B,CAAqC;AACvCW,4BAAI8P,eAAe9P;AADoB,qBAArC,EAEHuE,MAFG,CAEI;AACNxF,gCAAQ,SADF;AAEN6R,qCAAa9N,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAFP;AAGNqM,qCAAavM,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAHP;AAIN6N,6CAAqBZ,kBAJf;AAKNa,2CAAmBX,gBALb;AAMNY,2CAAmBV,gBANb;AAONW,gDAAwBT;AAPlB,qBAFJ,CAAN;;AAYAzK,4BAAQC,GAAR,CAAa,SAAQ+J,eAAehB,gBAAiB,YAAWmB,kBAAmB,GAAnF;AACH;;AAEDnK,wBAAQC,GAAR,CAAY,oBAAZ;AAEH,aArFD,CAqFE,OAAOO,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA;AACH;AAzF2C;AA0F/C;;AAED;;;;;;AAMMuF,6BAAN,CAAgCoC,WAAhC,EAA6CxC,cAA7C,EAA6D;AAAA;;AAAA;AACzD,gBAAI;AACA3F,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,QAAZ,EAAsBkI,WAAtB,EAAmC,UAAnC,EAA+CxC,cAA/C;;AAEA;AACA,sBAAMwF,mBAAmB,MAAM,QAAKjS,KAAL,CAAW,oBAAX,EAAiCK,KAAjC,CAAuC;AAClEC,6BAAS2O;AADyD,iBAAvC,EAE5BlN,IAF4B,EAA/B;;AAIA,oBAAI,CAACC,MAAMC,OAAN,CAAcgQ,gBAAd,CAAL,EAAsC;AAClC,wBAAIA,iBAAiBnF,cAArB,EAAqC;AACjChG,gCAAQC,GAAR,CAAY,aAAZ,EAA2BkL,iBAAiBnF,cAA5C,EAA4D,QAA5D;AACAhG,gCAAQC,GAAR,CAAY,gBAAZ,EAA8B0F,cAA9B,EAA8C,aAA9C;AACA;AACH,qBAJD,MAIO;AACH;AACA,8BAAM,QAAKzM,KAAL,CAAW,oBAAX,EAAiCK,KAAjC,CAAuC;AACzCC,qCAAS2O;AADgC,yBAAvC,EAEH1J,MAFG,CAEI;AACNuH,4CAAgBL,cADV;AAEN4D,yCAAavM,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAFP,yBAFJ,CAAN;AAMA8C,gCAAQC,GAAR,CAAY,gBAAZ;AACH;AACJ,iBAfD,MAeO;AACH;AACA,0BAAMlD,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,0BAAMkO,eAAe;AACjB5R,iCAAS2O,WADQ;AAEjBnC,wCAAgBL,cAFC;AAGjB0F,+BAAO,CAHU;AAIjBC,qCAAa,CAJI;AAKjBzB,sCAAc,CALG;AAMjB0B,0CAAkB,IAND;AAOjBC,qCAAa,CAPI;AAQjB1B,sCAAc,CARG;AASjB2B,0CAAkB,IATD;AAUjBxS,gCAAQ,CAVS;AAWjByS,0CAAkB3O,WAXD;AAYjBuM,qCAAavM,WAZI;AAajBwM,qCAAaxM;AAbI,qBAArB;;AAgBA,0BAAM,QAAK7D,KAAL,CAAW,oBAAX,EAAiC2I,GAAjC,CAAqCuJ,YAArC,CAAN;AACApL,4BAAQC,GAAR,CAAY,kBAAZ;AACH;AAEJ,aA/CD,CA+CE,OAAOO,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACH;AAlDwD;AAmD5D;;AAID;;;;;;;AAOMsH,gCAAN,CAAmCnC,cAAnC,EAAmD9I,OAAnD,EAA4D7C,SAA5D,EAAuE6L,uBAAuB,IAA9F,EAAoG;AAAA;;AAAA;AAChG,gBAAI;AACA7F,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,UAAZ,EAAwB0F,cAAxB,EAAwC,OAAxC,EAAiD9I,OAAjD;;AAEA,sBAAMsD,oBAAoB,QAAKC,OAAL,CAAa,QAAb,CAA1B;;AAEA;AACA,qBAAK,MAAMiI,KAAX,IAAoBrO,SAApB,EAA+B;AAC3B,0BAAMoO,cAAcC,MAAM3B,WAAN,GAAoB2B,MAAM9N,MAA9C;;AAEAyF,4BAAQC,GAAR,CAAa,SAAQoI,MAAMlB,UAAW,SAAQiB,WAAY,GAA1D;;AAEA;AACA,0BAAMuC,SAAS,MAAMxK,kBAAkBwL,4BAAlB,CACjBhG,cADiB,EAEjB9I,OAFiB,EAGjBwL,MAAMjJ,QAHW,EAIjBgJ,WAJiB,EAKjBvC,oBALiB,EAMjB,aANiB,CAArB;;AASA,wBAAI8E,OAAO9O,OAAX,EAAoB;AAChBmE,gCAAQC,GAAR,CAAa,QAAOoI,MAAMlB,UAAW,cAAawD,OAAOiB,UAAW,GAApE;AACH,qBAFD,MAEO;AACH5L,gCAAQC,GAAR,CAAa,QAAOoI,MAAMlB,UAAW,cAAawD,OAAOpK,OAAQ,EAAjE;AACH;AACJ;;AAEDP,wBAAQC,GAAR,CAAY,oBAAZ;AAEH,aA/BD,CA+BE,OAAOO,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA;AACH;AAnC+F;AAoCnG;AACD;;;;AAIMqL,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM9O,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAML,UAAU,QAAK/D,GAAL,CAAS,SAAT,CAAhB;AACA,gBAAIgT,OAAO,MAAM,QAAK5S,KAAL,CAAW,eAAX,EAA4BK,KAA5B,CAAkC;AAC/CU,0BAAU4C;AADqC,aAAlC,EAEd5B,IAFc,EAAjB;AAGA,gBAAIC,MAAMC,OAAN,CAAc2Q,IAAd,CAAJ,EAAyB;AACrB,uBAAO,QAAK3O,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,kBAAM4O,cAAc,MAAM,QAAK7S,KAAL,CAAW,eAAX,EAA4BK,KAA5B,CAAkC;AACxDU,0BAAU4C;AAD8C,aAAlC,EAEvB5B,IAFuB,EAA1B;AAGA;AACA,gBAAI+Q,aAAaF,KAAKvC,WAAtB;AACA,gBAAI0C,MAAM,CAAClP,cAAciP,UAAf,IAA6B,EAAvC;AACA,gBAAIE,YAAYJ,KAAKI,SAArB;AACA,gBAAIA,aAAa,CAAjB,EAAoB;AAChB,uBAAO,QAAKrQ,OAAL,CAAakQ,WAAb,CAAP;AACH,aAFD,MAEO,IAAIC,cAAc,CAAd,IAAmBC,MAAM,EAA7B,EAAiC;AACpC,uBAAO,QAAKpQ,OAAL,CAAakQ,WAAb,CAAP;AACH,aAFM,MAEA;AACH,oBAAII,cAAcJ,YAAYK,YAA9B;AACA,oBAAIC,YAAYN,YAAYO,aAA5B;AACA,oBAAIC,kBAAkB,MAAM,QAAKC,cAAL,CAAoBL,WAApB,EAAiCE,SAAjC,CAA5B;AACA,oBAAII,iBAAiBF,gBAAgBE,cAArC;AACA,oBAAIC,gBAAgBH,gBAAgBP,UAApC;AACAU,gCAAgB1P,SAAS,IAAIC,IAAJ,CAASyP,aAAT,EAAwBxP,OAAxB,KAAoC,IAA7C,CAAhB;AACAuP,iCAAiB,MAAM,QAAKE,iBAAL,CAAuBF,cAAvB,CAAvB;AACA,oBAAIG,SAASL,gBAAgBK,MAA7B;AACA,oBAAIC,SAASN,gBAAgBO,IAA7B;AACAD,yBAAStL,KAAKC,SAAL,CAAeqL,MAAf,CAAT;AACA,oBAAIE,WAAW;AACXC,oCAAgBP,cADL;AAEXP,+BAAWU,MAFA;AAGXC,4BAAQA,MAHG;AAIXtD,iCAAamD;AAJF,iBAAf;AAMA,sBAAM,QAAKxT,KAAL,CAAW,eAAX,EAA4BK,KAA5B,CAAkC;AACpCU,8BAAU4C;AAD0B,iBAAlC,EAEH4B,MAFG,CAEIsO,QAFJ,CAAN;AAGA,oBAAIE,UAAU,MAAM,QAAK/T,KAAL,CAAW,eAAX,EAA4BK,KAA5B,CAAkC;AAClDU,8BAAU4C;AADwC,iBAAlC,EAEjB5B,IAFiB,EAApB;AAGA,uBAAO,QAAKY,OAAL,CAAaoR,OAAb,CAAP;AACH;AACD;AA7CkB;AA8CrB;AACKT,kBAAN,CAAqBL,WAArB,EAAkCE,SAAlC,EAA6C;AAAA;AAC/C,gBAAIa,UAAU,aAAYhS,MAAMwO,MAAN,CAAa,oBAAb,CAA1B;AACM,kBAAMyD,UAAU;AACZC,wBAAQ,KADI;AAEZC,qBAAK,gDAAgDhB,SAAhD,GAA4D,QAA5D,GAAuEF,WAFhE;AAGZmB,yBAAS;AACL,oCAAgB,iCADX;AAEL,qCAAiBJ;AAFZ;AAHG,aAAhB;AAQA,gBAAIK,cAAc,MAAMlV,GAAG8U,OAAH,CAAxB;AACAI,0BAAchM,KAAKQ,KAAL,CAAWwL,WAAX,CAAd;AACA,mBAAOA,YAAY5C,MAAnB;AAZyC;AAa5C;AACKgC,qBAAN,CAAwB1T,MAAxB,EAAgC;AAAA;AAC5B,gBAAIA,UAAU,CAAd,EAAiB;AACb,uBAAO,UAAP;AACH,aAFD,MAEO,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,KAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,MAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,KAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,oCAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,wCAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,MAAP;AACH;AAf2B;AAgB/B;;AAED;;;AAGM0L,wBAAN,CAA2BhM,MAA3B,EAAmCsK,YAAnC,EAAiDmF,WAAjD,EAA8D;AAAA;;AAAA;AAC1D,gBAAI;AACA,sBAAMxD,aAAa,MAAM,QAAK1L,KAAL,CAAW,cAAX,EAA2BsU,KAA3B,CAAiC,IAAjC,EACpBC,IADoB,CACf,kCADe,EAEpBlU,KAFoB,CAEd;AACH,6BAAS0J,YADN;AAEH,kCAActK,MAFX;AAGH,iCAAa,QAHV;AAIH,oCAAgB,CAAC,IAAD,EAAO,IAAIsE,IAAJ,EAAP,CAJb;AAKH,mCAAe;AALZ,iBAFc,EASpB3D,KAToB,CASd,WATc,EAUpB2B,IAVoB,EAAzB;;AAYA,oBAAIC,MAAMC,OAAN,CAAcyJ,UAAd,CAAJ,EAA+B;AAC3B,2BAAO,EAAE/I,SAAS,KAAX,EAAkB0E,SAAS,QAA3B,EAAP;AACH;;AAED;AACA,oBAAI6H,cAAcxD,WAAW8I,UAA7B,EAAyC;AACrC,2BAAO,EAAE7R,SAAS,KAAX,EAAkB0E,SAAU,SAAQqE,WAAW8I,UAAW,GAA1D,EAAP;AACH;;AAED;AACA,sBAAMlJ,iBAAiB,QAAKmJ,iBAAL,CAAuB/I,UAAvB,EAAmCwD,WAAnC,CAAvB;;AAEA,uBAAO;AACHvM,6BAAS,IADN;AAEH+I,gCAAYA,UAFT;AAGHJ,oCAAgBA;AAHb,iBAAP;AAKH,aA9BD,CA8BE,OAAOhE,KAAP,EAAc;AACZtF,sBAAM0S,MAAN,CAAapN,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,uBAAO,EAAE3E,SAAS,KAAX,EAAkB0E,SAAS,SAA3B,EAAP;AACH;AAlCyD;AAmC7D;;AAED;;;AAGMuE,wBAAN,CAA2BnM,MAA3B,EAAmCyP,WAAnC,EAAgD;AAAA;;AAAA;AAC5C,gBAAI;AACA,sBAAMyF,mBAAmB,MAAM,QAAK3U,KAAL,CAAW,cAAX,EAA2BsU,KAA3B,CAAiC,IAAjC,EAC1BC,IAD0B,CACrB,kCADqB,EAE1BlU,KAF0B,CAEpB;AACH,kCAAcZ,MADX;AAEH,iCAAa,QAFV;AAGH,oCAAgB,CAAC,IAAD,EAAO,IAAIsE,IAAJ,EAAP,CAHb;AAIH,oCAAgB,CAAC,IAAD,EAAOmL,WAAP,CAJb;AAKH,mCAAe;AALZ,iBAFoB,EAS1B9O,KAT0B,CASpB,WAToB,EAU1Ba,MAV0B,EAA/B;;AAYA,oBAAI0T,iBAAiBtR,MAAjB,KAA4B,CAAhC,EAAmC;AAC/B,2BAAO,EAAEV,SAAS,IAAX,EAAiB+I,YAAY,IAA7B,EAAmCJ,gBAAgB,CAAnD,EAAP;AACH;;AAED,oBAAIsJ,aAAa,IAAjB;AACA,oBAAIC,cAAc,CAAlB;;AAEA,qBAAK,IAAIC,MAAT,IAAmBH,gBAAnB,EAAqC;AACjC,0BAAMI,WAAW,QAAKN,iBAAL,CAAuBK,MAAvB,EAA+B5F,WAA/B,CAAjB;AACA,wBAAI6F,WAAWF,WAAf,EAA4B;AACxBA,sCAAcE,QAAd;AACAH,qCAAaE,MAAb;AACH;AACJ;;AAED,uBAAO;AACHnS,6BAAS,IADN;AAEH+I,gCAAYkJ,UAFT;AAGHtJ,oCAAgBuJ;AAHb,iBAAP;AAKH,aAjCD,CAiCE,OAAOvN,KAAP,EAAc;AACZtF,sBAAM0S,MAAN,CAAapN,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,uBAAO,EAAE3E,SAAS,KAAX,EAAkB0E,SAAS,WAA3B,EAAP;AACH;AArC2C;AAsC/C;;AAED;;;AAGAoN,sBAAkBK,MAAlB,EAA0BE,MAA1B,EAAkC;AAC9B,YAAIF,OAAOG,aAAP,KAAyB,OAA7B,EAAsC;AAClC,mBAAOlK,KAAKmK,GAAL,CAASJ,OAAOK,cAAhB,EAAgCH,MAAhC,CAAP;AACH,SAFD,MAEO;AACH,kBAAMD,WAAWC,UAAUF,OAAOK,cAAP,GAAwB,GAAlC,CAAjB;AACA,mBAAOpK,KAAKmK,GAAL,CAASH,QAAT,EAAmBD,OAAOM,YAAP,IAAuBL,QAA1C,CAAP;AACH;AACJ;;AAED;;;AAGMrG,oBAAN,CAAuBhD,UAAvB,EAAmC/H,OAAnC,EAA4C0R,cAA5C,EAA4D/J,cAA5D,EAA4EgK,WAA5E,EAAyF;AAAA;;AAAA;AACrF,gBAAI;AACA;AACA,sBAAM,QAAKtV,KAAL,CAAW,cAAX,EAA2BK,KAA3B,CAAiC,EAACW,IAAI0K,WAAW1K,EAAhB,EAAjC,EAAsDuE,MAAtD,CAA6D;AAC/DxF,4BAAQ,MADuD;AAE/DwV,6BAAS,IAAIxR,IAAJ,EAFsD;AAG/DhD,8BAAU4C;AAHqD,iBAA7D,CAAN;;AAMA;AACA,sBAAM,QAAK3D,KAAL,CAAW,mBAAX,EAAgC2I,GAAhC,CAAoC;AACtCrI,6BAASoL,WAAWpL,OADkB;AAEtCkV,+BAAW9J,WAAW8J,SAFgB;AAGtC5H,oCAAgBlC,WAAW1K,EAHW;AAItCD,8BAAU4C,OAJ4B;AAKtCmK,qCAAiBuH,cALqB;AAMtCxH,qCAAiBvC,cANqB;AAOtCmK,kCAAcH;AAPwB,iBAApC,CAAN;;AAUAxO,wBAAQC,GAAR,CAAY,UAAZ,EAAwB2E,WAAWgK,WAAnC,EAAgD,OAAhD,EAAyDpK,cAAzD;AACH,aApBD,CAoBE,OAAOhE,KAAP,EAAc;AACZtF,sBAAM0S,MAAN,CAAapN,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,sBAAMA,KAAN;AACH;AAxBoF;AAyBxF;AA9+C+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\order.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst rp = require('request-promise');\nconst fs = require('fs');\nconst http = require(\"http\");\nmodule.exports = class extends Base {\n    /**\n     * 获取订单列表\n     * @return {Promise} []\n     */\n    async listAction() {\n\t\tconst userId = await this.getLoginUserId();\n\t\tconst showType = this.get('showType');\n        const page = this.get('page');\n        const size = this.get('size');\n        let status = [];\n        status = await this.model('order').getOrderStatus(showType);\n        let is_delete = 0;\n        const orderList = await this.model('order').field('id,add_time,actual_price,freight_price,offline_pay').where({\n            user_id: userId,\n            is_delete: is_delete,\n            order_type: ['<', 7],\n            order_status: ['IN', status]\n        }).page(page, size).order('add_time DESC').countSelect();\n        const newOrderList = [];\n        for (const item of orderList.data) {\n            // 订单的商品\n            item.goodsList = await this.model('order_goods').field('id,goods_name,goods_aka,list_pic_url,number,retail_price,goods_specifition_name_value').where({\n                user_id: userId,\n                order_id: item.id,\n                is_delete: 0\n            }).select();\n            item.goodsCount = 0;\n            item.goodsList.forEach(v => {\n                item.goodsCount += v.number;\n            });\n            item.add_time = moment.unix(await this.model('order').getOrderAddTime(item.id)).format('YYYY-MM-DD HH:mm:ss');\n            // item.dealdone_time = moment.unix(await this.model('order').getOrderAddTime(item.id)).format('YYYY-MM-DD HH:mm:ss');\n            // item.add_time =this.timestampToTime(await this.model('order').getOrderAddTime(item.id));\n            // 订单状态的处理\n            item.order_status_text = await this.model('order').getOrderStatusText(item.id);\n            // 可操作的选项\n            item.handleOption = await this.model('order').getOrderHandleOption(item.id);\n\n            // 获取售后申请信息\n            let refundApply = await this.model('refund_apply').where({\n                order_id: item.id\n            }).find();\n\n            if (!think.isEmpty(refundApply)) {\n                // 订单列表只需要基本的售后状态信息\n                item.refundInfo = {\n                    status: refundApply.status,\n                    refund_type: refundApply.refund_type,\n                    status_text: this.getRefundStatusText(refundApply.status),\n                    type_text: this.getRefundTypeText(refundApply.refund_type)\n                };\n                item.hasRefund = true;\n\n                // 如果有售后申请，优先显示售后状态\n                item.display_status_text = item.refundInfo.type_text + item.refundInfo.status_text;\n            } else {\n                item.refundInfo = null;\n                item.hasRefund = false;\n                item.display_status_text = item.order_status_text;\n            }\n\n            newOrderList.push(item);\n        }\n        orderList.data = newOrderList;\n        return this.success(orderList);\n    }\n    // 获得订单数量\n    //\n    async countAction() {\n        const showType = this.get('showType');\n\t\tconst userId = await this.getLoginUserId();\n        let status = [];\n        status = await this.model('order').getOrderStatus(showType);\n        let is_delete = 0;\n        const allCount = await this.model('order').where({\n            user_id: userId,\n            is_delete: is_delete,\n            order_status: ['IN', status]\n        }).count('id');\n        return this.success({\n            allCount: allCount,\n        });\n    }\n    // 获得订单数量状态\n    //\n    async orderCountAction() {\n\t\t// const user_id = this.getLoginUserId();;\n\t\tconst user_id = await this.getLoginUserId();\n        if(user_id != 0){\n            let toPay = await this.model('order').where({\n                user_id: user_id,\n                is_delete: 0,\n                order_type: ['<', 7],\n                order_status: ['IN', '101,801']\n            }).count('id');\n            // 获取有售后申请的订单ID\n            const refundOrderIds = await this.model('refund_apply').where({\n                user_id: user_id\n            }).field('order_id').select();\n            const excludeOrderIds = refundOrderIds.map(item => item.order_id);\n\n            let toDeliveryCondition = {\n                user_id: user_id,\n                is_delete: 0,\n                order_type: ['<', 7],\n                order_status: ['IN', '201,300']  // 包括已付款201和待发货300状态\n            };\n\n            // 排除有售后申请的订单\n            if (excludeOrderIds.length > 0) {\n                toDeliveryCondition.id = ['NOT IN', excludeOrderIds];\n            }\n\n            let toDelivery = await this.model('order').where(toDeliveryCondition).count('id');\n            let toReceiveCondition = {\n                user_id: user_id,\n                order_type: ['<', 7],\n                is_delete: 0,\n                order_status: 301\n            };\n\n            // 排除有售后申请的订单\n            if (excludeOrderIds.length > 0) {\n                toReceiveCondition.id = ['NOT IN', excludeOrderIds];\n            }\n\n            let toReceive = await this.model('order').where(toReceiveCondition).count('id');\n            let newStatus = {\n                toPay: toPay,\n                toDelivery: toDelivery,\n                toReceive: toReceive,\n            }\n            return this.success(newStatus);\n        }\n       \n    }\n    async detailAction() {\n        const orderId = this.get('orderId');\n\t\tconst userId = await this.getLoginUserId();\n        const orderInfo = await this.model('order').where({\n            user_id: userId,\n            id: orderId\n        }).find();\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        if (think.isEmpty(orderInfo)) {\n            return this.fail('订单不存在');\n        }\n        orderInfo.province_name = await this.model('region').where({\n            id: orderInfo.province\n        }).getField('name', true);\n        orderInfo.city_name = await this.model('region').where({\n            id: orderInfo.city\n        }).getField('name', true);\n        orderInfo.district_name = await this.model('region').where({\n            id: orderInfo.district\n        }).getField('name', true);\n        orderInfo.full_region = orderInfo.province_name + orderInfo.city_name + orderInfo.district_name;\n        orderInfo.postscript = Buffer.from(orderInfo.postscript, 'base64').toString();\n        const orderGoods = await this.model('order_goods').where({\n            user_id: userId,\n            order_id: orderId,\n            is_delete: 0\n        }).select();\n        var goodsCount = 0;\n        for (const gitem of orderGoods) {\n            goodsCount += gitem.number;\n        }\n        // 订单状态的处理\n        orderInfo.order_status_text = await this.model('order').getOrderStatusText(orderId);\n        if (think.isEmpty(orderInfo.confirm_time)) {\n            orderInfo.confirm_time = 0;\n        } else orderInfo.confirm_time = moment.unix(orderInfo.confirm_time).format('YYYY-MM-DD HH:mm:ss');\n        if (think.isEmpty(orderInfo.dealdone_time)) {\n            orderInfo.dealdone_time = 0;\n        } else orderInfo.dealdone_time = moment.unix(orderInfo.dealdone_time).format('YYYY-MM-DD HH:mm:ss');\n        if (think.isEmpty(orderInfo.pay_time)) {\n            orderInfo.pay_time = 0;\n        } else orderInfo.pay_time = moment.unix(orderInfo.pay_time).format('YYYY-MM-DD HH:mm:ss');\n        if (think.isEmpty(orderInfo.shipping_time)) {\n            orderInfo.shipping_time = 0;\n        } else {\n            orderInfo.confirm_remainTime = orderInfo.shipping_time + 10 * 24 * 60 * 60;\n            orderInfo.shipping_time = moment.unix(orderInfo.shipping_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        // 订单支付倒计时\n        if (orderInfo.order_status === 101 || orderInfo.order_status === 801) {\n            // if (moment().subtract(60, 'minutes') < moment(orderInfo.add_time)) {\n            orderInfo.final_pay_time = orderInfo.add_time + 24 * 60 * 60; //支付倒计时2小时\n            if (orderInfo.final_pay_time < currentTime) {\n                //超过时间不支付，更新订单状态为取消\n                let updateInfo = {\n                    order_status: 102\n                };\n                await this.model('order').where({\n                    id: orderId\n                }).update(updateInfo);\n            }\n        }\n        orderInfo.add_time = moment.unix(orderInfo.add_time).format('YYYY-MM-DD HH:mm:ss');\n        orderInfo.order_status = '';\n        // 订单可操作的选择,删除，支付，收货，评论，退换货\n        const handleOption = await this.model('order').getOrderHandleOption(orderId);\n        const textCode = await this.model('order').getOrderTextCode(orderId);\n        return this.success({\n            orderInfo: orderInfo,\n            orderGoods: orderGoods,\n            handleOption: handleOption,\n            textCode: textCode,\n            goodsCount: goodsCount,\n        });\n    }\n    /**\n     * order 和 order-check 的goodslist\n     * @return {Promise} []\n     */\n    async orderGoodsAction() {\n\t\tconst userId = await this.getLoginUserId();\n        const orderId = this.get('orderId');\n        if (orderId > 0) {\n            const orderGoods = await this.model('order_goods').field('id,goods_name,goods_aka,list_pic_url,number,retail_price,goods_specifition_name_value,goods_id,product_id').where({\n                user_id: userId,\n                order_id: orderId,\n                is_delete: 0\n            }).select();\n            var goodsCount = 0;\n            for (const gitem of orderGoods) {\n                goodsCount += gitem.number;\n            }\n            return this.success(orderGoods);\n        } else {\n            const cartList = await this.model('cart').where({\n                user_id: userId,\n                checked:1,\n                is_delete: 0,\n                is_fast: 0,\n            }).select();\n            return this.success(cartList);\n        }\n    }\n    /**\n     * 取消订单\n     * @return {Promise} []\n     */\n    async cancelAction() {\n        const orderId = this.post('orderId');\n\t\tconst userId = await this.getLoginUserId();\n        // 检测是否能够取消\n        const handleOption = await this.model('order').getOrderHandleOption(orderId);\n        // console.log('--------------' + handleOption.cancel);\n        if (!handleOption.cancel) {\n            return this.fail('订单不能取消');\n        }\n        // 设置订单已取消状态\n        let updateInfo = {\n            order_status: 102\n        };\n        let orderInfo = await this.model('order').field('order_type').where({\n            id: orderId,\n            user_id: userId\n        }).find();\n        //取消订单，还原库存\n        const goodsInfo = await this.model('order_goods').where({\n            order_id: orderId,\n            user_id: userId\n        }).select();\n        for (const item of goodsInfo) {\n            let goods_id = item.goods_id;\n            let product_id = item.product_id;\n            let number = item.number;\n            await this.model('goods').where({\n                id: goods_id\n            }).increment('goods_number', number);\n            await this.model('product').where({\n                id: product_id\n            }).increment('goods_number', number);\n        }\n        const succesInfo = await this.model('order').where({\n            id: orderId\n        }).update(updateInfo);\n        return this.success(succesInfo);\n    }\n    /**\n     * 删除订单\n     * @return {Promise} []\n     */\n    async deleteAction() {\n        const orderId = this.post('orderId');\n        // 检测是否能够取消\n        const handleOption = await this.model('order').getOrderHandleOption(orderId);\n        if (!handleOption.delete) {\n            return this.fail('订单不能删除');\n        }\n        const succesInfo = await this.model('order').orderDeleteById(orderId);\n        return this.success(succesInfo);\n    }\n    /**\n     * 确认订单\n     * @return {Promise} []\n     */\n    async confirmAction() {\n        const orderId = this.post('orderId');\n        // 检测是否能够取消\n        const handleOption = await this.model('order').getOrderHandleOption(orderId);\n        if (!handleOption.confirm) {\n            return this.fail('订单不能确认');\n        }\n        // 设置订单已完成状态\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        let updateInfo = {\n            order_status: 401,\n            confirm_time: currentTime\n        };\n        const succesInfo = await this.model('order').where({\n            id: orderId\n        }).update(updateInfo);\n\n        // 确认收货后，处理推广佣金奖励\n        await this.handlePromotionCommissionRewards(orderId);\n\n        return this.success(succesInfo);\n    }\n\n    /**\n     * 订单退款处理\n     * @return {Promise} []\n     */\n    async refundAction() {\n        const orderId = this.post('orderId');\n        const refundReason = this.post('refundReason') || '用户申请退款';\n\n        try {\n            console.log('=== 处理订单退款 ===');\n            console.log('订单ID:', orderId, '退款原因:', refundReason);\n\n            // 1. 检查订单状态\n            const order = await this.model('order').where({ id: orderId }).find();\n            if (think.isEmpty(order)) {\n                return this.fail('订单不存在');\n            }\n\n            if (order.order_status === 203) {\n                return this.fail('订单已经退款');\n            }\n\n            // 2. 更新订单状态为已退款\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            await this.model('order').where({ id: orderId }).update({\n                order_status: 203, // 已退款\n                refund_time: currentTime\n            });\n\n            // 3. 处理佣金退款\n            const commissionService = this.service('commission');\n            const refundResult = await commissionService.handleRefundCommission(orderId);\n\n            if (refundResult.success) {\n                console.log('✅ 订单退款处理完成');\n                return this.success({\n                    message: '退款处理成功',\n                    refundResult: refundResult\n                });\n            } else {\n                console.log('❌ 佣金退款处理失败:', refundResult.message);\n                return this.fail('佣金退款处理失败: ' + refundResult.message);\n            }\n\n        } catch (error) {\n            console.error('订单退款处理失败:', error);\n            return this.fail('退款处理失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 申请售后\n     * @return {Promise} []\n     */\n    async refundApplyAction() {\n        const userId = await this.getLoginUserId();\n        const orderId = this.post('orderId');\n        const refundType = this.post('refundType') || 'refund_only'; // refund_only: 仅退款, return_refund: 退货退款\n        const refundAmount = parseFloat(this.post('refundAmount')) || 0;\n        const refundReason = this.post('refundReason') || '';\n        const refundDesc = this.post('refundDesc') || '';\n        const images = this.post('images') || [];\n\n        try {\n            console.log('=== 用户申请售后 ===');\n            console.log('用户ID:', userId, '订单ID:', orderId);\n            console.log('退款类型:', refundType, '退款金额:', refundAmount);\n\n            // 0. 验证用户登录\n            if (!userId) {\n                return this.fail('请先登录');\n            }\n\n            // 1. 验证订单\n            const order = await this.model('order').where({\n                id: orderId,\n                user_id: userId\n            }).find();\n\n            if (think.isEmpty(order)) {\n                return this.fail('订单不存在或无权限访问');\n            }\n\n            // 2. 检查订单状态是否可以申请售后\n            const handleOption = await this.model('order').getOrderHandleOption(orderId);\n            if (!handleOption.apply_refund) {\n                return this.fail('当前订单状态不支持申请售后');\n            }\n\n            // 3. 验证退款金额\n            if (refundAmount <= 0 || refundAmount > parseFloat(order.actual_price)) {\n                return this.fail('退款金额不正确');\n            }\n\n            // 4. 检查是否已有售后申请（同一订单不允许重复申请）\n            const existingApply = await this.model('refund_apply').where({\n                order_id: orderId\n            }).find();\n\n            if (!think.isEmpty(existingApply)) {\n                // 根据不同状态返回不同的错误信息\n                switch (existingApply.status) {\n                    case 'pending':\n                        return this.fail('该订单已有售后申请待处理，请耐心等待');\n                    case 'processing':\n                        return this.fail('该订单售后申请正在处理中，请耐心等待');\n                    case 'approved':\n                        return this.fail('该订单售后申请已通过，无需重复申请');\n                    case 'rejected':\n                        return this.fail('该订单售后申请已被拒绝，如有疑问请联系客服');\n                    case 'completed':\n                        return this.fail('该订单售后已完成，无需重复申请');\n                    default:\n                        return this.fail('该订单已有售后申请记录，无法重复申请');\n                }\n            }\n\n            // 5. 创建售后申请记录\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            const applyData = {\n                order_id: orderId,\n                user_id: userId,\n                order_sn: order.order_sn,\n                refund_type: refundType,\n                refund_amount: refundAmount,\n                refund_reason: refundReason,\n                refund_desc: refundDesc,\n                images: JSON.stringify(images),\n                status: 'pending', // pending: 待处理, processing: 处理中, approved: 已同意, rejected: 已拒绝, completed: 已完成\n                apply_time: currentTime,\n                created_at: new Date(),\n                updated_at: new Date()\n            };\n\n            const applyId = await this.model('refund_apply').add(applyData);\n\n            console.log('✅ 售后申请提交成功，申请ID:', applyId);\n            return this.success({\n                message: '售后申请提交成功，我们会尽快处理',\n                applyId: applyId\n            });\n\n        } catch (error) {\n            console.error('售后申请失败:', error);\n            return this.fail('售后申请失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 获取售后详情\n     * @return {Promise} []\n     */\n    async refundDetailAction() {\n        const userId = await this.getLoginUserId();\n        const applyId = this.get('applyId');\n\n        try {\n            console.log('=== 获取售后详情 ===');\n            console.log('用户ID:', userId, '申请ID:', applyId);\n\n            if (!userId) {\n                return this.fail('请先登录');\n            }\n\n            // 获取售后申请详情\n            const refundApply = await this.model('refund_apply').where({\n                id: applyId,\n                user_id: userId\n            }).find();\n\n            if (think.isEmpty(refundApply)) {\n                return this.fail('售后申请不存在或无权限访问');\n            }\n\n            // 获取订单信息\n            const orderInfo = await this.model('order').where({\n                id: refundApply.order_id\n            }).find();\n\n            // 获取订单商品\n            const orderGoods = await this.model('order_goods').where({\n                order_id: refundApply.order_id\n            }).select();\n\n            // 处理图片字段\n            if (refundApply.images) {\n                try {\n                    refundApply.images = JSON.parse(refundApply.images);\n                } catch (e) {\n                    refundApply.images = [];\n                }\n            } else {\n                refundApply.images = [];\n            }\n\n            // 格式化申请时间\n            refundApply.apply_time_text = new Date(refundApply.apply_time * 1000).toLocaleString();\n\n            console.log('✅ 售后详情获取成功');\n            return this.success({\n                refundInfo: refundApply,\n                orderInfo: orderInfo,\n                orderGoods: orderGoods\n            });\n\n        } catch (error) {\n            console.error('获取售后详情失败:', error);\n            return this.fail('获取售后详情失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 撤销售后申请\n     * @return {Promise} []\n     */\n    async refundCancelAction() {\n        const userId = await this.getLoginUserId();\n        const applyId = this.post('applyId');\n\n        try {\n            console.log('=== 撤销售后申请 ===');\n            console.log('用户ID:', userId, '申请ID:', applyId);\n\n            if (!userId) {\n                return this.fail('请先登录');\n            }\n\n            // 检查售后申请\n            const refundApply = await this.model('refund_apply').where({\n                id: applyId,\n                user_id: userId\n            }).find();\n\n            if (think.isEmpty(refundApply)) {\n                return this.fail('售后申请不存在或无权限访问');\n            }\n\n            // 只有待处理状态可以撤销\n            if (refundApply.status !== 'pending') {\n                return this.fail('当前状态不支持撤销操作');\n            }\n\n            // 删除售后申请记录\n            await this.model('refund_apply').where({\n                id: applyId\n            }).delete();\n\n            console.log('✅ 售后申请撤销成功');\n            return this.success({\n                message: '售后申请已撤销'\n            });\n\n        } catch (error) {\n            console.error('撤销售后申请失败:', error);\n            return this.fail('撤销售后申请失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 获取售后状态文本\n     */\n    getRefundStatusText(status) {\n        const statusMap = {\n            'pending': '待处理',\n            'processing': '处理中',\n            'approved': '已同意',\n            'rejected': '已拒绝',\n            'wait_return': '等待退货',\n            'returned': '已退货',\n            'completed': '已完成'\n        };\n        return statusMap[status] || '未知状态';\n    }\n\n    /**\n     * 获取退款类型文本\n     */\n    getRefundTypeText(refundType) {\n        const typeMap = {\n            'refund_only': '仅退款',\n            'return_refund': '退货退款'\n        };\n        return typeMap[refundType] || '售后';\n    }\n\n    /**\n     * 用户填写退货物流信息\n     * @return {Promise} []\n     */\n    async submitReturnLogisticsAction() {\n        const userId = await this.getLoginUserId();\n        const applyId = this.post('applyId');\n        const logisticsCompany = this.post('logisticsCompany');\n        const logisticsNo = this.post('logisticsNo');\n\n        try {\n            console.log('=== 用户提交退货物流信息 ===');\n            console.log('用户ID:', userId, '申请ID:', applyId);\n\n            if (!userId) {\n                return this.fail('请先登录');\n            }\n\n            if (!logisticsCompany || !logisticsNo) {\n                return this.fail('请填写完整的物流信息');\n            }\n\n            // 检查售后申请\n            const refundApply = await this.model('refund_apply').where({\n                id: applyId,\n                user_id: userId\n            }).find();\n\n            if (think.isEmpty(refundApply)) {\n                return this.fail('售后申请不存在或无权限访问');\n            }\n\n            // 只有等待退货状态可以填写物流信息\n            if (refundApply.status !== 'wait_return') {\n                return this.fail('当前状态不支持填写物流信息');\n            }\n\n            // 更新物流信息\n            await this.model('refund_apply').where({\n                id: applyId\n            }).update({\n                user_logistics_company: logisticsCompany,\n                user_logistics_no: logisticsNo,\n                user_return_time: parseInt(new Date().getTime() / 1000),\n                status: 'returned',\n                updated_at: new Date()\n            });\n\n            console.log('✅ 退货物流信息提交成功');\n            return this.success({\n                message: '退货物流信息提交成功，请等待商家确认收货'\n            });\n\n        } catch (error) {\n            console.error('提交退货物流信息失败:', error);\n            return this.fail('提交退货物流信息失败: ' + error.message);\n        }\n    }\n    /**\n     * 完成评论后的订单\n     * @return {Promise} []\n     */\n    async completeAction() {\n        const orderId = this.get('orderId');\n        // 设置订单已完成\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        let updateInfo = {\n            order_status: 401,\n            dealdone_time: currentTime\n        };\n        const succesInfo = await this.model('order').where({\n            id: orderId\n        }).update(updateInfo);\n        return this.success(succesInfo);\n    }\n    /**\n     * 提交订单\n     * @returns {Promise.<void>}\n     */\n    async submitAction() {\n        // 获取收货地址信息和计算运费\n\t\tconst userId = await this.getLoginUserId();\n        const addressId = this.post('addressId');\n        const freightPrice = this.post('freightPrice');\n        const offlinePay = this.post('offlinePay');\n        const userCouponId = this.post('userCouponId'); // 用户选择的优惠券ID\n        let postscript = this.post('postscript');\n        const buffer = Buffer.from(postscript); // 留言\n        const checkedAddress = await this.model('address').where({\n            id: addressId\n        }).find();\n        if (think.isEmpty(checkedAddress)) {\n            return this.fail('请选择收货地址');\n        }\n        // 获取要购买的商品\n        const checkedGoodsList = await this.model('cart').where({\n            user_id: userId,\n            checked: 1,\n            is_delete: 0\n        }).select();\n        if (think.isEmpty(checkedGoodsList)) {\n            return this.fail('请选择商品');\n        }\n        let checkPrice = 0;\n        let checkStock = 0;\n        for(const item of checkedGoodsList){\n            let product = await this.model('product').where({\n                id:item.product_id\n            }).find();\n            if(item.number > product.goods_number){\n                checkStock++;\n            }\n\n            // 对于秒杀商品，需要验证秒杀价格是否发生变化\n            if(item.seckill_round_id){\n                // 查询当前秒杀商品信息\n                const seckillGoods = await this.model('flash_sale_round_goods').where({\n                    round_id: item.seckill_round_id,\n                    goods_id: item.goods_id\n                }).find();\n\n                if(!think.isEmpty(seckillGoods)){\n                    // 计算当前规格的秒杀价格\n                    let currentSeckillPrice = product.retail_price;\n                    if (seckillGoods.original_price > 0) {\n                        const discountRate = seckillGoods.flash_price / seckillGoods.original_price;\n                        currentSeckillPrice = Math.round(product.retail_price * discountRate * 100) / 100;\n                        currentSeckillPrice = Math.max(0.01, currentSeckillPrice);\n                    } else {\n                        currentSeckillPrice = seckillGoods.flash_price;\n                    }\n\n                    // 验证秒杀价格是否发生变化（允许0.01的误差）\n                    if(Math.abs(item.add_price - currentSeckillPrice) > 0.01){\n                        console.log(`秒杀商品 ${item.goods_id} 价格发生变化: 购物车价格=${item.add_price}, 当前价格=${currentSeckillPrice}`);\n                        checkPrice++;\n                    }\n                } else {\n                    // 秒杀商品信息不存在，说明秒杀已结束\n                    console.log(`秒杀商品 ${item.goods_id} 轮次信息不存在，秒杀可能已结束`);\n                    checkPrice++;\n                }\n            } else {\n                // 普通商品验证原价\n                if(item.retail_price != item.add_price){\n                    checkPrice++;\n                }\n            }\n        }\n        if(checkStock > 0){\n            return this.fail(400, '库存不足，请重新下单');\n        }\n        if(checkPrice > 0){\n            return this.fail(400, '价格发生变化，请重新下单');\n        }\n        // 获取订单使用的红包\n        // 如果有用红包，则将红包的数量减少，当减到0时，将该条红包删除\n        // 统计商品总价\n        let goodsTotalPrice = 0.00;\n        for (const cartItem of checkedGoodsList) {\n            goodsTotalPrice += cartItem.number * cartItem.retail_price;\n        }\n        // 处理优惠券\n        let discountAmount = 0;\n        let selectedUserCoupon = null;\n\n        if (userCouponId) {\n            // 验证并使用指定的优惠券\n            const couponResult = await this.validateAndUseCoupon(userId, userCouponId, goodsTotalPrice);\n            if (couponResult.success) {\n                discountAmount = couponResult.discountAmount;\n                selectedUserCoupon = couponResult.userCoupon;\n            } else {\n                return this.fail(couponResult.message);\n            }\n        } else {\n            // 自动选择最优优惠券\n            const bestCouponResult = await this.autoSelectBestCoupon(userId, goodsTotalPrice);\n            if (bestCouponResult.success && bestCouponResult.userCoupon) {\n                discountAmount = bestCouponResult.discountAmount;\n                selectedUserCoupon = bestCouponResult.userCoupon;\n            }\n        }\n\n        // 订单价格计算\n        const orderTotalPrice = goodsTotalPrice + freightPrice; // 订单的总价\n        const actualPrice = orderTotalPrice - discountAmount; // 减去优惠券优惠后的实际支付金额\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        let print_info = '';\n        for (const item in checkedGoodsList) {\n            let i = Number(item) + 1;\n            print_info = print_info + i + '、' + checkedGoodsList[item].goods_aka + '【' + checkedGoodsList[item].number + '】 ';\n        }\n        let def = await this.model('settings').where({\n            id: 1\n        }).find();\n        let sender_name = def.Name;\n        let sender_mobile = def.Tel;\n        // let sender_address = '';\n        let userInfo = await this.model('user').where({\n            id: userId\n        }).find();\n        // const checkedAddress = await this.model('address').where({id: addressId}).find();\n        // 获取推广员信息（从前端传递或本地存储）\n        const promoterUserId = this.post('shareUserId') || 0;\n        let promoterId = null;\n        let parentPromoterUserId = null;\n\n        // 如果有推广员，处理推广员关系\n        if (promoterUserId > 0) {\n            console.log('检测到推广下单，推广员用户ID:', promoterUserId, '下单人ID:', userId);\n\n            // 查找推广员记录\n            const promoter = await this.model('personal_promoters').where({\n                user_id: promoterUserId\n            }).find();\n\n            if (!think.isEmpty(promoter)) {\n                promoterId = promoter.id;\n                console.log('找到推广员ID:', promoterId);\n\n                // 建立推广员关系：下单人成为推广员的下级（如果还没有上级的话）\n                await this.establishPromoterRelation(userId, promoterUserId);\n\n                // 查找当前分享者的上级（用于二级佣金）\n                // 注意：佣金基于当前分享者，与购买者的上级关系无关\n                parentPromoterUserId = promoter.parent_user_id;\n                if (parentPromoterUserId) {\n                    console.log('找到当前分享者的上级:', parentPromoterUserId, '将获得二级佣金');\n                }\n                console.log('佣金将发放给当前分享者:', promoterUserId, '和其上级:', parentPromoterUserId);\n            } else {\n                console.log('推广员记录不存在，可能需要先创建');\n            }\n        }\n\n        const orderInfo = {\n            order_sn: this.model('order').generateOrderNumber(),\n            user_id: userId,\n            // 收货地址和运费\n            consignee: checkedAddress.name,\n            mobile: checkedAddress.mobile,\n            province: checkedAddress.province_id,\n            city: checkedAddress.city_id,\n            district: checkedAddress.district_id,\n            address: checkedAddress.address,\n            order_status: 101, // 订单初始状态为 101\n            // 根据城市得到运费，这里需要建立表：所在城市的具体运费\n            freight_price: freightPrice,\n            postscript: buffer.toString('base64'),\n            add_time: currentTime,\n            goods_price: goodsTotalPrice,\n            order_price: orderTotalPrice,\n            actual_price: actualPrice,\n            change_price: actualPrice,\n            print_info: print_info,\n            offline_pay: offlinePay,\n            // 优惠券相关字段\n            user_coupon_id: selectedUserCoupon ? selectedUserCoupon.id : null,\n            discount_amount: discountAmount,\n            original_amount: orderTotalPrice,\n            // 推广相关字段（如果需要在订单表中记录）\n            // promoter_id: promoterId,\n            // promoter_user_id: promoterUserId > 0 ? promoterUserId : null\n        };\n        // 开启事务，插入订单信息和订单商品\n        const orderId = await this.model('order').add(orderInfo);\n        orderInfo.id = orderId;\n        if (!orderId) {\n            return this.fail('订单提交失败');\n        }\n        // 将商品信息录入数据库\n        const orderGoodsData = [];\n        for (const goodsItem of checkedGoodsList) {\n            orderGoodsData.push({\n                user_id: userId,\n                order_id: orderId,\n                goods_id: goodsItem.goods_id,\n                product_id: goodsItem.product_id,\n                goods_name: goodsItem.goods_name,\n                goods_aka: goodsItem.goods_aka,\n                list_pic_url: goodsItem.list_pic_url,\n                retail_price: goodsItem.retail_price,\n                number: goodsItem.number,\n                goods_specifition_name_value: goodsItem.goods_specifition_name_value,\n                goods_specifition_ids: goodsItem.goods_specifition_ids\n            });\n        }\n        await this.model('order_goods').addMany(orderGoodsData);\n\n        // 处理秒杀商品，创建秒杀订单记录\n        for (const goodsItem of checkedGoodsList) {\n            if (goodsItem.seckill_round_id) {\n                // 查询秒杀轮次商品信息\n                const seckillGoods = await this.model('flash_sale_round_goods').where({\n                    round_id: goodsItem.seckill_round_id,\n                    goods_id: goodsItem.goods_id\n                }).find();\n\n                if (!think.isEmpty(seckillGoods)) {\n                    // 创建秒杀订单记录\n                    await this.model('flash_sale_orders').add({\n                        round_id: goodsItem.seckill_round_id,\n                        round_goods_id: seckillGoods.id,\n                        order_id: orderId,\n                        user_id: userId,\n                        goods_id: goodsItem.goods_id,\n                        quantity: goodsItem.number,\n                        flash_price: goodsItem.retail_price,\n                        total_amount: goodsItem.retail_price * goodsItem.number,\n                        created_at: new Date()\n                    });\n\n                    // 减少秒杀库存\n                    await this.model('flash_sale_round_goods').where({\n                        id: seckillGoods.id\n                    }).decrement('stock', goodsItem.number);\n\n                    // 增加已售数量\n                    await this.model('flash_sale_round_goods').where({\n                        id: seckillGoods.id\n                    }).increment('sold_count', goodsItem.number);\n\n                    console.log(`秒杀商品 ${goodsItem.goods_id} 库存减少 ${goodsItem.number}，订单记录已创建`);\n                }\n            }\n        }\n\n        // 如果使用了优惠券，标记为已使用并记录使用日志\n        if (selectedUserCoupon) {\n            await this.markCouponAsUsed(selectedUserCoupon, orderId, orderTotalPrice, discountAmount, actualPrice);\n        }\n\n        // 如果是推广订单，创建推广订单记录（分享有礼佣金等确认收货后发放）\n        if (promoterId && promoterUserId) {\n            await this.createPromotionOrder(promoterId, promoterUserId, userId, orderId, checkedGoodsList, actualPrice);\n\n            // 记录分享有礼佣金（等确认收货后发放）\n            await this.recordShareCommissionRewards(promoterUserId, orderId, checkedGoodsList, parentPromoterUserId);\n        }\n\n        await this.model('cart').clearBuyGoods();\n        return this.success({\n            orderInfo: orderInfo,\n            discountAmount: discountAmount,\n            couponUsed: selectedUserCoupon ? true : false\n        });\n    }\n    async updateAction() {\n        const addressId = this.post('addressId');\n        const orderId = this.post('orderId');\n        // 备注\n        // let postscript = this.post('postscript');\n        // const buffer = Buffer.from(postscript);\n        const updateAddress = await this.model('address').where({\n            id: addressId\n        }).find();\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const orderInfo = {\n            // 收货地址和运费\n            consignee: updateAddress.name,\n            mobile: updateAddress.mobile,\n            province: updateAddress.province_id,\n            city: updateAddress.city_id,\n            district: updateAddress.district_id,\n            address: updateAddress.address,\n            // TODO 根据地址计算运费\n            // freight_price: 0.00,\n            // 备注\n            // postscript: buffer.toString('base64'),\n            // add_time: currentTime\n        };\n        const updateInfo = await this.model('order').where({\n            id: orderId\n        }).update(orderInfo);\n        return this.success(updateInfo);\n    }\n\n    /**\n     * 创建推广订单记录\n     * @param {number} promoterId 推广员ID\n     * @param {number} promoterUserId 推广员用户ID\n     * @param {number} buyerUserId 购买者用户ID\n     * @param {number} orderId 订单ID\n     * @param {array} goodsList 商品列表\n     * @param {number} orderAmount 订单金额\n     */\n    async createPromotionOrder(promoterId, promoterUserId, buyerUserId, orderId, goodsList, orderAmount) {\n        try {\n            console.log('创建推广订单记录:', { promoterId, promoterUserId, buyerUserId, orderId, orderAmount });\n\n            // 获取订单信息\n            const orderInfo = await this.model('order').where({ id: orderId }).find();\n\n            // 为每个商品创建推广订单记录\n            for (const goods of goodsList) {\n                // 获取佣金配置\n                const commissionConfig = await this.getCommissionConfig(goods.goods_id);\n                const commissionRate = commissionConfig ? commissionConfig.commission_rate : 5.0; // 默认5%\n                const commissionAmount = (goods.goods_price * goods.number * commissionRate / 100).toFixed(2);\n\n                // 检查是否是首次购买\n                const isFirstOrder = await this.checkFirstOrder(buyerUserId);\n\n                const promotionOrderData = {\n                    promoter_id: promoterId,\n                    promoter_user_id: promoterUserId,\n                    buyer_user_id: buyerUserId,\n                    order_id: orderId,\n                    order_sn: orderInfo.order_sn,\n                    goods_id: goods.goods_id,\n                    goods_name: goods.goods_name,\n                    goods_price: goods.goods_price,\n                    order_amount: goods.goods_price * goods.number,\n                    commission_rate: commissionRate,\n                    commission_amount: commissionAmount,\n                    share_source: 'other', // 默认为其他来源\n                    status: 'pending',\n                    is_first_order: isFirstOrder ? 1 : 0,\n                    create_time: parseInt(new Date().getTime() / 1000),\n                    update_time: parseInt(new Date().getTime() / 1000)\n                };\n\n                await this.model('promotion_orders').add(promotionOrderData);\n                console.log('推广订单记录创建成功，商品ID:', goods.goods_id, '佣金:', commissionAmount);\n            }\n\n            // 更新推广员统计\n            await this.updatePromoterOrderStats(promoterId);\n\n        } catch (error) {\n            console.error('创建推广订单记录失败:', error);\n        }\n    }\n\n    /**\n     * 获取佣金配置\n     * @param {number} goodsId 商品ID\n     */\n    async getCommissionConfig(goodsId) {\n        try {\n            // 先查找商品特定配置\n            let config = await this.model('commission_config').where({\n                goods_id: goodsId,\n                is_active: 1\n            }).find();\n\n            if (think.isEmpty(config)) {\n                // 查找分类配置或全局配置\n                config = await this.model('commission_config').where({\n                    goods_id: 0,\n                    is_active: 1\n                }).find();\n            }\n\n            return config;\n        } catch (error) {\n            console.error('获取佣金配置失败:', error);\n            return null;\n        }\n    }\n\n    /**\n     * 检查是否首次购买\n     * @param {number} userId 用户ID\n     */\n    async checkFirstOrder(userId) {\n        try {\n            const orderCount = await this.model('order').where({\n                user_id: userId,\n                order_status: ['in', [201, 301, 302, 401]] // 已付款及以上状态\n            }).count();\n\n            return orderCount === 0;\n        } catch (error) {\n            console.error('检查首次购买失败:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 更新推广员订单统计\n     * @param {number} promoterId 推广员ID\n     */\n    async updatePromoterOrderStats(promoterId) {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n\n            await this.model('personal_promoters').where({\n                id: promoterId\n            }).update({\n                total_orders: ['exp', 'total_orders + 1'],\n                month_orders: ['exp', 'month_orders + 1'],\n                update_time: currentTime\n            });\n\n            console.log('推广员订单统计更新成功');\n\n        } catch (error) {\n            console.error('更新推广员订单统计失败:', error);\n        }\n    }\n\n    /**\n     * 处理推广佣金奖励（确认收货时调用）\n     * @param {number} orderId 订单ID\n     */\n    async handlePromotionCommissionRewards(orderId) {\n        try {\n            console.log('=== 开始处理推广佣金奖励 ===');\n            console.log('订单ID:', orderId);\n\n            // 查找该订单的推广记录\n            const promotionOrders = await this.model('promotion_orders').where({\n                order_id: orderId,\n                status: 'pending'  // 只处理待结算的推广订单\n            }).select();\n\n            if (think.isEmpty(promotionOrders)) {\n                console.log('该订单无推广记录或已结算，跳过佣金奖励');\n                return;\n            }\n\n            console.log(`找到 ${promotionOrders.length} 条推广记录`);\n\n            // 处理每个推广记录\n            for (const promotionOrder of promotionOrders) {\n                console.log('处理推广记录:', promotionOrder.id, '推广员:', promotionOrder.promoter_user_id);\n\n                // 获取商品的分销配置\n                const distributionConfig = await this.model('goods_distribution').where({\n                    goods_id: promotionOrder.goods_id\n                }).find();\n\n                if (think.isEmpty(distributionConfig) || distributionConfig.is_distributed !== 1) {\n                    console.log('商品未在分销池中或已移出，跳过佣金发放');\n                    continue;\n                }\n\n                // 计算各级佣金\n                const orderAmount = parseFloat(promotionOrder.order_amount);\n                const personalCommission = (orderAmount * (distributionConfig.personal_rate || 0) / 100).toFixed(2);\n                const level1Commission = (orderAmount * (distributionConfig.level1_rate || 0) / 100).toFixed(2);\n                const level2Commission = (orderAmount * (distributionConfig.level2_rate || 0) / 100).toFixed(2);\n                const teamLeaderCommission = (orderAmount * (distributionConfig.team_leader_rate || 0) / 100).toFixed(2);\n\n                console.log(`订单金额: ${orderAmount}元，个人佣金: ${personalCommission}元`);\n\n                // 使用佣金服务发放佣金\n                const commissionService = this.service('commission');\n\n                // 发放当前分享者佣金（一级佣金）\n                if (parseFloat(personalCommission) > 0) {\n                    const result = await commissionService.settleCommission(\n                        promotionOrder.promoter_user_id,\n                        parseFloat(personalCommission),\n                        orderId,\n                        promotionOrder.id,\n                        `分享商品${promotionOrder.goods_name}获得佣金`\n                    );\n                    console.log(`✅ 分享者佣金发放成功: 用户${promotionOrder.promoter_user_id} 获得 ${personalCommission}元`);\n                }\n\n                // 发放分享者的上级佣金（二级佣金）\n                if (promotionOrder.parent_promoter_user_id && parseFloat(level1Commission) > 0) {\n                    const result = await commissionService.settleCommission(\n                        promotionOrder.parent_promoter_user_id,\n                        parseFloat(level1Commission),\n                        orderId,\n                        promotionOrder.id,\n                        `下级分享商品${promotionOrder.goods_name}获得上级佣金`\n                    );\n                    console.log(`✅ 分享者上级佣金发放成功: 用户${promotionOrder.parent_promoter_user_id} 获得 ${level1Commission}元`);\n                }\n\n                // 更新推广订单状态为已结算\n                await this.model('promotion_orders').where({\n                    id: promotionOrder.id\n                }).update({\n                    status: 'settled',\n                    settle_time: parseInt(new Date().getTime() / 1000),\n                    update_time: parseInt(new Date().getTime() / 1000),\n                    personal_commission: personalCommission,\n                    level1_commission: level1Commission,\n                    level2_commission: level2Commission,\n                    team_leader_commission: teamLeaderCommission\n                });\n\n                console.log(`✅ 推广员 ${promotionOrder.promoter_user_id} 佣金发放成功: ${personalCommission}元`);\n            }\n\n            console.log('=== 推广佣金奖励处理完成 ===');\n\n        } catch (error) {\n            console.error('处理推广佣金奖励失败:', error);\n            // 不抛出错误，避免影响确认收货流程\n        }\n    }\n\n    /**\n     * 建立推广员关系：下单人成为推广员的下级（仅在没有上级时）\n     * 注意：关系建立与佣金分配是分开的，佣金始终给当前分享者\n     * @param {number} buyerUserId 购买者用户ID\n     * @param {number} promoterUserId 推广员用户ID\n     */\n    async establishPromoterRelation(buyerUserId, promoterUserId) {\n        try {\n            console.log('=== 检查并建立推广员关系 ===');\n            console.log('购买者ID:', buyerUserId, '当前分享者ID:', promoterUserId);\n\n            // 检查购买者是否已有推广员关系\n            const existingPromoter = await this.model('personal_promoters').where({\n                user_id: buyerUserId\n            }).find();\n\n            if (!think.isEmpty(existingPromoter)) {\n                if (existingPromoter.parent_user_id) {\n                    console.log('购买者已有上级推广员:', existingPromoter.parent_user_id, '保持现有关系');\n                    console.log('注意：佣金仍然给当前分享者:', promoterUserId, '与购买者的上级关系无关');\n                    return;\n                } else {\n                    // 已是推广员但没有上级，建立关系\n                    await this.model('personal_promoters').where({\n                        user_id: buyerUserId\n                    }).update({\n                        parent_user_id: promoterUserId,\n                        update_time: parseInt(new Date().getTime() / 1000)\n                    });\n                    console.log('✅ 为现有推广员建立上级关系');\n                }\n            } else {\n                // 购买者不是推广员，创建推广员记录并建立关系\n                const currentTime = parseInt(new Date().getTime() / 1000);\n                const promoterData = {\n                    user_id: buyerUserId,\n                    parent_user_id: promoterUserId,\n                    level: 1,\n                    total_views: 0,\n                    total_orders: 0,\n                    total_commission: 0.00,\n                    month_views: 0,\n                    month_orders: 0,\n                    month_commission: 0.00,\n                    status: 1,\n                    first_share_time: currentTime,\n                    create_time: currentTime,\n                    update_time: currentTime\n                };\n\n                await this.model('personal_promoters').add(promoterData);\n                console.log('✅ 创建推广员记录并建立上级关系');\n            }\n\n        } catch (error) {\n            console.error('建立推广员关系失败:', error);\n        }\n    }\n\n\n\n    /**\n     * 记录分享有礼佣金（等确认收货后发放）\n     * @param {number} promoterUserId 推广员用户ID\n     * @param {number} orderId 订单ID\n     * @param {array} goodsList 商品列表\n     * @param {number} parentPromoterUserId 上级推广员用户ID\n     */\n    async recordShareCommissionRewards(promoterUserId, orderId, goodsList, parentPromoterUserId = null) {\n        try {\n            console.log('=== 开始记录分享有礼佣金 ===');\n            console.log('推广员用户ID:', promoterUserId, '订单ID:', orderId);\n\n            const commissionService = this.service('points');\n\n            // 处理每个商品的分享佣金记录\n            for (const goods of goodsList) {\n                const orderAmount = goods.goods_price * goods.number;\n\n                console.log(`记录商品: ${goods.goods_name}, 金额: ${orderAmount}元`);\n\n                // 使用支持上级推广员的佣金记录方法\n                const result = await commissionService.addShareCommissionWithParent(\n                    promoterUserId,\n                    orderId,\n                    goods.goods_id,\n                    orderAmount,\n                    parentPromoterUserId,\n                    'miniprogram'\n                );\n\n                if (result.success) {\n                    console.log(`✅ 商品 ${goods.goods_name} 分享佣金记录成功: ${result.commission}元`);\n                } else {\n                    console.log(`❌ 商品 ${goods.goods_name} 分享佣金记录失败: ${result.message}`);\n                }\n            }\n\n            console.log('=== 分享有礼佣金记录完成 ===');\n\n        } catch (error) {\n            console.error('记录分享有礼佣金失败:', error);\n            // 不抛出错误，避免影响下单流程\n        }\n    }\n    /**\n     * 查询物流信息asd\n     * @returns {Promise.<void>}\n     */\n    async expressAction() {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const orderId = this.get('orderId');\n        let info = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        if (think.isEmpty(info)) {\n            return this.fail(400, '暂无物流信息');\n        }\n        const expressInfo = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        // 如果is_finish == 1；或者 updateTime 小于 1分钟，\n        let updateTime = info.update_time;\n        let com = (currentTime - updateTime) / 60;\n        let is_finish = info.is_finish;\n        if (is_finish == 1) {\n            return this.success(expressInfo);\n        } else if (updateTime != 0 && com < 20) {\n            return this.success(expressInfo);\n        } else {\n            let shipperCode = expressInfo.shipper_code;\n            let expressNo = expressInfo.logistic_code;\n            let lastExpressInfo = await this.getExpressInfo(shipperCode, expressNo);\n            let deliverystatus = lastExpressInfo.deliverystatus;\n            let newUpdateTime = lastExpressInfo.updateTime;\n            newUpdateTime = parseInt(new Date(newUpdateTime).getTime() / 1000);\n            deliverystatus = await this.getDeliverystatus(deliverystatus);\n            let issign = lastExpressInfo.issign;\n            let traces = lastExpressInfo.list;\n            traces = JSON.stringify(traces);\n            let dataInfo = {\n                express_status: deliverystatus,\n                is_finish: issign,\n                traces: traces,\n                update_time: newUpdateTime\n            }\n            await this.model('order_express').where({\n                order_id: orderId\n            }).update(dataInfo);\n            let express = await this.model('order_express').where({\n                order_id: orderId\n            }).find();\n            return this.success(express);\n        }\n        // return this.success(latestExpressInfo);\n    }\n    async getExpressInfo(shipperCode, expressNo) {\n\t\tlet appCode = \"APPCODE \"+ think.config('aliexpress.appcode');\n        const options = {\n            method: 'GET',\n            url: 'http://wuliu.market.alicloudapi.com/kdi?no=' + expressNo + '&type=' + shipperCode,\n            headers: {\n                \"Content-Type\": \"application/json; charset=utf-8\",\n                \"Authorization\": appCode\n            }\n        };\n        let sessionData = await rp(options);\n        sessionData = JSON.parse(sessionData);\n        return sessionData.result;\n    }\n    async getDeliverystatus(status) {\n        if (status == 0) {\n            return '快递收件(揽件)';\n        } else if (status == 1) {\n            return '在途中';\n        } else if (status == 2) {\n            return '正在派件';\n        } else if (status == 3) {\n            return '已签收';\n        } else if (status == 4) {\n            return '派送失败(无法联系到收件人或客户要求择日派送，地址不详或手机号不清)';\n        } else if (status == 5) {\n            return '疑难件(收件人拒绝签收，地址有误或不能送达派送区域，收费等原因无法正常派送)';\n        } else if (status == 6) {\n            return '退件签收';\n        }\n    }\n\n    /**\n     * 验证并使用优惠券\n     */\n    async validateAndUseCoupon(userId, userCouponId, orderAmount) {\n        try {\n            const userCoupon = await this.model('user_coupons').alias('uc')\n                .join('coupons c ON uc.coupon_id = c.id')\n                .where({\n                    'uc.id': userCouponId,\n                    'uc.user_id': userId,\n                    'uc.status': 'unused',\n                    'uc.expire_at': ['>=', new Date()],\n                    'c.is_delete': 0\n                })\n                .field('uc.*, c.*')\n                .find();\n\n            if (think.isEmpty(userCoupon)) {\n                return { success: false, message: '优惠券不可用' };\n            }\n\n            // 验证使用条件\n            if (orderAmount < userCoupon.min_amount) {\n                return { success: false, message: `订单金额需满${userCoupon.min_amount}元` };\n            }\n\n            // 计算优惠金额\n            const discountAmount = this.calculateDiscount(userCoupon, orderAmount);\n\n            return {\n                success: true,\n                userCoupon: userCoupon,\n                discountAmount: discountAmount\n            };\n        } catch (error) {\n            think.logger.error('验证优惠券失败:', error);\n            return { success: false, message: '优惠券验证失败' };\n        }\n    }\n\n    /**\n     * 自动选择最优优惠券\n     */\n    async autoSelectBestCoupon(userId, orderAmount) {\n        try {\n            const availableCoupons = await this.model('user_coupons').alias('uc')\n                .join('coupons c ON uc.coupon_id = c.id')\n                .where({\n                    'uc.user_id': userId,\n                    'uc.status': 'unused',\n                    'uc.expire_at': ['>=', new Date()],\n                    'c.min_amount': ['<=', orderAmount],\n                    'c.is_delete': 0\n                })\n                .field('uc.*, c.*')\n                .select();\n\n            if (availableCoupons.length === 0) {\n                return { success: true, userCoupon: null, discountAmount: 0 };\n            }\n\n            let bestCoupon = null;\n            let maxDiscount = 0;\n\n            for (let coupon of availableCoupons) {\n                const discount = this.calculateDiscount(coupon, orderAmount);\n                if (discount > maxDiscount) {\n                    maxDiscount = discount;\n                    bestCoupon = coupon;\n                }\n            }\n\n            return {\n                success: true,\n                userCoupon: bestCoupon,\n                discountAmount: maxDiscount\n            };\n        } catch (error) {\n            think.logger.error('自动选择优惠券失败:', error);\n            return { success: false, message: '自动选择优惠券失败' };\n        }\n    }\n\n    /**\n     * 计算优惠金额\n     */\n    calculateDiscount(coupon, amount) {\n        if (coupon.discount_type === 'fixed') {\n            return Math.min(coupon.discount_value, amount);\n        } else {\n            const discount = amount * (coupon.discount_value / 100);\n            return Math.min(discount, coupon.max_discount || discount);\n        }\n    }\n\n    /**\n     * 标记优惠券为已使用并记录日志\n     */\n    async markCouponAsUsed(userCoupon, orderId, originalAmount, discountAmount, finalAmount) {\n        try {\n            // 更新用户优惠券状态\n            await this.model('user_coupons').where({id: userCoupon.id}).update({\n                status: 'used',\n                used_at: new Date(),\n                order_id: orderId\n            });\n\n            // 记录使用日志\n            await this.model('coupon_usage_logs').add({\n                user_id: userCoupon.user_id,\n                coupon_id: userCoupon.coupon_id,\n                user_coupon_id: userCoupon.id,\n                order_id: orderId,\n                original_amount: originalAmount,\n                discount_amount: discountAmount,\n                final_amount: finalAmount\n            });\n\n            console.log('优惠券使用成功:', userCoupon.coupon_code, '优惠金额:', discountAmount);\n        } catch (error) {\n            think.logger.error('标记优惠券使用失败:', error);\n            throw error;\n        }\n    }\n};"]}