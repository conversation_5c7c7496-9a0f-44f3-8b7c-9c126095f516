-- 插入测试秒杀数据
-- 执行此SQL文件来创建测试用的秒杀数据

USE hiolabsDB;

-- 1. 插入秒杀时间段数据
INSERT INTO `hiolabs_flash_sale_time_slots` (`name`, `start_time`, `end_time`, `sort_order`, `is_active`) VALUES
('上午场', '10:00:00', '12:00:00', 1, 1),
('下午场', '14:00:00', '16:00:00', 2, 1),
('晚上场', '20:00:00', '22:00:00', 3, 1);

-- 2. 插入秒杀轮次数据（假设商品ID 1-3 存在）
INSERT INTO `hiolabs_flash_sale_rounds` (
  `round_number`, `goods_id`, `goods_name`, `goods_image`, `original_price`, `flash_price`,
  `stock`, `sold_count`, `limit_quantity`, `start_time`, `end_time`, `status`
) VALUES 
-- 当前进行中的轮次
(1, 1, 'iPhone 15 Pro Max 256GB', '/images/goods/iphone15pro.jpg', 8999.00, 6999.00, 10, 3, 1, 
 NOW(), DATE_ADD(NOW(), INTERVAL 2 HOUR), 'active'),
 
(2, 2, '小米14 Ultra 512GB', '/images/goods/mi14ultra.jpg', 5999.00, 4999.00, 15, 8, 2, 
 NOW(), DATE_ADD(NOW(), INTERVAL 2 HOUR), 'active'),
 
(3, 3, 'MacBook Pro 14寸 M3', '/images/goods/macbookpro.jpg', 15999.00, 13999.00, 5, 2, 1, 
 NOW(), DATE_ADD(NOW(), INTERVAL 2 HOUR), 'active'),

-- 即将开始的轮次
(4, 1, 'iPhone 15 Pro Max 256GB', '/images/goods/iphone15pro.jpg', 8999.00, 7299.00, 8, 0, 1, 
 DATE_ADD(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 5 HOUR), 'upcoming'),
 
(5, 2, '小米14 Ultra 512GB', '/images/goods/mi14ultra.jpg', 5999.00, 5299.00, 12, 0, 2, 
 DATE_ADD(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 5 HOUR), 'upcoming');

-- 3. 如果商品表中没有对应商品，插入一些测试商品数据
INSERT IGNORE INTO `hiolabs_goods` (
  `id`, `category_id`, `is_on_sale`, `name`, `goods_number`, `sell_volume`, 
  `retail_price`, `min_retail_price`, `cost_price`, `min_cost_price`, 
  `goods_brief`, `goods_unit`, `list_pic_url`, `add_time`
) VALUES 
(1, 1, 1, 'iPhone 15 Pro Max 256GB', 50, 128, '8999.00', 8999.00, '7500.00', 7500.00, 
 '苹果最新旗舰手机，搭载A17 Pro芯片', '台', '/images/goods/iphone15pro.jpg', UNIX_TIMESTAMP()),
 
(2, 1, 1, '小米14 Ultra 512GB', 30, 89, '5999.00', 5999.00, '4500.00', 4500.00, 
 '小米年度旗舰，徕卡影像系统', '台', '/images/goods/mi14ultra.jpg', UNIX_TIMESTAMP()),
 
(3, 2, 1, 'MacBook Pro 14寸 M3', 20, 45, '15999.00', 15999.00, '12000.00', 12000.00, 
 '苹果专业级笔记本电脑，M3芯片', '台', '/images/goods/macbookpro.jpg', UNIX_TIMESTAMP());

-- 4. 插入商品分类数据（如果不存在）
INSERT IGNORE INTO `hiolabs_category` (`id`, `name`, `parent_id`, `sort_order`, `is_show`) VALUES 
(1, '手机数码', 0, 1, 1),
(2, '电脑办公', 0, 2, 1);

-- 5. 插入商品规格数据（product表）
INSERT IGNORE INTO `hiolabs_product` (
  `goods_id`, `goods_specification_ids`, `goods_sn`, `goods_number`, 
  `retail_price`, `cost`, `goods_name`, `is_on_sale`
) VALUES 
(1, '', 'IP15PM256', 50, 8999.00, 7500.00, 'iPhone 15 Pro Max 256GB', 1),
(2, '', 'MI14U512', 30, 5999.00, 4500.00, '小米14 Ultra 512GB', 1),
(3, '', 'MBP14M3', 20, 15999.00, 12000.00, 'MacBook Pro 14寸 M3', 1);

SELECT '✅ 测试秒杀数据插入完成！' as result;

-- 查询验证数据
SELECT '=== 秒杀时间段 ===' as info;
SELECT * FROM `hiolabs_flash_sale_time_slots`;

SELECT '=== 秒杀轮次 ===' as info;
SELECT * FROM `hiolabs_flash_sale_rounds`;

SELECT '=== 商品信息 ===' as info;
SELECT id, name, retail_price, list_pic_url FROM `hiolabs_goods` WHERE id IN (1,2,3);
