{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue?vue&type=style&index=0&id=da052a4e&scoped=true&lang=css&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\PointsGoodsPage.vue", "mtime": 1754302540662}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wb2ludHMtZ29vZHMtcGFnZSB7CiAgbWluLWhlaWdodDogMTAwdmg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZmFmYjsKfQoKLnBhZ2UtaGVhZGVyIHsKICBwb3NpdGlvbjogc3RpY2t5OwogIHRvcDogMDsKICB6LWluZGV4OiAxMDsKfQoKLyog5ZON5bqU5byP6LCD5pW0ICovCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogIC5ncmlkLWNvbHMtMS5tZFxcOmdyaWQtY29scy00IHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIG1pbm1heCgwLCAxZnIpKTsKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA2NDBweCkgewogIC5ncmlkLWNvbHMtMS5tZFxcOmdyaWQtY29scy00IHsKICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDEsIG1pbm1heCgwLCAxZnIpKTsKICB9Cn0K"}, {"version": 3, "sources": ["PointsGoodsPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6tBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "PointsGoodsPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"points-goods-page\">\n    <!-- 页面头部 -->\n    <div class=\"page-header bg-white shadow-sm border-b\">\n      <div class=\"px-6 py-4\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h1 class=\"text-2xl font-semibold text-gray-900\">积分兑好礼</h1>\n            <p class=\"text-sm text-gray-500 mt-1\">管理积分商城商品，设置积分价格和库存</p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <button \n              @click=\"showAddModal = true\" \n              class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i class=\"ri-add-line mr-2\"></i>\n              添加积分商品\n            </button>\n            <button \n              @click=\"refreshData\" \n              class=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              <i class=\"ri-refresh-line mr-2\"></i>\n              刷新\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"p-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"p-3 rounded-full bg-blue-100\">\n              <i class=\"ri-gift-line text-2xl text-blue-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm font-medium text-gray-500\">总商品数</p>\n              <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.totalGoods || 0 }}</p>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"p-3 rounded-full bg-green-100\">\n              <i class=\"ri-shopping-cart-line text-2xl text-green-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm font-medium text-gray-500\">上架商品</p>\n              <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.activeGoods || 0 }}</p>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"p-3 rounded-full bg-yellow-100\">\n              <i class=\"ri-exchange-line text-2xl text-yellow-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm font-medium text-gray-500\">总兑换次数</p>\n              <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.totalExchanges || 0 }}</p>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"p-3 rounded-full bg-purple-100\">\n              <i class=\"ri-coin-line text-2xl text-purple-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm font-medium text-gray-500\">消耗积分</p>\n              <p class=\"text-2xl font-semibold text-gray-900\">{{ statistics.totalPoints || 0 }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 筛选和搜索 -->\n      <div class=\"bg-white rounded-lg shadow-sm border p-4 mb-6\">\n        <div class=\"flex items-center justify-between\">\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex items-center space-x-2\">\n              <label class=\"text-sm font-medium text-gray-700\">状态:</label>\n              <select \n                v-model=\"filters.status\" \n                @change=\"loadPointsGoods\"\n                class=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n              >\n                <option value=\"\">全部</option>\n                <option value=\"1\">上架</option>\n                <option value=\"0\">下架</option>\n              </select>\n            </div>\n            \n            <div class=\"flex items-center space-x-2\">\n              <label class=\"text-sm font-medium text-gray-700\">分类:</label>\n              <select \n                v-model=\"filters.category_id\" \n                @change=\"loadPointsGoods\"\n                class=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\n              >\n                <option value=\"\">全部分类</option>\n                <option v-for=\"category in categories\" :key=\"category.id\" :value=\"category.id\">\n                  {{ category.name }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"flex items-center space-x-2\">\n            <input \n              v-model=\"filters.keyword\" \n              @keyup.enter=\"loadPointsGoods\"\n              placeholder=\"搜索商品名称...\" \n              class=\"border border-gray-300 rounded-md px-3 py-2 text-sm w-64\"\n            />\n            <button \n              @click=\"loadPointsGoods\" \n              class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm\"\n            >\n              搜索\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 商品列表 -->\n      <div class=\"bg-white rounded-lg shadow-sm border\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  商品信息\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  积分价格\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  库存/销量\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  状态\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  排序\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n              <tr v-for=\"item in pointsGoodsList\" :key=\"item.id\" class=\"hover:bg-gray-50\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 h-16 w-16\">\n                      <img \n                        :src=\"item.goods_image || '/static/images/default-goods.png'\" \n                        :alt=\"item.goods_name\"\n                        class=\"h-16 w-16 rounded-lg object-cover\"\n                      />\n                    </div>\n                    <div class=\"ml-4\">\n                      <div class=\"text-sm font-medium text-gray-900 max-w-xs truncate\">\n                        {{ item.goods_name }}\n                      </div>\n                      <div class=\"text-sm text-gray-500\">\n                        ID: {{ item.goods_id }}\n                      </div>\n                      <div v-if=\"item.is_hot\" class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1\">\n                        热门\n                      </div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm font-medium text-red-600\">\n                    {{ item.points_price }} 积分\n                  </div>\n                  <div v-if=\"item.original_price > 0\" class=\"text-sm text-gray-500\">\n                    原价: ¥{{ item.original_price }}\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  <div>库存: {{ item.stock }}</div>\n                  <div>销量: {{ item.sold_count }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span \n                    :class=\"[\n                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                      item.status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    ]\"\n                  >\n                    {{ item.status === 1 ? '上架' : '下架' }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {{ item.sort }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                  <button \n                    @click=\"editPointsGoods(item)\" \n                    class=\"text-blue-600 hover:text-blue-900\"\n                  >\n                    编辑\n                  </button>\n                  <button \n                    @click=\"toggleStatus(item)\" \n                    :class=\"[\n                      'hover:opacity-75',\n                      item.status === 1 ? 'text-red-600' : 'text-green-600'\n                    ]\"\n                  >\n                    {{ item.status === 1 ? '下架' : '上架' }}\n                  </button>\n                  <button \n                    @click=\"deletePointsGoods(item)\" \n                    class=\"text-red-600 hover:text-red-900\"\n                  >\n                    删除\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n        \n        <!-- 分页 -->\n        <div v-if=\"pagination.total > 0\" class=\"px-6 py-4 border-t border-gray-200\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"text-sm text-gray-700\">\n              显示 {{ (pagination.page - 1) * pagination.limit + 1 }} 到 \n              {{ Math.min(pagination.page * pagination.limit, pagination.total) }} 条，\n              共 {{ pagination.total }} 条记录\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button \n                @click=\"changePage(pagination.page - 1)\"\n                :disabled=\"pagination.page <= 1\"\n                class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                上一页\n              </button>\n              <span class=\"px-3 py-1 text-sm\">\n                第 {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }} 页\n              </span>\n              <button \n                @click=\"changePage(pagination.page + 1)\"\n                :disabled=\"pagination.page >= Math.ceil(pagination.total / pagination.limit)\"\n                class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                下一页\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 空状态 -->\n        <div v-if=\"pointsGoodsList.length === 0 && !loading\" class=\"text-center py-12\">\n          <i class=\"ri-gift-line text-6xl text-gray-300 mb-4\"></i>\n          <p class=\"text-gray-500 text-lg mb-2\">暂无积分商品</p>\n          <p class=\"text-gray-400 text-sm\">点击\"添加积分商品\"开始配置</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n        <div class=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n        <span class=\"text-gray-700\">加载中...</span>\n      </div>\n    </div>\n\n    <!-- 添加/编辑商品模态框 -->\n    <div v-if=\"showAddModal || showEditModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n          <h3 class=\"text-lg font-medium text-gray-900\">\n            {{ showAddModal ? '添加积分商品' : '编辑积分商品' }}\n          </h3>\n        </div>\n        \n        <div class=\"px-6 py-4\">\n          <form @submit.prevent=\"savePointsGoods\">\n            <!-- 选择商品 (仅添加时显示) -->\n            <div v-if=\"showAddModal\" class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择商品</label>\n              <select \n                v-model=\"formData.goods_id\" \n                required\n                class=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n              >\n                <option value=\"\">请选择商品</option>\n                <option v-for=\"goods in availableGoods\" :key=\"goods.id\" :value=\"goods.id\">\n                  {{ goods.name }} (ID: {{ goods.id }})\n                </option>\n              </select>\n            </div>\n            \n            <!-- 积分价格 -->\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">积分价格</label>\n              <input \n                v-model.number=\"formData.points_price\" \n                type=\"number\" \n                min=\"1\"\n                required\n                class=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                placeholder=\"请输入积分价格\"\n              />\n            </div>\n            \n            <!-- 库存数量 -->\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">库存数量</label>\n              <input \n                v-model.number=\"formData.stock\" \n                type=\"number\" \n                min=\"0\"\n                required\n                class=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                placeholder=\"请输入库存数量\"\n              />\n            </div>\n            \n            <!-- 排序权重 -->\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">排序权重</label>\n              <input \n                v-model.number=\"formData.sort\" \n                type=\"number\" \n                min=\"0\"\n                class=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                placeholder=\"数值越大排序越靠前，默认为0\"\n              />\n            </div>\n            \n            <!-- 是否热门 -->\n            <div class=\"mb-4\">\n              <label class=\"flex items-center\">\n                <input \n                  v-model=\"formData.is_hot\" \n                  type=\"checkbox\" \n                  class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                />\n                <span class=\"ml-2 text-sm text-gray-700\">设为热门商品</span>\n              </label>\n            </div>\n            \n            <!-- 商品描述 -->\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">商品描述</label>\n              <textarea \n                v-model=\"formData.description\" \n                rows=\"3\"\n                class=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                placeholder=\"请输入商品描述（可选）\"\n              ></textarea>\n            </div>\n          </form>\n        </div>\n        \n        <div class=\"px-6 py-4 border-t border-gray-200 flex justify-end space-x-3\">\n          <button \n            @click=\"closeModal\" \n            class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n          >\n            取消\n          </button>\n          <button \n            @click=\"savePointsGoods\" \n            :disabled=\"saving\"\n            class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {{ saving ? '保存中...' : '保存' }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { pointsGoodsApi } from '@/api/points-goods'\n\nexport default {\n  name: 'PointsGoodsPage',\n  data() {\n    return {\n      loading: false,\n      saving: false,\n      showAddModal: false,\n      showEditModal: false,\n\n      // 统计数据\n      statistics: {\n        totalGoods: 0,\n        activeGoods: 0,\n        totalExchanges: 0,\n        totalPoints: 0\n      },\n\n      // 筛选条件\n      filters: {\n        status: '',\n        category_id: '',\n        keyword: ''\n      },\n\n      // 分页\n      pagination: {\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n\n      // 数据列表\n      pointsGoodsList: [],\n      categories: [],\n      availableGoods: [],\n\n      // 表单数据\n      formData: {\n        id: null,\n        goods_id: '',\n        points_price: '',\n        stock: '',\n        sort: 0,\n        is_hot: false,\n        description: ''\n      }\n    }\n  },\n\n  mounted() {\n    this.initPage()\n  },\n\n  methods: {\n    // 初始化页面\n    async initPage() {\n      await this.loadStatistics()\n      await this.loadCategories()\n      await this.loadPointsGoods()\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        const data = await pointsGoodsApi.getStatistics()\n        this.statistics = data\n      } catch (error) {\n        console.error('加载统计数据失败:', error)\n        // 使用模拟数据作为fallback\n        this.statistics = {\n          totalGoods: 25,\n          activeGoods: 18,\n          totalExchanges: 156,\n          totalPoints: 12580\n        }\n      }\n    },\n\n    // 加载分类列表\n    async loadCategories() {\n      try {\n        const data = await pointsGoodsApi.getCategories()\n        this.categories = data\n      } catch (error) {\n        console.error('加载分类失败:', error)\n        // 使用模拟数据作为fallback\n        this.categories = [\n          { id: 1, name: '数码产品' },\n          { id: 2, name: '生活用品' },\n          { id: 3, name: '美食饮品' },\n          { id: 4, name: '服装配饰' }\n        ]\n      }\n    },\n\n    // 加载积分商品列表\n    async loadPointsGoods() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.pagination.page,\n          limit: this.pagination.limit,\n          ...this.filters\n        }\n\n        const data = await pointsGoodsApi.getList(params)\n        this.pointsGoodsList = data.list || data\n        this.pagination.total = data.total || 0\n\n      } catch (error) {\n        console.error('加载积分商品列表失败:', error)\n        this.$message?.error('加载数据失败')\n\n        // 使用模拟数据作为fallback\n        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟加载延迟\n\n        this.pointsGoodsList = [\n          {\n            id: 1,\n            goods_id: 101,\n            goods_name: '精美保温水杯',\n            goods_image: 'https://picsum.photos/200/200?random=1',\n            points_price: 500,\n            original_price: 89.00,\n            stock: 100,\n            sold_count: 25,\n            status: 1,\n            sort: 10,\n            is_hot: true,\n            description: '高品质保温水杯，保温效果佳'\n          },\n          {\n            id: 2,\n            goods_id: 102,\n            goods_name: '无线蓝牙耳机',\n            goods_image: 'https://picsum.photos/200/200?random=2',\n            points_price: 1200,\n            original_price: 199.00,\n            stock: 50,\n            sold_count: 18,\n            status: 1,\n            sort: 8,\n            is_hot: true,\n            description: '高音质无线蓝牙耳机'\n          },\n          {\n            id: 3,\n            goods_id: 103,\n            goods_name: '多功能手机支架',\n            goods_image: 'https://picsum.photos/200/200?random=3',\n            points_price: 300,\n            original_price: 39.00,\n            stock: 200,\n            sold_count: 45,\n            status: 1,\n            sort: 5,\n            is_hot: false,\n            description: '可调节角度的手机支架'\n          }\n        ]\n\n        this.pagination.total = 3\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载可选商品列表\n    async loadAvailableGoods() {\n      try {\n        const data = await pointsGoodsApi.getAvailableGoods()\n        this.availableGoods = data\n      } catch (error) {\n        console.error('加载可选商品失败:', error)\n        // 使用模拟数据作为fallback\n        this.availableGoods = [\n          { id: 104, name: '智能手环' },\n          { id: 105, name: '充电宝' },\n          { id: 106, name: '蓝牙音箱' },\n          { id: 107, name: '数据线' }\n        ]\n      }\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.pagination.page = 1\n      this.loadPointsGoods()\n      this.loadStatistics()\n    },\n\n    // 分页切换\n    changePage(page) {\n      if (page < 1 || page > Math.ceil(this.pagination.total / this.pagination.limit)) {\n        return\n      }\n      this.pagination.page = page\n      this.loadPointsGoods()\n    },\n\n    // 编辑积分商品\n    editPointsGoods(item) {\n      this.formData = {\n        id: item.id,\n        goods_id: item.goods_id,\n        points_price: item.points_price,\n        stock: item.stock,\n        sort: item.sort,\n        is_hot: item.is_hot,\n        description: item.description || ''\n      }\n      this.showEditModal = true\n    },\n\n    // 切换状态\n    async toggleStatus(item) {\n      try {\n        const newStatus = item.status === 1 ? 0 : 1\n        const action = newStatus === 1 ? '上架' : '下架'\n\n        if (!confirm(`确定要${action}这个商品吗？`)) {\n          return\n        }\n\n        await pointsGoodsApi.update(item.id, { status: newStatus })\n\n        item.status = newStatus\n        alert(`${action}成功`)\n\n      } catch (error) {\n        console.error('切换状态失败:', error)\n        alert('操作失败')\n      }\n    },\n\n    // 删除积分商品\n    async deletePointsGoods(item) {\n      if (!confirm('确定要删除这个积分商品吗？此操作不可恢复！')) {\n        return\n      }\n\n      try {\n        await pointsGoodsApi.delete(item.id)\n\n        // 从列表中移除\n        const index = this.pointsGoodsList.findIndex(goods => goods.id === item.id)\n        if (index > -1) {\n          this.pointsGoodsList.splice(index, 1)\n          this.pagination.total--\n        }\n\n        alert('删除成功')\n\n      } catch (error) {\n        console.error('删除失败:', error)\n        alert('删除失败')\n      }\n    },\n\n    // 保存积分商品\n    async savePointsGoods() {\n      if (!this.validateForm()) {\n        return\n      }\n\n      this.saving = true\n      try {\n        if (this.showAddModal) {\n          // 添加商品\n          await pointsGoodsApi.add(this.formData)\n\n          alert('添加成功')\n          this.closeModal()\n          this.refreshData()\n\n        } else {\n          // 更新商品\n          await pointsGoodsApi.update(this.formData.id, this.formData)\n\n          alert('更新成功')\n          this.closeModal()\n          this.loadPointsGoods()\n        }\n\n      } catch (error) {\n        console.error('保存失败:', error)\n        alert('保存失败')\n      } finally {\n        this.saving = false\n      }\n    },\n\n    // 表单验证\n    validateForm() {\n      if (this.showAddModal && !this.formData.goods_id) {\n        alert('请选择商品')\n        return false\n      }\n\n      if (!this.formData.points_price || this.formData.points_price <= 0) {\n        alert('请输入有效的积分价格')\n        return false\n      }\n\n      if (this.formData.stock < 0) {\n        alert('库存数量不能为负数')\n        return false\n      }\n\n      return true\n    },\n\n    // 关闭模态框\n    closeModal() {\n      this.showAddModal = false\n      this.showEditModal = false\n      this.formData = {\n        id: null,\n        goods_id: '',\n        points_price: '',\n        stock: '',\n        sort: 0,\n        is_hot: false,\n        description: ''\n      }\n    }\n  },\n\n  watch: {\n    // 监听添加模态框显示状态\n    showAddModal(newVal) {\n      if (newVal) {\n        this.loadAvailableGoods()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.points-goods-page {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.page-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .grid-cols-1.md\\\\:grid-cols-4 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n\n@media (max-width: 640px) {\n  .grid-cols-1.md\\\\:grid-cols-4 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n}\n</style>\n"]}]}