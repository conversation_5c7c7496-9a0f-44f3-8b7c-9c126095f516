# 积分兑好礼系统

## 系统概述

积分兑好礼系统是一个完整的积分商城解决方案，允许管理员从现有商品库中选择商品加入积分兑换池，为每个商品规格设置积分价格和现金价格，用户可以使用积分或积分+现金的方式兑换商品。

## 核心功能

### 后台管理功能
1. **商品选择**: 从现有商品库中选择商品加入积分商城
2. **规格管理**: 支持选择商品的特定规格或全部规格
3. **价格设置**: 为每个规格设置积分价格和可选的现金价格
4. **库存管理**: 支持设置积分商品的专属库存限制
5. **限购设置**: 支持每日限购和用户总限购设置
6. **状态管理**: 支持商品上架/下架操作
7. **统计分析**: 提供积分商品和兑换订单的统计数据

### 前端展示功能
1. **商品展示**: 在小程序中展示积分商品列表
2. **商品详情**: 显示商品详细信息和兑换规则
3. **积分兑换**: 支持纯积分或积分+现金兑换
4. **订单管理**: 积分兑换订单的创建和管理

## 技术架构

### 数据库设计
- `hiolabs_points_goods`: 积分商品主表，关联商品和规格
- `hiolabs_points_orders`: 积分兑换订单表
- `hiolabs_points_user_limits`: 用户兑换限制表

### 后端API (ThinkJS)
- **管理端API**: `/admin/points-goods/*` - 商品管理相关接口
- **前端API**: `/api/points-goods/*` - 小程序展示相关接口

### 前端界面
- **Web管理界面**: Vue.js + Tailwind CSS
- **小程序界面**: 原生微信小程序开发

## 安装和配置

### 1. 数据库初始化
```bash
# 执行数据库脚本
mysql -u username -p database_name < service/database/points_goods_redesign.sql
```

### 2. 后端配置
确保ThinkJS服务正常运行，积分商品相关的控制器已部署：
- `service/src/admin/controller/points-goods.js`
- `service/src/api/controller/points-goods.js`

### 3. 前端配置
确保Web管理界面和小程序前端已部署：
- Web管理页面: `web/src/components/Marketing/PointsGoodsPage.vue`
- 小程序页面: `pages/points-mall/*`

## 使用指南

### 管理员操作流程

1. **登录后台管理系统**
   - 访问: `http://your-domain/admin`
   - 导航到: 营销工具 > 积分兑好礼

2. **添加积分商品**
   - 点击"添加积分商品"按钮
   - 从商品库中选择要加入积分商城的商品
   - 选择具体的商品规格
   - 设置积分价格（必填）
   - 设置现金价格（可选，支持积分+现金模式）
   - 配置库存限制、限购规则等
   - 保存并上架

3. **管理积分商品**
   - 查看商品列表和统计数据
   - 编辑商品信息和价格
   - 上架/下架商品
   - 删除不需要的商品

### 用户使用流程

1. **浏览积分商城**
   - 在小程序中进入积分商城页面
   - 浏览可兑换的积分商品

2. **兑换商品**
   - 选择心仪的商品
   - 查看兑换规则和价格
   - 确认兑换（使用积分或积分+现金）
   - 填写收货信息
   - 完成兑换

## API接口文档

### 管理端接口

#### 获取积分商品列表
```
GET /admin/points-goods/list
参数: page, limit, keyword, status
返回: 积分商品列表（包含商品信息、规格信息、价格等）
```

#### 获取可选商品
```
GET /admin/points-goods/available-goods
参数: page, limit, keyword, category_id
返回: 可添加到积分商城的商品列表
```

#### 添加积分商品
```
POST /admin/points-goods/add
参数: goods_id, product_id, points_price, cash_price, stock_limit等
返回: 操作结果
```

#### 更新积分商品
```
POST /admin/points-goods/update
参数: id, points_price, cash_price, status等
返回: 操作结果
```

### 前端接口

#### 获取积分商品列表
```
GET /api/points-goods/list
参数: page, limit, category_id
返回: 前端展示用的积分商品列表
```

#### 获取积分商品详情
```
GET /api/points-goods/detail
参数: id
返回: 商品详细信息
```

## 系统特点

### 1. 与现有商品系统深度集成
- 直接使用现有的商品库和规格系统
- 避免数据重复，保持数据一致性
- 支持商品信息的实时同步

### 2. 灵活的定价策略
- 支持纯积分兑换
- 支持积分+现金混合兑换
- 每个规格可独立定价

### 3. 完善的库存和限购管理
- 支持积分商品专属库存
- 支持每日限购和用户总限购
- 实时库存检查和扣减

### 4. 用户友好的管理界面
- 直观的商品选择界面
- 实时的统计数据展示
- 便捷的批量操作功能

## 测试和验证

### 运行测试脚本
```bash
node test_points_goods_system.js
```

### 手动测试步骤
1. 访问管理后台，检查积分兑好礼菜单是否显示
2. 尝试添加积分商品，验证商品选择和价格设置功能
3. 检查商品列表显示是否正常
4. 在小程序中验证积分商城页面显示

## 故障排除

### 常见问题

1. **菜单不显示**
   - 检查路由配置是否正确
   - 确认菜单配置已更新

2. **API调用失败**
   - 检查后端服务是否正常运行
   - 验证数据库连接和表结构

3. **商品数据不显示**
   - 确认数据库中有商品数据
   - 检查JOIN查询是否正确执行

4. **前端页面报错**
   - 检查Vue组件是否正确导入
   - 验证API接口返回数据格式

## 技术支持

如遇到问题，请检查：
1. 数据库表是否正确创建
2. 后端API接口是否正常响应
3. 前端路由和组件是否正确配置
4. 小程序页面是否正确部署

## 版本信息

- 版本: 1.0.0
- 更新日期: 2025-01-04
- 兼容性: HioShop电商系统
- 技术栈: ThinkJS + Vue.js + 微信小程序
